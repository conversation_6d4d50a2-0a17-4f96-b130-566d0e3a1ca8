import { useSession, signOut } from "next-auth/react";
import Layout from "../components/layout";
import Link from "next/link";
import { useState, useEffect } from "react";
import axios from "axios";
import { usePermissions } from "../contexts/PermissionsContext";
import { createPermission<PERSON>he<PERSON> } from "../components/utils";
import RouteGuard from "../components/RouteGuard";

const Returns = () => {
  const { data: session, status } = useSession();
  const [returnForms, setReturnForms] = useState([]);
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);

  const syncReturns = async (user_id) => {
    try {
      const res = await fetch("/api/syncJDE/syncReturns", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({user_id}),
      });
      if (res.status === 200) {
        console.log("Returns Updated");
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error("An unexpected error while updating returns occurred:", error);
    }
  };

  useEffect(() => {
    const syncAndContinue = async () => {
      if (session) {
        if (!session.user.sync) {
          await syncReturns(session.user.id);
          update({ sync: new Date() });
          console.log("no sync");
        } else {
          console.log("sync", session);
        }

        try {
          const limit = 10;
          axios
          .get("/api/return/getAll", {
            params: {
              userId: session.user.id,
              groupId: session.user.group_id,
              locationId: session.user.location_id,
              accountId: session.user.account_id,
              limit,
            },
          })
          .then((response) => {
            console.log(response.data);
            setReturnForms(response.data);
          });
          console.log('Refreshed returns');
        } catch (error) {
          // Handle errors if any of the requests fail
          console.log("error", error);
        } 
      }
    };
    syncAndContinue();
  
  }, [session]);
  return (
    <Layout>
      <RouteGuard section="Returns">
      <div className="flex flex-row gap-4">
        <div className="basis-2/3">
          <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
            <p className="text-2xl font-semibold pt-5 pb-4">Returns</p>
            <hr className="h-px bg-gray-200 border-0 -mx-8" />
            <div className="py-8 text-sm">
              <p>
                Unopened items returned for credit must be accompanied by an RMA
                (return material authorization) and the reason for the
                return.
              </p>
              <p className="text-lg font-bold my-4">
                Merchandise that will not be accepted:
              </p>
              <ul className="list-disc ml-8 leading-6">
                <li>
                  &apos;Special Order&apos; or &apos;Custom Products&apos;
                </li>
                <li>Merchandise not in standard OASIS packaging</li>
                <li>Discontinued products</li>
              </ul>
              <p className="text-lg font-bold mt-8">
                Allowance Schedule for acceptable returns:
              </p>
              <div className="flex my-6">
                <div className="grow">
                  <p>
                    Saleable merchandise returned in factory condition within 60
                    days:
                  </p>
                </div>
                <div>
                  <p className="font-bold">Full Credit</p>
                </div>
              </div>
              <div className="flex my-6">
                <div className="grow">
                  <p>
                    Saleable merchandise returned in factory condition within 60
                    to 90 days:
                  </p>
                </div>
                <div>
                  <p className="font-bold">Cost less 40%</p>
                </div>
              </div>
              <div className="flex my-6">
                <div className="grow">
                  <p>
                    Saleable merchandise returned in factory 90 days to 1 year:
                  </p>
                </div>
                <div>
                  <p className="font-bold">Cost less 60%</p>
                </div>
              </div>
              <div className="flex my-6">
                <div className="grow">
                  <p>Merchandise returned one year after invoice date:</p>
                </div>
                <div>
                  <p className="font-bold">No Credit</p>
                </div>
              </div>
              <div className="flex my-6">
                <div className="grow">
                  <p>Merchandise unsuitable for resale:</p>
                </div>
                <div>
                  <p className="font-bold">No Credit</p>
                </div>
              </div>
              <div className="flex my-6">
                <div className="grow">
                  <p>Merchandise defective at time of reciept by customer:</p>
                </div>
                <div>
                  <p className="font-bold">Full Credit</p>
                </div>
              </div>
              <div className="flex my-6">
                <div className="grow">
                  <p>Merchandise shipped in error:</p>
                </div>
                <div>
                  <p className="font-bold">Full Credit</p>
                </div>
              </div>
              <div className="mt-24">
              {hasPermission('Returns', "add") && (
                <Link
                  className="bg-[#08447C] text-white rounded-full whitespace-nowrap py-5 px-8 hover:bg-primary-dark focus:ring-0 focus:ring-transparent"
                  href={{
                    pathname: "/return",
                  }}
                >
                  Start Return Process
                </Link>
                )}
              </div>
            </div>
          </div>
        </div>
        <div className="basis-1/3">
          <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
            <p className="text-2xl font-semibold pt-5 pb-4">Return History</p>
            <hr className="h-px bg-gray-200 border-0 -mx-8" />
            <div className="grid divide-y -mx-8">
              {returnForms.length < 1 ? (
                <p className="mt-4">No return history</p>
              ) : (
                returnForms.map((item) => {
                  return (
                    <div
                      className="grid grid-cols-2 gap-4 p-8 text-sm"
                      key={item.id}
                    >
                      <div>
                        <p className="font-semibold mb-1">RMA# {item.rma_num}</p>
                        <Link
                          className="text-[#08447C] whitespace-nowrap "
                          href={{
                            pathname: "/return",
                            query: {
                              id: item.id,
                            },
                          }}
                          replace
                        >
                          View Return
                        </Link>
                      </div>
                      <div>
                        {item.status === 1 && (
                          <p className="text-center font-semibold p-2 text-amber-500 bg-amber-100 rounded-md w-18 float-right">
                            Pending
                          </p>
                        )}
                        {item.status === 2 && (
                          <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md w-18 float-right">
                            Approved
                          </p>
                        )}
                        {item.status === 3 && (
                          <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md w-18 float-right">
                            Denied
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>
      </div>
      </RouteGuard>
    </Layout>
  );
};

export default Returns;
