import { useSession, signOut } from "next-auth/react";
import Layout from "../components/layout";
import { MdAddCircle, MdLockReset } from "react-icons/md";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tabs } from "flowbite-react";
import { useEffect, useState } from "react";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { BiPencil } from "react-icons/bi";
import { FaTrash } from "react-icons/fa";
import axios from "axios";
import AddUser from "../components/users/UsersAdd";
import EditUser from "../components/users/UsersEdit";
import Permissions from "../components/users/Permissions";
import { usePermissions } from "../contexts/PermissionsContext";
import { createPermissionChecker } from "../components/utils";
import RouteGuard from "../components/RouteGuard";

const Users = () => {
  const [users, setUsers] = useState([]);
  const [locations, setLocations] = useState([]);
  const [modal, setModal] = useState(false);
  const { data: session, status } = useSession();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);

  const handleUserAdd = () => {
    axios
      .get(`api/user/getByAccount`, {
        params: {
          groupId: session.user.group_id,
          locationId: session.user.location_id,
          accountId: session.user.account_id,
        },
      })
      .then((response) => {
        setUsers(response.data);
      });
  };

  useEffect(() => {
    if (session) {
      axios
        .get(`api/user/getByAccount`, {
          params: {
            groupId: session.user.group_id,
            locationId: session.user.location_id,
            accountId: session.user.account_id,
          },
        })
        .then((response) => {
          setUsers(response.data);
        });

      axios
        .get(`/api/location/getList`, {
          params: {
            groupId: session.user.group_id,
            locationId: session.user.location_id,
            accountId: session.user.account_id,
          },
        })
        .then((response) => {
          setLocations(response.data.formattedItems);
        });

      // const timer = setTimeout(() => {
      //   addNotifications({
      //     iconColor: {
      //       bg: "",
      //       text: "",
      //     },
      //     alertColor: "",
      //     alertMessage: "",
      //     alertShow: false,
      //   });
      // }, 7000);
      // return () => clearTimeout(timer);
      
    }
  }, [session]);

  // if (session) {
    return (
      <Layout>
        <RouteGuard section="Users">
        <div className="relative h-full overflow-y-auto bg-white py-6 rounded-xl dark:bg-gray-800">
          <Tabs.Group aria-label="Users Tabs" style="underline" className={`flex flex-nowrap custom-tab2 ${session && session.user.group_id !== 1 ? "nsa" : ""}`}>
            <Tabs.Item active title="Users">
              {/* Your original Users table content */}
              <div className="flex justify-end mb-4">
                  {hasPermission('Users', "add") && (
                    <>
                      <Button
                        color="#08447C"
                        outline={1}
                        onClick={() => setModal(true)}
                        className="absolute top-5"
                      >
                        <MdAddCircle className="mr-2 h-5 w-5 text-[#08447C]" />
                        Add New
                      </Button>

                      <Modal
                        show={modal}
                        onClose={() => setModal(false)}
                        size="4xl"
                        dismissible
                        className="bg-opacity-10"
                      >
                        <AddUser
                          setModal={setModal}
                          locations={locations}
                          session={session}
                          onUpdate={handleUserAdd}
                          alert={notifications}
                          setAlert={addNotifications}
                        />
                      </Modal>
                    </>
                  )}
                </div>
                <div className="px-6 lg:px-8 w-full overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400 ">

            <thead className="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" className="px-6 py-3">
                  Level
                </th>
                <th scope="col" className="px-6 py-3">
                  User
                </th>
                <th scope="col" className="px-6 py-3">
                  Email
                </th>
                <th scope="col" className="px-6 py-3">
                  Location
                </th>
                <th scope="col" className="px-6 py-3">
                  Phone
                </th>
                {hasPermission('Users', "edit") && (
                <th scope="col" className="px-6 py-3">
                  Edit
                </th>
                )}
                {hasPermission('Users', "delete") && (
                <th scope="col" className="px-6 py-3">
                  Delete
                </th>
                )}
                {session?.user.group_id === 1 && (

                <th scope="col" className="px-6 py-3">
                  Reset Password
                </th>
                )}
              </tr>
            </thead>
            <tbody>
              {users.map((item, index) => (
                <User
                  item={item}
                  key={index}
                  locations={locations}
                  session={session}
                  handleUserEdit={handleUserAdd}
                  alert={notifications}
                  setAlert={addNotifications}
                  hasPermission = {hasPermission}
                />
              ))}
            </tbody>
          </table>
          </div>
          </Tabs.Item>
          {session?.user.group_id === 1 ? (
          <Tabs.Item title="Group Permissions">
            <Permissions/>
          </Tabs.Item>
          ) : '' }
        </Tabs.Group>
        </div>
        </RouteGuard>
      </Layout>
    );
  // }
};

export default Users;

function User({ item, session, locations, handleUserEdit, alert, setAlert, hasPermission }) {
  // console.log(item);
  const [editModal, setEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleResetPassword = async () => {
    try {
      const response = await axios.post("/api/user/forgotPassword", {
        email: item.email,
      });
  
      if (response.status === 200) {
        setAlert({
          iconColor: {
            bg: "bg-green-100",
            text: "text-green-500",
          },
          bgColor: "bg-green-100",
          message: "Reset email sent successfully.",
          show: true,
        });
      }
    } catch (error) {
      setAlert({
        iconColor: {
          bg: "bg-red-100",
          text: "text-red-500",
        },
        bgColor: "bg-red-100",
        message: `Error sending reset email: ${error.response?.data?.message || error.message}`,
        show: true,
      });
    }
  };

  const handleDelete = async (user_id) => {
  
    try {
      const response = await axios.post(`/api/user/delete`, {
        id: user_id,
      });
  
      if (response.status === 200) {
        setAlert({
          iconColor: {
            bg: "bg-green-100",
            text: "text-green-500",
          },
          bgColor: "bg-green-100",
          message: "User deleted successfully.",
          show: true,
        });
        setShowDeleteModal(false);
        handleUserEdit();
      } else {
        setAlert({
          iconColor: {
            bg: "bg-red-100",
            text: "text-red-500",
          },
          bgColor: "bg-red-100",
          message: `Error deleting user.`,
          show: true,
        });
      }
    } catch (error) {
      console.error(error);
      setAlert({
        iconColor: {
          bg: "bg-red-100",
          text: "text-red-500",
        },
        bgColor: "bg-red-100",
        message: `Error deleting user: ${error.response?.data?.message || error.message}`,
        show: true,
      });
    }
  };
  

  return (
    <>
      <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
        <th
          scope="row"
          className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
        >
          {item.Group ? item.Group.name : ""}
        </th>
        <td className="px-6 py-4">
          {item.first_name} {item.last_name}
        </td>
        <td className="px-6 py-4">{item.email}</td>
        <td className="px-6 py-4">
          {item.Locations ? item.Locations.practice : (item.group_id == 4 ? "ALL" : "")}
        </td>
        <td className="px-6 py-4">
          {item.phone && (
            <>
              {`(${item.phone.slice(0, 3)}) `}
              {`${item.phone.slice(3, 6)}-`}
              {item.phone.slice(6)}
            </>
          )}
        </td>
        {hasPermission('Users', "edit") && (
        <td className="px-6 py-4 self-center">
          <BiPencil
            className="hover:text-blue-500 cursor-pointer"
            size="20px"
            onClick={() => {
              if(item.primary_user == 1 && item.id !== session.user.id){
                
                return setAlert({
                  iconColor: {
                    bg: "bg-red-100",
                    text: "text-red-500",
                  },
                  bgColor: "bg-red-100",
                  message: "Only the primary user can change this account",
                  show: true,
                  })
              }
              setEditModal(true)
            }}
          />
        </td>
        )}
        {hasPermission('Users', "delete") && (
        <td className="px-6 py-4 justify-items-center">
          <FaTrash
            className="hover:text-red-800 text-red-500 cursor-pointer"
            size="15px"
            onClick={() => {
              if((item.primary_user == 1 && item.id !== session.user.id) || (item.id == session.user.id)){
                
                return setAlert({
                  iconColor: {
                    bg: "bg-red-100",
                    text: "text-red-500",
                  },
                  bgColor: "bg-red-100",
                  message: "Not allowed to delete user",
                  show: true,
                  })
              }
              setShowDeleteModal(true)
            }}
          />
        </td>
        )}
        {session?.user.group_id === 1 && (

        <td className="px-6 py-4 justify-items-center">
          <MdLockReset
            className="hover:text-blue-500 cursor-pointer"
            size="20px"
            onClick={handleResetPassword}
          />
        </td>
        )}
      </tr>
      <Modal
        size="4xl"
        dismissible
        show={editModal}
        onClose={() => setEditModal(false)}
      >
        <EditUser
          userData={item}
          locations={locations}
          session={session}
          onUpdate={handleUserEdit}
          setEditModal={setEditModal}
          alert={alert}
          setAlert={setAlert}
        />
      </Modal>

      <Modal show={showDeleteModal} size="md" onClose={() => setShowDeleteModal(false)} popup>
        <Modal.Header/>
        <Modal.Body>
          <div className="text-center">
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete this user?
            </h3>
            <div className="flex justify-center gap-4">
              <Button className="secondary-button w-36" pill onClick={() => setShowDeleteModal(false)}>
                {"No"}
              </Button>
              <Button className="theme-button w-36" pill onClick={() => handleDelete(item.id)}>
                Yes, Delete
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
}
