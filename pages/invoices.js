import Layout from "../components/layout";
import { useDate } from "../contexts/DateContext";
import { useState, useEffect } from "react";
import axios from "axios";
import InvoiceTable from "../components/InvoiceTable";
import { useSession, signOut } from "next-auth/react";
import RouteGuard from "../components/RouteGuard";

const Invoices = () => {
  const { startDate, endDate, handleStartDateChange, handleEndDateChange } =
    useDate();
  const [invoices, setInvoices] = useState([]);
  // const [fetchingPOP, setFetchingPOP] = useState(false);
  // const [POPFetched, setPOPFetched] = useState(false);
  const { data: session, status, update } = useSession();

  // useEffect(() => {
  //   axios.get("/api/invoice/all").then((response) => {
  //     console.log(response.data);
  //     setInvoices(response.data);
  //   });
  // }, []);

  // const refreshPOP = async () => {
  //   setFetchingPOP(true);
  //   console.log("updating...");
  //   try {
  //     const res = await fetch("/api/syncJDE/runPostOrderProcess", {
  //       method: "POST",
  //       headers: { "Content-Type": "application/json" },
  //     });
  //     if (res.status === 200) {
  //       console.log("Post Order Process Updated");
  //       setFetchingPOP(false);
  //       setPOPFetched(true);
  //       update({ popSync: new Date() });
  //     } else {
  //       setFetchingPOP(false);
  //       throw new Error(await res.text());
  //     }
  //   } catch (error) {
  //     setFetchingPOP(false);
  //     alert(error.message);
  //     console.error(
  //       "An unexpected error while updating Orders occurred:",
  //       error
  //     );
  //   }
  // };

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        const response = await axios.get("/api/invoice/fetchRange", {
          params: {
            startDate,
            endDate,
            userId: session.user.id,
            groupId: session.user.group_id,
            locationId: session.user.location_id,
            accountId: session.user.account_id,
          },
        });

        if (isMounted) {
          setInvoices(response.data);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (startDate !== null && endDate !== null) {
      fetchData();
    }

    return () => {
      isMounted = false;
    };
  }, [startDate, endDate, session]);

    return (
      <Layout>
        <RouteGuard section="My Invoices">
        <div className="relative h-full overflow-x-auto overflow-y-auto bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
          <InvoiceTable
            data={invoices}
            // refreshPOP={refreshPOP}
            // fetchingPOP={fetchingPOP}
            // lastFetched={session.user.popSync}
          />
        </div>
        </RouteGuard>
      </Layout>
    );
};

export default Invoices;
