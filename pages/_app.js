import { useEffect } from "react";
import { useRouter } from "next/router";
import { DateProvider } from "../contexts/DateContext";
import { CartProvider } from "../contexts/CartContext";
import { UseUserAcctProvider } from "../contexts/UserAcctContext";
import { UseTrainingProvider } from "../contexts/TrainingContext";
import "../styles/globals.css";
import { SessionProvider } from "next-auth/react";
import { UseLocationsProvider } from "../contexts/LocationsContext";
import { UseProductDetailsProvider } from "../contexts/ProductDetailsContext";
import { UseGlobalAlertProvider } from "../contexts/GlobalAlertContext";
import { PromotionsProvider } from '../contexts/PromotionsContext';
import { PermissionsProvider } from "../contexts/PermissionsContext";

// import Wrapper from "../components/wrapper";
import Head from "next/head";
import * as gtag from "../gtag.js"


export default function App({
  Component,
  pageProps: { session, ...pageProps },
}) {
  const isProduction = process.env.NEXT_PUBLIC_ENVIRONMENT === 'production';
  // const gaTrackingId = process.env.NEXT_PUBLIC_GA_TRACKING_ID;
  const router = useRouter();

  // Track page views on route changes
  useEffect(() => {
    const handleRouteChange = (url) => {
      gtag.pageview(url)
    }

    router.events.on('routeChangeComplete', handleRouteChange)

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [router.events])


  return (
    <SessionProvider session={session}>
      <UseUserAcctProvider>
      <PermissionsProvider>
        <UseGlobalAlertProvider>
          <PromotionsProvider>
            <UseLocationsProvider>
              <CartProvider>
                <DateProvider>
                  <UseTrainingProvider>
                    <UseProductDetailsProvider>
                    {/* Conditionally include GA script in production */}
                    {isProduction && gtag.GA_TRACKING_ID && (
                        <Head>
                         <script
                            async
                            src={`https://www.googletagmanager.com/gtag/js?id=${gtag.GA_TRACKING_ID}`}
                          />
                          <script
                            dangerouslySetInnerHTML={{
                              __html: `
                                window.dataLayer = window.dataLayer || [];
                                function gtag(){dataLayer.push(arguments);}
                                gtag('js', new Date());
                                gtag('config', '${gtag.GA_TRACKING_ID}', {
                                  page_path: window.location.pathname,
                                });
                              `,
                            }}
                          />
                        </Head>
                      )}
                      <Component {...pageProps} />
                    </UseProductDetailsProvider>
                  </UseTrainingProvider>
                </DateProvider>
              </CartProvider>
            </UseLocationsProvider>
          </PromotionsProvider>
        </UseGlobalAlertProvider>
        </PermissionsProvider>
      </UseUserAcctProvider>
    </SessionProvider>
  );
}
