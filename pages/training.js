import { useSession, signOut } from "next-auth/react";
import Layout from "../components/layout";
import { useState, useEffect, useRef } from "react";
import axios from "axios";
import { useRouter } from 'next/router';
import { useTrainingContext } from "../contexts/TrainingContext";
import RouteGuard from "../components/RouteGuard";


const Training = () => {
  const [categories, setCategories] = useState([]);
  const [title, setTitle] = useState("");
  const [path, setPath] = useState("");
  const videoRef = useRef();
  const router = useRouter();

  const { training, setTraining } = useTrainingContext();

  useEffect(() => {
    axios.get("/api/training/get").then((response) => {
      console.log(response.data);
      setCategories(response.data);
    });
    setTitle(training.title);
    setPath(training.video_path);
  }, []);

  const handleAction = (title, file) => {
    console.log(title, file)
    setTitle(title);
    setPath(file);
    videoRef.current?.load();
  };
console.log("Training Context", training);
  return (
    <Layout>
      <RouteGuard section="Resources">
      <div className="relative h-screen overflow-x-auto overflow-y-auto bg-white rounded-xl max-h-full">
        <div className="font-semibold text-left p-8 border-b-2">
          <p className="text-2xl">Resources</p>
        </div>
        <div className="flex flex-row h-full gap-4">
          <div className="basis-1/6 bg-gray-0 border-r max-h-screen border-gray-200 ">
            {categories ? (
              categories.map((value, index) => (
                <Category data={value} key={index} action={handleAction} setTraining={setTraining} />
              ))
            ) : (
              <h1>no content</h1>
            )}
          </div>
          <div className="basis-5/6 bg-gray-0 h-full ">
            <div className="p-8">
              <p className="text-lg font-semibold">{title}</p>
              {/* <p className="text-xs text-gray-500">12 min</p> */}
              <div className="p-4">
                {title ? (
                  <div className="relative w-full h-0 aspect-w-16 aspect-h-9">
                    <iframe
                      allow="fullscreen"
                      src={`${path}`}
                      style={{
                        width: '100%',
                        height: '520px',
                        border: 'none',
                      }}
                    ></iframe>
                  </div>
                
                ) : (
                  <h1 className="">Select a video to get started.</h1>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
      </RouteGuard>
    </Layout>
  );
};

export default Training;

function Category({ data, action, setTraining }) {
  function selection(title, file) {
    action(title, file);
  }

  return (
    <div className="border-b border-gray-200 pb-7" >
      <div className="my-4 mx-4">
        <p className="text-xl font-normal">{data.title}</p>
        {/* <p className="text-xs font-medium text-gray-500">1/5 Completed</p> */}
      </div>
      <div className="mt-5">
        <div className="text-sm font-medium text-gray-900 bg-white">
          {data.TrainingMaterials.map((value, index) => (
            <div key={value.id} className="block w-full px-4 py-5 cursor-pointer hover:bg-gray-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700 focus:text-blue-700"
                onClick={() => {
                  setTraining({
                    title: value.title,
                    video_path: value.video_path
                  })
                  selection(value.title, value.video_path);
                }}
              >
                {value.title}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
