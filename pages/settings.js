"use client";
import { useSession } from "next-auth/react";
import { useState, useEffect } from "react";
import Layout from "../components/layout";
import { useRouter } from "next/router";
import { Card, Label, TextInput, Checkbox, Button, Tabs } from "flowbite-react";
import Select from "react-select";
import axios from "axios";
import { stateNames } from "../components/utils";

const Settings = () => {
  const { data: session, status, update } = useSession();

  const [userProfile, setUserProfile] = useState({
    email: "",
    firstName: "",
    lastName: "",
    title: "",
    extension: "",
    mainPhone: "",
    mainPhoneType: "",
    fax: "",
    address: {
      street: "",
      street2: "",
      city: "",
      zip: "",
      state: "",
    },
  });

  const [passwordChange, setPasswordChange] = useState({
    current_password: "",
    new_password: "",
    confirm_new_password: "",
  });
  const [passwordCheck, setPasswordCheck] = useState({
    show: false,
    message: "",
    pass: false,
  });

  const [settings, setSettings] = useState({
    invoice: {
      view: "",
      sort: "",
      new: {
        email: 0,
        badge: 0,
        sms: 0,
      },
      payment_recieved: {
        email: 0,
        badge: 0,
        sms: 0,
      },
      past_due: {
        email: 0,
        badge: 0,
        sms: 0,
      },
    },
    orders: {
      view: "",
      sort: "",
      new: {
        email: 0,
        badge: 0,
        sms: 0,
      },
      recieved: {
        email: 0,
        badge: 0,
        sms: 0,
      },
      shipped: {
        email: 0,
        badge: 0,
        sms: 0,
      },
    },
  });

  const router = useRouter();

  const phone_options = [
    { value: "Mobile", label: "Mobile" },
    { value: "Work", label: "Work" },
  ];
  const view_options = [
    { value: "all", label: "All" },
    { value: "paid", label: "Paid" },
  ];
  const sort_options = [
    { value: "latest", label: "Latest First" },
    { value: "unpaid", label: "Unpaid First" },
  ];

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setUserProfile({
      ...userProfile,
      mainPhone: cleanedInput,
    });
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  const updateSettings = () => {
    axios
      .put("api/settings/update", {
        id: session.user.id,
        userProfile: userProfile,
        settings: settings,
      })
      .then((response) => {
        alert(response.data.message);
      })
      .catch((error) => {
        alert("Profile could not be saved, please try again.");
      });
  };

  const changePassword = (event) => {
    event.preventDefault();
    const password_regex = /^(?=.*[A-Z])(?=.*\d).{8,}$/;

    const hashedPassword = session.user.password;
    const user_id = session.user.id;

    setPasswordCheck({
      show: false,
      message: "",
      pass: false,
    });
    const { current_password, new_password, confirm_new_password } =
      passwordChange;
    console.log(passwordChange);

    if (!password_regex.test(new_password)) {
      setPasswordCheck({
        show: true,
        message: "New password does not meet criteria",
        pass: false,
      });
      return;
    }
    if (new_password !== confirm_new_password) {
      setPasswordCheck({
        show: true,
        message: "Passwords do not match",
        pass: false,
      });
      return;
    }

    axios
      .put("api/settings/updatePassword", {
        plainPassword: current_password,
        hashedPassword: hashedPassword,
        newPassword: new_password,
        user_id: user_id,
      })
      .then((response) => {
        if (response.status == 200 && response.data.changed) {
          console.log("password response", response.data.hash);
          update({ password: response.data.hash });
          setPasswordCheck({
            show: true,
            message: response.data.message,
            pass: true,
          });
        } else {
          setPasswordCheck({
            show: true,
            message: response.data.message,
            pass: false,
          });
        }
      })
      .catch((error) => {
        setPasswordCheck({
          show: true,
          message: "Password could not be saved, please try again.",
          pass: false,
        });
        // update();
      });
  };

  useEffect(() => {
    if (status === "authenticated") {
      console.log(session.user.id);
      console.log(session);

      const user_id = session.user.id;

      axios.get(`/api/user/getById?id=${user_id}`).then((response) => {
        console.log(response.data[0]);
        const {
          first_name,
          last_name,
          email,
          position,
          ext,
          phone,
          mobile_type,
          fax,
          address,
          address_l2,
          city,
          state,
          zip,
        } = response.data[0];
        setUserProfile({
          email: email,
          firstName: first_name,
          lastName: last_name,
          title: position,
          extension: ext,
          mainPhone: phone,
          mainPhoneType: mobile_type,
          fax: fax,
          address: {
            street: address,
            street2: address_l2,
            city: city,
            state: state,
            zip: zip,
          },
        });
        let formattedPhone = "";
        for (let i = 0; i < phone.length; i++) {
          if (i === 0) {
            formattedPhone = `(${phone[i]}`;
          } else if (i === 3) {
            formattedPhone += `) ${phone[i]}`;
          } else if (i === 6) {
            formattedPhone += `-${phone[i]}`;
          } else {
            formattedPhone += phone[i];
          }
        }
        setFormatPhone(formattedPhone);
      });

      axios.get(`/api/settings/get?id=${user_id}`).then((response) => {
        console.log(response.data);
        const {
          invoice_view,
          invoice_sort,
          inv_new_email,
          inv_new_badge,
          inv_new_sms,
          inv_recieved_email,
          inv_recieved_badge,
          inv_recieved_sms,
          inv_pastdue_email,
          inv_pastdue_badge,
          inv_pastdue_sms,
          order_view,
          order_sort,
          ord_new_email,
          ord_new_badge,
          ord_new_sms,
          ord_recieved_email,
          ord_recieved_badge,
          ord_recieved_sms,
          ord_shipped_email,
          ord_shipped_badge,
          ord_shipped_sms,
        } = response.data[0];
        setSettings({
          invoice: {
            view: invoice_view,
            sort: invoice_sort,
            new: {
              email: inv_new_email,
              badge: inv_new_badge,
              sms: inv_new_sms,
            },
            payment_recieved: {
              email: inv_recieved_email,
              badge: inv_recieved_badge,
              sms: inv_recieved_sms,
            },
            past_due: {
              email: inv_pastdue_email,
              badge: inv_pastdue_badge,
              sms: inv_pastdue_sms,
            },
          },
          orders: {
            view: order_view,
            sort: order_sort,
            new: {
              email: ord_new_email,
              badge: ord_new_badge,
              sms: ord_new_sms,
            },
            recieved: {
              email: ord_recieved_email,
              badge: ord_recieved_badge,
              sms: ord_recieved_sms,
            },
            shipped: {
              email: ord_shipped_email,
              badge: ord_shipped_badge,
              sms: ord_shipped_sms,
            },
          },
        });
        console.log(settings);
      });
    }
  }, [session]);

  return (
    <Layout>
      <div className="h-full overflow-y-auto bg-white rounded-xl dark:bg-gray-800 flex flex-col divide-y">
        <div className="px-6 py-2 font-bold text-md">
          <p className="text-2xl p-4">Settings</p>
        </div>
        <div className="px-6 py-2 font-semibold text-sm">
          User Profile
          <div className="flex justify-center py-8">
            <form className="w-3/4 mx-auto">
              <div className="grid grid-cols-4 gap-4 mb-5">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="firstName" value="First Name" />
                  </div>
                  <TextInput
                    id="firstName"
                    type="text"
                    placeholder="First Name"
                    required={true}
                    value={userProfile.firstName}
                    // onChange={(e) => setFirstName(e.target.value)}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        firstName: e.target.value,
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="lastName" value="Last Name" />
                  </div>
                  <TextInput
                    id="lastName"
                    type="text"
                    placeholder="Last Name"
                    required={true}
                    value={userProfile.lastName}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        lastName: e.target.value,
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="email" value="Email/Username" />
                  </div>
                  <TextInput
                    id="email"
                    type="email"
                    placeholder="Email/Username"
                    required={true}
                    shadow={true}
                    value={userProfile.email}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        email: e.target.value,
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="position" value="Job Title" />
                  </div>
                  <TextInput
                    id="position"
                    type="text"
                    placeholder="Job Title"
                    // required={true}
                    value={userProfile.title}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        title: e.target.value,
                      })
                    }
                    className="custom-input"
                  />
                </div>

                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="phone" value="Phone" />
                  </div>
                  <TextInput
                    id="phone"
                    type="text"
                    maxLength={14}
                    placeholder="Phone"
                    // required={true}
                    value={formatPhone}
                    onChange={handlePhoneChange}
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="ext" value="Extension" />
                  </div>
                  <TextInput
                    id="ext"
                    type="text"
                    placeholder="Extension"
                    // required={true}
                    value={userProfile.extension}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        extension: e.target.value,
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-0.5 block">
                    <Label htmlFor="mobile_type" value="Phone Type" />
                  </div>
                  <Select
                    options={phone_options}
                    id="mobile_type"
                    // required={true}
                    placeholder="Choose One"
                    // data-id="account"
                    className="custom-select"
                    onChange={(selectedOption) =>
                      setUserProfile({
                        ...userProfile,
                        mainPhoneType: selectedOption.value,
                      })
                    }
                    value={phone_options.find(
                      (option) => option.value === userProfile.mainPhoneType
                    )}
                  />
                </div>

                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="fax" value="Fax" />
                  </div>
                  <TextInput
                    id="fax"
                    type="text"
                    placeholder="Fax"
                    // required={true}
                    value={userProfile.fax}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        fax: e.target.value,
                      })
                    }
                    className="custom-input"
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="address_l1" value="Street Address" />
                  </div>
                  <TextInput
                    id="address_l1"
                    type="text"
                    placeholder="Street Address"
                    // required={true}
                    value={userProfile.address.street}
                    // onChange={(e) => setFirstName(e.target.value)}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        address: { street: e.target.value },
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="address_l2" value="Address Line 2" />
                  </div>
                  <TextInput
                    id="address_l2"
                    type="text"
                    placeholder="Address Line 2"
                    // required={true}
                    value={userProfile.address.street2}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        address: { street2: e.target.value },
                      })
                    }
                    className="custom-input"
                  />
                </div>
              </div>
              <div className="grid grid-cols-3  gap-4">
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="city" value="City" />
                  </div>
                  <TextInput
                    id="city"
                    type="text"
                    placeholder="City"
                    // required={true}
                    value={userProfile.address.city}
                    // onChange={(e) => setFirstName(e.target.value)}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        address: { city: e.target.value },
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="zip" value="Zip" />
                  </div>
                  <TextInput
                    id="zip"
                    type="text"
                    placeholder="Zip"
                    // required={true}
                    value={userProfile.address.zip}
                    // onChange={(e) => setFirstName(e.target.value)}
                    onChange={(e) =>
                      setUserProfile({
                        ...userProfile,
                        address: { zip: e.target.value },
                      })
                    }
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-2 block">
                    <Label htmlFor="state" value="State" />
                  </div>
                  <Select
                    options={stateNames}
                    name="state"
                    required={true}
                    placeholder="State"
                    value={
                      userProfile.address.state !== ""
                        ? stateNames.find(
                            (option) =>
                              option.value === userProfile.address.state
                          )
                        : ""
                    }
                    className="custom-select"
                    onChange={(selectedOption) =>
                      setUserProfile({
                        ...userProfile,
                        address: { state: selectedOption.value },
                      })
                    }
                  />
                </div>
              </div>
            </form>
          </div>
          <div className="pt-8">
            Change Password
            <div className="flex justify-center py-8">
              <form
                className="w-3/4 mx-auto"
                id="password_form"
                name="password_form"
                onSubmit={changePassword}
              >
                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div>
                    <div className="mb-2 block">
                      <Label
                        htmlFor="current_password"
                        value="Current Password"
                      />
                    </div>
                    <TextInput
                      id="current_password"
                      type="password"
                      required
                      placeholder="Current Password"
                      // required={true}
                      // onChange={(e) => setFirstName(e.target.value)}
                      onChange={(e) =>
                        setPasswordChange({
                          ...passwordChange,
                          current_password: e.target.value,
                        })
                      }
                      className="custom-input"
                    />
                  </div>
                  <div>
                    <div className="mb-2 block">
                      <Label htmlFor="new_password" value="New Password" />
                    </div>
                    <TextInput
                      id="new_password"
                      type="password"
                      placeholder="New Password"
                      required
                      helperText={
                        <span className="text-xs">
                          Password must contain 8 characters with, 1 uppercase,
                          1 lowercase and 1 number
                        </span>
                      }
                      // required={true}
                      // onChange={(e) => setFirstName(e.target.value)}
                      onChange={(e) =>
                        setPasswordChange({
                          ...passwordChange,
                          new_password: e.target.value,
                        })
                      }
                      className="custom-input"
                    />
                  </div>
                  <div>
                    <div className="mb-2 block">
                      <Label
                        htmlFor="confirm_new_password"
                        value="Confirm New Password"
                      />
                    </div>
                    <TextInput
                      id="confirm_new_password"
                      type="password"
                      placeholder="Confirm Password"
                      required
                      // required={true}
                      // onChange={(e) => setFirstName(e.target.value)}
                      onChange={(e) =>
                        setPasswordChange({
                          ...passwordChange,
                          confirm_new_password: e.target.value,
                        })
                      }
                      className="custom-input"
                    />
                  </div>
                </div>
                <div className="m-auto pt-8">
                  <Button
                    className="m-auto theme-button"
                    type="submit"
                    pill={true}
                  >
                    Update Password
                  </Button>
                  {passwordCheck.show && (
                    <p
                      className={
                        passwordCheck.pass
                          ? "text-center text-green-600 mt-4"
                          : "text-center text-red-600 mt-4"
                      }
                    >
                      {passwordCheck.message}
                    </p>
                  )}
                </div>
              </form>
            </div>
          </div>
        </div>
        <div className="px-6 py-2 font-semibold text-sm">
          Invoices
          <div className="grid grid-cols-3 gap-4 py-2">
            <div>
              <p className="text-xs font-normal text-gray-500 mb-4">View</p>
              <div className="w-1/4">
                <Select
                  options={view_options}
                  id="invoice_view"
                  // required={true}
                  placeholder="Choose One"
                  // data-id="account"
                  className="custom-select setting w-max"
                  onChange={(selectedOption) =>
                    setSettings({
                      ...settings,
                      invoice: {
                        ...settings.invoice,
                        view: selectedOption.value,
                      },
                    })
                  }
                  value={view_options.find(
                    (option) => option.value === settings.invoice.view
                  )}
                />
              </div>
            </div>
            <div>
              <p className="text-xs font-normal text-gray-500 mb-4">Sort By</p>
              <div className="w-1/4">
                <Select
                  options={sort_options}
                  id="invoice_sort"
                  // required={true}
                  placeholder="Choose One"
                  // data-id="account"
                  className="custom-select setting w-max"
                  onChange={(selectedOption) =>
                    setSettings({
                      ...settings,
                      invoice: {
                        ...settings.invoice,
                        sort: selectedOption.value,
                      },
                    })
                  }
                  value={sort_options.find(
                    (option) => option.value === settings.invoice.sort
                  )}
                />
              </div>
            </div>
            <div>
              <p className="text-xs font-normal text-gray-500 mb-4">
                Notification Settings
              </p>
              <div className="flex gap-2 flex-col">
                <div className="grid grid-cols-4 gap-x-2 gap-y-4">
                  <div>
                    <Label
                      htmlFor="invoice_new"
                      className="text-xs font-normal"
                    >
                      New Invoice
                    </Label>
                  </div>
                  <div>
                    <Checkbox
                      id="invoice_new_email"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.new.email}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.new.email = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Email</span>
                  </div>
                  <div>
                    <Checkbox
                      id="invoice_new_badge"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.new.badge}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.new.badge = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Badge</span>
                  </div>
                  <div>
                    <Checkbox
                      id="invoice_new_sms"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.new.sms}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.new.sms = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">SMS</span>
                  </div>
                  <div>
                    <Label
                      htmlFor="payment_recieved"
                      className="text-xs font-normal"
                    >
                      Payment Recieved
                    </Label>
                  </div>
                  <div>
                    <Checkbox
                      id="payment_recieved_email"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.payment_recieved.email}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.payment_recieved.email =
                            e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Email</span>
                  </div>
                  <div>
                    <Checkbox
                      id="payment_recieved_badge"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.payment_recieved.badge}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.payment_recieved.badge =
                            e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Badge</span>
                  </div>
                  <div>
                    <Checkbox
                      id="payment_recieved_sms"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.payment_recieved.sms}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.payment_recieved.sms =
                            e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">SMS</span>
                  </div>
                  <div>
                    <Label htmlFor="past_due" className="text-xs font-normal">
                      Past Due
                    </Label>
                  </div>
                  <div>
                    <Checkbox
                      id="past_due_email"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.past_due.email}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.past_due.email = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Email</span>
                  </div>
                  <div>
                    <Checkbox
                      id="past_due_badge"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.past_due.badge}
                      onChange={(e) => {
                        console.log("em");
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.past_due.badge = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Badge</span>
                  </div>
                  <div>
                    <Checkbox
                      id="past_due_sms"
                      data-id="invoice"
                      className="checked:bg-primary"
                      checked={settings.invoice.past_due.sms}
                      onChange={(e) => {
                        console.log("em");
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.invoice.past_due.sms = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">SMS</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="px-6 py-2 font-semibold text-sm">
          Orders
          <div className="grid grid-cols-3 gap-4 py-2">
            <div>
              <p className="text-xs font-normal text-gray-500 mb-4">View</p>
              <div className="w-1/4">
                <Select
                  options={view_options}
                  id="order_view"
                  // required={true}
                  placeholder="Choose One"
                  // data-id="account"
                  className="custom-select setting w-max"
                  onChange={(selectedOption) =>
                    setSettings({
                      ...settings,
                      orders: {
                        ...settings.orders,
                        view: selectedOption.value,
                      },
                    })
                  }
                  value={view_options.find(
                    (option) => option.value === settings.orders.view
                  )}
                />
              </div>
            </div>
            <div>
              <p className="text-xs font-normal text-gray-500 mb-4">Sort By</p>
              <div className="w-1/4">
                <Select
                  options={sort_options}
                  id="order_sort"
                  // required={true}
                  placeholder="Choose One"
                  // data-id="account"
                  className="custom-select setting w-max"
                  onChange={(selectedOption) =>
                    setSettings({
                      ...settings,
                      orders: {
                        ...settings.orders,
                        sort: selectedOption.value,
                      },
                    })
                  }
                  value={sort_options.find(
                    (option) => option.value === settings.orders.sort
                  )}
                />
              </div>
            </div>
            <div>
              <p className="text-xs font-normal text-gray-500 mb-4">
                Notification Settings
              </p>
              <div className="flex gap-2 flex-col">
                <div className="grid grid-cols-4 gap-x-2 gap-y-4">
                  <div>
                    <Label htmlFor="order_new" className="text-xs font-normal">
                      New Order
                    </Label>
                  </div>
                  <div>
                    <Checkbox
                      id="order_new_email"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.new.email}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.new.email = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Email</span>
                  </div>
                  <div>
                    <Checkbox
                      id="order_new_badge"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.new.badge}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.new.badge = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Badge</span>
                  </div>
                  <div>
                    <Checkbox
                      id="order_new_sms"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.new.sms}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.new.sms = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">SMS</span>
                  </div>
                  <div>
                    <Label
                      htmlFor="order_recieved"
                      className="text-xs font-normal"
                    >
                      Order Recieved
                    </Label>
                  </div>
                  <div>
                    <Checkbox
                      id="order_recieved_email"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.recieved.email}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.recieved.email = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Email</span>
                  </div>
                  <div>
                    <Checkbox
                      id="order_recieved_badge"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.recieved.badge}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.recieved.badge = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Badge</span>
                  </div>
                  <div>
                    <Checkbox
                      id="order_recieved_sms"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.recieved.sms}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.recieved.sms = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">SMS</span>
                  </div>
                  <div>
                    <Label
                      htmlFor="order_shipphed"
                      className="text-xs font-normal"
                    >
                      Order Shipped
                    </Label>
                  </div>
                  <div>
                    <Checkbox
                      id="order_shipphed_email"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.shipped.email}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.shipped.email = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Email</span>
                  </div>
                  <div>
                    <Checkbox
                      id="order_shipphed_badge"
                      className="checked:bg-primary"
                      data-id="order"
                      checked={settings.orders.shipped.badge}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.shipped.badge = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">Badge</span>
                  </div>
                  <div>
                    <Checkbox
                      id="order_shipphed_sms"
                      data-id="order"
                      className="checked:bg-primary"
                      checked={settings.orders.shipped.sms}
                      onChange={(e) => {
                        setSettings((prev) => {
                          const updated = { ...prev };
                          updated.orders.shipped.sms = e.target.checked;
                          return updated;
                        });
                      }}
                    />
                    <span className="text-xs font-normal ml-3">SMS</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="container mt-20 mb-8 mx-auto w-3/5 flex justify-around">
            <Button
              className="col-span-3 col-start-1 w-52 theme-button"
              pill={true}
              onClick={updateSettings}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
