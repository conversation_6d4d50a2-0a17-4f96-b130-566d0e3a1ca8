// Inbox.js
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { useRouter } from 'next/router';
import axios from "axios";
import Layout from "../components/layout";
import InboxSidebar from "../components/inbox/InboxSidebar";
import InboxPanel from "../components/inbox/InboxPanel";

const Inbox = () => {
    const { data: session, status, update } = useSession();
    const router = useRouter();
    const { id } = router.query;

    const [messages, setMessages] = useState([]);
    const [selectedMessage, setSelectedMessage] = useState(null);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const user_id = session.user.id;
        const response = await axios.get(`/api/userNotifications/get?id=${user_id}`);
  
        const apiNotifications = response.data;
        if (apiNotifications.length > 0) {
            const formatMessages = apiNotifications.map((userNotification) => ({
            id: userNotification.id,
            title: userNotification.Notifications.headline,
            content: userNotification.Notifications.body,
            open: Boolean(userNotification.open),
            }));
    
            setMessages((prevMessages) => {
                const newMessages = formatMessages.filter((message) => !prevMessages.some((prevMessage) => prevMessage.id === message.id));
                return [...prevMessages, ...newMessages];
            });
            // Check if an 'id' is present in the router query
            if (id) {
                // Find the message with the matching 'id'
                const messageToSelect = formatMessages.find((message) => message.id === parseInt(id, 10));
                if (messageToSelect) {
                    // Set the selected message
                    setSelectedMessage(messageToSelect);
                }
            } else {
                
                // Preselect the first message
                setSelectedMessage(formatMessages[0]);
                handleSelectMessage(formatMessages[0]);
            }
        } else {
            // Display a message if there are no messages
            setMessages([]);
            setSelectedMessage(null);
        }
      } catch (error) {
        console.error("Error fetching messages:", error);
      }
    };

    if (session) {
        fetchMessages();
      }
  
  }, [session]);

  const handleSelectMessage = async (message) => {
    try {
      // Mark the message as opened (assuming you have an 'open' property in your message object)
      if (!message.open) {
        const updatedMessages = messages.map((msg) =>
          msg.id === message.id ? { ...msg, open: true } : msg
        );
        setMessages(updatedMessages);
        
        // Update the userNotification to set open=1
        const userNotificationId = message.id; // Assuming 'id' is the identifier of userNotification
        await axios.put(`/api/userNotifications/updateOpenStatus?id=${userNotificationId}`, { open: 1 });
    }
  
      setSelectedMessage(message);
    } catch (error) {
      console.error("Error updating open status:", error);
    }
  };
  

  return (
    <Layout>
      <div className="h-full w-full overflow-hidden bg-white rounded-xl dark:bg-gray-800">
        <div className="grid grid-cols-6 grid-rows-[auto,1fr] w-full h-full overflow-hidden">
          <div className="col-span-6 border-r-2 border-[#EFF1F0] h-fit w-full">
            <div className="flex w-full px-7 justify-start items-center border-b-2 h-[60px] border-[#EFF1F0]">
              <p className="text-2xl font-bold text-center">Inbox</p>
            </div>
          </div>
      
          <div className="col-span-1 border-r-2 border-[#EFF1F0] h-full w-full overflow-y-auto">
            <InboxSidebar
              messages={messages}
              onMessageClick={handleSelectMessage}
              selectedMessage={selectedMessage}
            />
          </div>
          <div className="col-span-5 pb-10 w-full overflow-y-auto">
            {messages.length === 0 ? (
              <p>No messages available.</p>
            ) : (
              <InboxPanel selectedMessage={selectedMessage} />
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Inbox;
