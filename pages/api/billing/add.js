import prisma from "./../prisma";
import axios from "axios";

export default async (req, res) => {
  const now = new Date();
  if (req.method === "POST") {
    console.log(req.body);
    const {
      nickname,
      card_owner,
      card_number,
      expiration_date,
      cvc,
      company_name,
      company_website,
      address,
      address_2,
      country,
      zipcode,
      city,
      state,
      phone,
      default: isDefault,
    } = req.body.newCard;

    const user = req.body.user;
    const location = req.body.location;
    const account_id = req.body.account_id;

//     console.log('account_id', account_id)
// return;
    const cardpointeAuth = `Basic ${Buffer.from(`${process.env.CARDPOINTE_USER}:${process.env.CARDPOINTE_PASS}`).toString('base64')}`;

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: cardpointeAuth,
      },
    };

    axios
      .put(
        `${process.env.CARDPOINTE_URL}/cardconnect/rest/profile`,
        {
          merchid: process.env.CARDPOINTE_MERCH_ID,
          account: card_number,
          expiry: expiration_date,
          region: state,
          phone: phone,
          postal: zipcode,
          city: city,
          country: country,
          address1: address,
          name: card_owner,
          defaultacct: "N",
        },
        config
      )
      .then(async function (payment) {
        console.log(payment.data);
        if (payment.data.respstat === "A") {
          try {
            const item = await prisma.billing.create({
              data: {
                user_id: user,
                company: company_name,
                website: company_website,
                address: address,
                address_2: address_2,
                city: city,
                state: state,
                country: country,
                postal_code: zipcode,
                phone: phone,
                account_id: account_id,
                created_at: now,
                updated_at: now,
                Payment: {
                  create: {
                    added_by: user,
                    nickname: nickname,
                    last_four: card_number.slice(-4),
                    type: payment.data.accttype,
                    token: payment.data.token,
                    expiry: payment.data.expiry,
                    profileid: payment.data.profileid,
                    authcode: payment.data.authcode,
                    retref: payment.data.retref,
                    location_id: location,
                    account_id: account_id,
                    created_at: now,
                    updated_at: now,
                  },
                },
              },
            });
            return res.status(200).json({
              item,
              ...payment.data,
            });
          } catch (err) {
            console.log(err.message);
            return res.status(500).json(err);
          }
        } else if (payment.data.respstat === "B") {
          console.log(payment.data);
          return res
            .status(500)
            .json({ message: payment.data.resptext, status: "B" });
        } else if (payment.data.respstat === "C") {
          console.log(payment.data);
          return res
            .status(500)
            .json({ message: payment.data.resptext, status: "C" });
        } else {
          console.log(payment.data);
          return res
            .status(500)
            .json({ message: payment.data.resptext, status: "F" });
        }
      })
      .catch(function (error) {
        console.log(error.message);
        return res.status(500).json(error);
      });
  }
};
