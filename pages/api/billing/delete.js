import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "POST") {
    const { id } = req.body;

    try {
      // const item = await prisma.payment.delete({
      //   where: {
      //     id: id,
      //   },
      // });

      const item = await prisma.payment.update({
        where: {
          id: id,
        },
        data: {
          deleted: 1
        }
      });

      return res.status(200).json({ item });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
