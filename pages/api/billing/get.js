import prisma from "./../prisma";
import axios from "axios";

const cardpointeAuth = `Basic ${Buffer.from(`${process.env.CARDPOINTE_USER}:${process.env.CARDPOINTE_PASS}`).toString('base64')}`;

const config = {
  headers: {
    "Content-Type": "application/json",
    Authorization: cardpointeAuth,
  },
};

export default async (req, res) => {
  if (req.method === "GET") {
    const { user_id, account_id, location_id, user_group } = req.query;

    let cards = []; // Initialize cards as an empty array

    console.log('location', location_id);
    console.log('user_group', user_group);

    if ((user_group == '4' || user_group == '2') && location_id) {
      console.log('ADMIN/STANDARD')
      cards = await prisma.payment.findMany({
        where: {
          deleted: 0,
          location_id: Number(location_id),
        },
        include: {
          User: true,
          Billing: true,
        },
      });
    } else {
      console.log('NONADMIN')
      cards = await prisma.payment.findMany({
        where: {
          deleted: 0,
          OR: [
            {
              added_by: {
                equals: Number(user_id),
              },
            },
            {
              account_id: {
                equals: Number(account_id),
              },
            },
          ],
        },
        include: {
          User: true,
          Billing: true,
        },
      });
    }

    console.log('cards', cards);

    // for loop cards
    let card_info = [];

    for (let i = 0; i < cards.length; i++) {
      await axios
        .get(
          `${process.env.CARDPOINTE_URL}/cardconnect/rest/profile/${cards[i].profileid}/1/${process.env.CARDPOINTE_MERCH_ID}`,
          config
        )
        .then(async function (payment) {
          if (payment.data[0].respstat && payment.data[0].respstat === "C"){
            const item = await prisma.payment.delete({
              where: {
                id: cards[i].id,
              },
            });
            if (cards.length == 1){
              return res.status(404).send('Sorry, the credit card information that was previously entered is not complete.  Please re-enter your credit card information.');
            }

          } else {
            card_info.push({
              cardpointe: payment.data,
              db: cards[i],
            });
          }
          
        })
        .catch(function (error) {
          res.status(500).send(error.message);
        });
    }
    res.status(200).send(card_info);
  }
};
