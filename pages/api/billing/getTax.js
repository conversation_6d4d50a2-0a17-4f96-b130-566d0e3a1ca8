import axios from "axios";

export default async (req, res) => {
  try {
    // const contact_id = req.body;
    const { id, cart } = req.body;

    const cartArray = Object.values(cart);

    await axios({
      method: "POST",
      url: process.env.JDE_API_URL + `/Billing/CalculateTax/${id}`,
      data: {
        action: "calculate",
        cart: cartArray,
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    })
      .then((response) => {
        // console.log(response);

        return res.status(response.status).json(response.data);
      })
      .catch((error) => console.log(error));

    return res.status(200).json({ message: "Sales Tax info updated" });
  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};
