import prisma from "./../prisma";
import axios from "axios";

export default async (req, res) => {
  if (req.method === "PUT") {
    const now = new Date();
    const { cardInfo } = req.body;
    console.log("cardInfo", cardInfo);

    const cardpointeAuth = `Basic ${Buffer.from(`${process.env.CARDPOINTE_USER}:${process.env.CARDPOINTE_PASS}`).toString('base64')}`;

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: cardpointeAuth,
      },
    };

    const newCardInfo = {
      profile: cardInfo.profileid + "/" + cardInfo.account_id,
      profileupdate: "Y",
      region: cardInfo.state,
      phone: cardInfo.phone,
      postal: cardInfo.zipcode,
      expiry: cardInfo.expiration_date,
      city: cardInfo.city,
      country: cardInfo.country,
      address: cardInfo.address,
      merchid: process.env.CARDPOINTE_MERCH_ID,
      name: cardInfo.card_owner,
      account: cardInfo.card_number ? cardInfo.card_number : cardInfo.token,
      defaultacct: cardInfo.default,
    };

    axios
      .post(
        `${process.env.CARDPOINTE_URL}/cardconnect/rest/profile/`,
        newCardInfo,
        config
      )
      .then(async function (payment) {
        console.log("payment data", payment.data);
        if (payment.data.respstat === "A") {
          const item = await prisma.billing.findFirst({
            where: {
              user_id: cardInfo.user_id,
              company: cardInfo.company_name,
              website: cardInfo.company_website,
              address: cardInfo.address,
              address_2: cardInfo.address_2,
              city: cardInfo.city,
              state: cardInfo.state,
              country: cardInfo.country,
              postal_code: cardInfo.zipcode,
              phone: cardInfo.phone,
            },
            select: {
              id: true,
            },
          });

          console.log(item);

          if (!item) {
            const item = await prisma.billing.create({
              data: {
                user_id: cardInfo.user_id,
                company: cardInfo.company_name,
                website: cardInfo.company_website,
                address: cardInfo.address,
                address_2: cardInfo.address_2,
                city: cardInfo.city,
                state: cardInfo.state,
                country: cardInfo.country,
                postal_code: cardInfo.zipcode,
                phone: cardInfo.phone,
                created_at: now,
                updated_at: now,
              },
              select: {
                id: true,
              },
            });

            console.log(item);
          }

          const item2 = await prisma.payment.update({
            where: {
              id: cardInfo.id,
            },
            data: {
              nickname: cardInfo.nickname,
              token: payment.data.token,
              expiry: payment.data.expiry,
              type: payment.data.accttype,
              profileid: payment.data.profileid,
              last_four: cardInfo.card_number.slice(-4),
              billing_id: item.id,
              updated_at: now,
            },
          });

          return res.status(200).json(item2);
      } else if (payment.data.respstat === "B") {
        console.log(payment.data);
        return res
          .status(500)
          .json({ message: payment.data.resptext, status: "B" });
      } else if (payment.data.respstat === "C") {
        console.log(payment.data);
        return res
          .status(500)
          .json({ message: payment.data.resptext, status: "C" });
      } else {
        console.log(payment.data);
        return res
          .status(500)
          .json({ message: payment.data.resptext, status: "F" });
      }
    })
      
      .catch(function (error) {
        console.log(error);
        return res.status(500).json(error);
      });
  }
};
