import prisma from "../prisma";

export default async (req, res) => {
  if (req.method === "GET") {
    const { query, user_id, group_id, location_id, account_id } = req.query;

    try {
      const findProducts = await prisma.product.findMany({
        where: {
          OR: [
            {
              name: {
                contains: query,
              },
            },
            {
              sku: {
                contains: query,
              },
            },
          ],
        },
      });

      const orders = [];

      for (const product of findProducts) {
        // Access the id of each product
        const product_id = product.id;

        const findOrdersByProduct = await prisma.order.findMany({
          where: {
            account_id: Number(account_id),
            OrderedProduct: {
              some: {
                product_id: product_id,
              },
            },
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations: true,
            User: true,
          },
          orderBy: {
            date: "desc", // Sort by date in descending order
          },
        });

        if (findOrdersByProduct.length > 0) {
          orders.push(findOrdersByProduct[0]);
        }
      }

      if (findProducts.length == 0) {
        const checkOrders = await prisma.order.findMany({
          where: {
            account_id: Number(account_id),
            jde_id: query,
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations: true,
            User: true,
          },
          orderBy: {
            date: "desc", // Sort by date in descending order
          },
        });

        if (checkOrders.length > 0) {
          orders.push(checkOrders[0]);
        }
      }

      const findInvoices = await prisma.invoice.findMany({
        where: {
          Order: {
            account_id: Number(account_id),
          },
        },
        include: {
          Order: {
            include: {
              Billing: true,
              Payment: true,
              OrderedProduct: {
                include: {
                  Product: {
                    include: {
                      ProductImage: true,
                    },
                  },
                },
              },
              User: true, // Include the User associated with the Order
              Locations: true,
            },
          },
        },
        orderBy: {
          Order: {
            date: "desc", // Sort by date in descending order
          },
        },
      });

      //   console.log(findProducts);
      //   console.log("orders", findOrders);
      //   console.log(findInvoices);
      const filteredProducts = findProducts.filter(product => product.disabled === 0);

      return res.status(200).json({
        products: filteredProducts,
        orders: orders,
        invoices: findInvoices,
      });
    } catch (err) {
      console.log(err.message);
      return res
        .status(503)
        .json({ message: "Something went wrong. Please try again." });
    }
  }
};
