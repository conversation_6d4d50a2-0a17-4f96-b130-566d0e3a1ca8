import prisma from "../prisma";

export default async (req, res) => {
  if (req.method === "GET") {
    const { id, account_id } = req.query;

    console.log("filter product id",id);

    try {
      const findOrdersByProduct = await prisma.order.findMany({
        where: {
          account_id: Number(account_id),
          OrderedProduct: {
            some: {
              product_id: Number(id),
            },
          },
        },
        include: {
          Billing: true,
          Payment: true,
          OrderedProduct: {
            include: {
              Product: {
                include: {
                  ProductImage: true,
                },
              },
            },
          },
          Locations: true,
          User: true,
        },
        orderBy: {
          date: "desc", // Sort by date in descending order
        },
      });

      return res.status(200).json(findOrdersByProduct);
    } catch (err) {
      console.log(err.message);
      return res
        .status(503)
        .json({ message: "Something went wrong. Please try again." });
    }
  }
};
