import prisma from './prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    console.log('incoming req');
    const { account, user_id } = req.query;
    try {
      // cart
      const items = await prisma.cart.findMany({
        where: {
          AND: {
            account_id: {
              equals: Number(account),
            },
            user_id: {
              equals: Number(user_id),
            },
          },
        },
        include: {
          Product: {
            include: {
              ProductImage: true,
            },
          },
        },
      });
      console.log(items)
      return res.status(200).json({ items });
    } catch (err) {
      console.log(err)
      return res.status(503).json({ err: err.toString() });
    }
  }
};
