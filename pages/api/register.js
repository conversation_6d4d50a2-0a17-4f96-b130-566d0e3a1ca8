import prisma from "./prisma";
const bcrypt = require("bcrypt");

// type Data = {
//     name: string
//     first_name: string,
//     last_name: string,
//     email: string,
//     password: string
// }

export default async (req, res) => {
  console.log("here");
  if (req.method === "POST") {
    const { account, user, contacts, source, approved, childLocations } =
      req.body;
    const now = new Date();
    let contactId = 1;

    try {
      let setAcct = false;
      const hash = await bcrypt.hash(user.password, 10);
      // Create owner contact first
      const primary = await prisma.user.create({
        data: {
          first_name: user.firstName,
          last_name: user.lastName,
          position: user.jobTitle,
          phone: user.phone,
          address: account.address_l1,
          address_l2: account.address_l2,
          city: account.city,
          fax: account.fax,
          state: account.state,
          zip: account.zip,
          email: user.email,
          password: hash,
          primary_user: 1,
          position: "Owner",
          jde_id: account.jde_id,
          created_at: now,
          updated_at: now,
          Group: {
            connect: {
              id: 1,
            },
          },
          Setting: {
            create: {
              invoice_view: "all",
              created_at: now,
              updated_at: now,
            },
          },
        },
      });
      await prisma.account
        .create({
          data: {
            business_name: account.business_name,
            address: account.address_l1,
            address_l2: account.address_l2,
            city: account.city,
            fax: account.fax,
            state: account.state,
            website: account.website,
            zip: account.zip,
            hear_of_oasis: account.hear_of_oasis,
            invoice_preferred_by: account.invoice_preferred_by,
            private_practice: account.private_practice,
            tax_exempt_or_resale: account.tax_exempt_or_resale,
            exempt_resale_number:  account.exempt_resale_number,
            num_locations: Number(account.num_locations),
            treat_dry_eye: account.treat_dry_eye,
            punctal_plugs: account.punctal_plugs,
            pref_freight_delivery: account.pref_freight_delivery,
            freight_company: account.freight_company,
            shipping_acct_num: account.shipping_acct_num,
            email_marketing_on: account.email_marketing_on,
            primary_specialty: account.primary_specialty,
            secondary_specialty: account.secondary_specialty,
            partner_type: account.partner_type,
            jde_id: account.jde_id,
            territory_code: account.territory_code,
            terms:account.accept,
            approved: approved,
            created_at: now,
            updated_at: now,
            Locations: {
              create: {
                jde_id: account.jde_id,
                practice: account.business_name,
                website: account.website,
                address: account.address_l1,
                address_2: account.address_l2,
                city: account.city,
                state: account.state,
                zip: account.zip,
                phone: user.phone,
                type: account.type,
                partner_type: account.partner_type,
                created_at: now,
                updated_at: now,
                default: 1,
              },
            },
            Contact: {
              create: [
                {
                  type: "Owner",
                  contact_id: contactId++,
                  created_at: now,
                  updated_at: now,
                  User: {
                    connect: {
                      id: primary.id,
                    },
                  },
                },
                contacts && contacts.purchasing_email
                  ? {
                      type: "Purchasing",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.purchasing_email,
                          },
                          create: {
                            first_name: contacts.purchasing_firstName,
                            last_name: contacts.purchasing_lastName,
                            phone: contacts.purchasing_phone,
                            email: contacts.purchasing_email,
                            position: "Purchasing",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                invoice_view: "all",
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 4,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
                contacts && contacts.accounting_email
                  ? {
                      type: "Accounting",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.accounting_email,
                          },
                          create: {
                            first_name: contacts.accounting_firstName,
                            last_name: contacts.accounting_lastName,
                            phone: contacts.accounting_phone,
                            email: contacts.accounting_email,
                            position: "Accounting",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 3,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
                contacts && contacts.manager_email
                  ? {
                      type: "Manager",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.manager_email,
                          },
                          create: {
                            first_name: contacts.manager_firstName,
                            last_name: contacts.manager_lastName,
                            email: contacts.manager_email,
                            position: "Manager",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                invoice_view: "all",
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 4,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
                contacts && contacts.front_office_email
                  ? {
                      type: "Front Office",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.front_office_email,
                          },
                          create: {
                            first_name: contacts.front_office_firstName,
                            last_name: contacts.front_office_lastName,
                            email: contacts.front_office_email,
                            position: "Front Office",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                invoice_view: "all",
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 4,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
                contacts && contacts.lead_tech_email
                  ? {
                      type: "Lead Tech",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.lead_tech_email,
                          },
                          create: {
                            first_name: contacts.lead_tech_firstName,
                            last_name: contacts.lead_tech_lastName,
                            email: contacts.lead_tech_email,
                            position: "Lead Tech",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                invoice_view: "all",
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 4,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
                contacts && contacts.ophtha_email
                  ? {
                      type: "Ophthalmologist",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.ophtha_email,
                          },
                          create: {
                            first_name: contacts.ophtha_firstName,
                            last_name: contacts.ophtha_lastName,
                            email: contacts.ophtha_email,
                            position: "Ophthalmologist",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                invoice_view: "all",
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 4,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
                contacts && contacts.optometrist_email
                  ? {
                      type: "Optometrist",
                      contact_id: contactId++,
                      created_at: now,
                      updated_at: now,
                      User: {
                        connectOrCreate: {
                          where: {
                            email: contacts.optometrist_email,
                          },
                          create: {
                            first_name: contacts.optometrist_firstName,
                            last_name: contacts.optometrist_lastName,
                            email: contacts.optometrist_email,
                            position: "Optometrist",
                            jde_id: account.jde_id,
                            created_at: now,
                            updated_at: now,
                            Setting: {
                              create: {
                                invoice_view: "all",
                                created_at: now,
                                updated_at: now,
                              },
                            },
                            Group: {
                              connect: {
                                id: 4,
                              },
                            },
                          },
                        },
                      },
                    }
                  : null,
              ]
                .filter(Boolean)
                // .reverse(),
            },
          },
        })
        .then(async (account) => {
          console.log(account.id);

          const contactsSearch = await prisma.Contact.findMany({
            where: {
              account_id: account.id, // Filter by account_id
            },
            select: {
              user_id: true,
            },
          });

          const primarySearch = await prisma.user.findFirst({
            where: {
              account_id: account.id, // Filter by account_id
              primary_user: 1,
            },
            select: {
              id: true,
            },
          });
          const locationSearch = await prisma.Locations.findFirst({
            where: {
              account_id: account.id, // Filter by account_id
              practice: account.business_name,
              website: account.website,
              address: account.address,
              address_2: account.address_l2,
              city: account.city,
              state: account.state,
              zip: account.zip,
            },
            select: {
              id: true,
            },
          });

          if (primarySearch && locationSearch) {
            await prisma.$transaction([
              prisma.user.updateMany({
                where: {
                  account_id: account.id,
                },
                data: {
                  account_id: account.id,
                  location_id: locationSearch.id,
                },
              }),
              prisma.user.update({
                where: {
                  id: primarySearch.id,
                },
                data: {
                  account_id: account.id,
                  location_id: locationSearch.id,
                },
              }),
            ]);
            setAcct = true;
            console.log("Location Updated for", primary);
          }
          if (contactsSearch && locationSearch) {
            for (const contact of contactsSearch) {
              console.log(contact);
              await prisma.$transaction([
                prisma.user.updateMany({
                  where: {
                    account_id: account.id,
                  },
                  data: {
                    account_id: account.id,
                    location_id: locationSearch.id,
                  },
                }),
                prisma.user.update({
                  where: {
                    id: contact.user_id,
                  },
                  data: {
                    account_id: account.id,
                    location_id: locationSearch.id,
                  },
                }),
              ]);
            }
          }

          if (childLocations) {
            console.log("Child Locations Present");
            let allChildLocations = [];

            for (let locations in childLocations) {
              if (childLocations.hasOwnProperty(locations)) {
                let locationData = childLocations[locations];
                allChildLocations = [
                  ...allChildLocations,
                  {
                    jde_id: locationData.jde_id.toString(),
                    practice: locationData.company,
                    website: locationData.website,
                    address: locationData.address_1,
                    address_2: locationData.address_2,
                    city: locationData.city,
                    state: locationData.state,
                    zip: locationData.zip,
                    country: "US",
                    phone: locationData.phone,
                    type: locationData.type,
                    partner_type: locationData.partner_type,
                    account_id: account.id,
                    created_at: now,
                    updated_at: now,
                  },
                ];

                // const createChildLocations = await prisma.locations.create({
                //   data: {
                //     jde_id: locationData.jde_id.toString(),
                //     practice: locationData.company,
                //     website: locationData.website,
                //     address: locationData.address_1,
                //     address_2: locationData.address_2,
                //     city: locationData.city,
                //     state: locationData.state,
                //     zip: locationData.zip,
                //     country: "US",
                //     phone: locationData.phone,
                //     type: req.body.type,
                //     partner_type: req.body.partner_type,
                //     account_id: 1,
                //     created_at: now,
                //     updated_at: now,
                //   },
                // });

                // console.log("createChildLocations", createChildLocations);
              }
            }

            console.log("allChildLocations", allChildLocations);

            const createChildLocations = await prisma.locations.createMany({
              data: allChildLocations,
            });
          }
          return res
            .status(200)
            .json({ account_id: account.id, source: source, contacts: contacts });
        });
    } catch (err) {
      console.log(err);
      return res.status(503).json({ err: err.toString() });
    }
  } else {
    return res
      .status(405)
      .json({ error: "This request only supports POST requests" });
  }
};
