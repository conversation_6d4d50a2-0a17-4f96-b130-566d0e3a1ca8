import { Decimal } from '@prisma/client/runtime';
import prisma from './prisma';

export default async (req, res) => {
  if (req.method === "POST") {
    const { id, account_id, qty, user_id, price, promotions } = req.body;

    console.log(req.body);
    
    console.log(id);

    try {
       // Check if a cart entry with the same user_id, account_id, and product_id exists
       const existingCartEntry = await prisma.cart.findFirst({
        where: {
          user_id: Number(user_id),
          account_id: Number(account_id),
          product_id: Number(id),
        },
      });

      // Initialize the quantity with the incoming value
      let totalQuantity = Number(qty);

      // If the product exists in the cart, add its current quantity to the total
      if (existingCartEntry) {
        totalQuantity += existingCartEntry.qty;
      }

      // Function to check promotion conditions
      function checkPromotionConditions(productId, quantity, promotion) {
        for (const condition of promotion.Conditions) {
          if (condition.type === 'product_quantity') {
            const productInCondition =
              condition.applicableProducts.length === 0 ||
              condition.applicableProducts.some(product => Number(product.product_id) === Number(productId));
            if (!productInCondition || quantity < condition.threshold) {
              return false;
            }
          }

          if (condition.type !== 'product_quantity') {
            return false;
          }
        }

        return true;
      }

      // Function to check applicable promotions
      function checkApplicablePromotions(productId, quantity, promotions) {
        return promotions.filter(
          promotion =>
            promotion.promotion_type === 'product_specific' &&
            promotion.discount_type === 'fixed_price' &&
            checkPromotionConditions(productId, quantity, promotion)
        );
      }

      // Function to get the final price, considering promotions
      function getFinalPrice(productId, quantity, promotions) {
        const applicablePromotions = checkApplicablePromotions(productId, quantity, promotions);

        let finalPrice = price;

        applicablePromotions.forEach(promo => {
          const promoPrice = promo.discount_value;
          if (Number(promoPrice) < Number(finalPrice)) {
            finalPrice = promoPrice;
          }
        });

        return finalPrice;
      }

      // Get the final price including any applicable promotions
      const finalPrice = getFinalPrice(id, totalQuantity, promotions);
      
      if (existingCartEntry) {
        // If the cart entry exists, update the quantity
        await prisma.cart.update({
          where: { id: existingCartEntry.id },
          data: { qty: totalQuantity, price: Decimal(finalPrice) },
        });
      } else {
        // If the cart entry does not exist, create a new one
        await prisma.cart.create({
          data: {
            user_id: Number(user_id),
            account_id: Number(account_id),
            product_id: Number(id),
            qty: Number(qty),
            price: Decimal(finalPrice),
          },
        });
      }

      return res
        .status(200)
        .json({ message: "Product has been added to your cart." });
    } catch (err) {
      console.log(err.message);
      return res
        .status(503)
        .json({ message: "Something went wrong. Please try again." });
    }
  }
};