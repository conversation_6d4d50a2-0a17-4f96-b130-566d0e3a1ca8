import prisma from "./prisma";

export default async (req, res) => {
  if (req.method === "GET") {
    const { id } = req.query;

    console.log(id);

    let route = [];

    try {
      // products
      let products = await prisma.ProductCategory.findMany({
        where: {
          category_id: Number(id),
          Product : {
            disabled: 0,
          }
        },
        include: {
          Product: {
            select: {
              id: true,
              sku: true,
              name: true,
              stock: true,
              ProductAttribute: true,
              ProductImage: true,
            },
          },
          Category: {
            select: {
              name: true,
              parent_id: true,
              id: true,
            },
          },
        },
      });

      route.push({
        name: products[0].Category.name,
        parent_id: products[0].Category.parent_id,
        id: products[0].Category.id,
      });

      console.log("First Parent ID", route[0].parent_id);

      // if (route[0].parent_id != 1 && route[0].parent_id != 2) {
        let parent = await prisma.category.findMany({
          where: {
            id: Number(route[0].parent_id),
          },
          select: {
            name: true,
            parent_id: true,
            id: true,
          },
        });

        console.log("parent", parent);
        if (parent[0].parent_id != -1){
          route.unshift(parent[0]);
        }
      // }

      console.log("route", route);

      const filters = await prisma.categoryFilter.findMany({
        where: {
          category_id: Number(id),
        },
        include: {
          Filter: {
            include: {
              FilterAttribute: true,
            },
          },
        },
      });

      // //for loop over each individual product
      // for (let i = 0; i < products.length; i++) {
      //   // get the product
      //   const product = products[i];
      //   // prisma fetch from ProductAttribute based on product id
      //   const imagePaths = await prisma.productImage.findMany({
      //     where: {
      //       product_id: product.product_id,
      //     },
      //   });
      //   products[i].images = imagePaths;
      // }

      // const result = JSON.parse(
      //   JSON.stringify(products).split('"ProductImage"').join('"images"')
      // );

      // console.log(products);
      // console.log(route);

      return res.status(200).json({ products, filters, route });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
