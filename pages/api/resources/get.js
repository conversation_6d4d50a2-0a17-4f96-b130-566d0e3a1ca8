import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    // const { info } = JSON.parse(req.body);

    try {
      const categories = await prisma.trainingMaterials.findMany({
        where: {
          disabled: 0,
        },
      });

      return res.status(200).json(categories);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
