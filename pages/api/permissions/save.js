// pages/api/permissions/save.js
import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const { accountId, permissions } = req.body;

  if (!accountId || !permissions) {
    return res.status(400).json({ message: "Missing accountId or permissions" });
  }

  try {
    await prisma.accountCustomPermission.deleteMany({
      where: { account_id: Number(accountId) },
    });

    const defaultPermissions = await prisma.roleDefaultPermission.findMany();

    const defaultMap = new Map();
    defaultPermissions.forEach((row) => {
      const key = `${row.group_id}-${row.permission_id}`;
      defaultMap.set(key, true);
    });

    const selectedMap = new Map();
    Object.entries(permissions).forEach(([groupId, permIds]) => {
      permIds.forEach((pid) => {
        const key = `${groupId}-${pid}`;
        selectedMap.set(key, true);
      });
    });

    const overrideEntries = [];

    // Detect removed permissions (overridden to false)
    defaultMap.forEach((_, key) => {
      if (!selectedMap.has(key)) {
        const [group_id, permission_id] = key.split("-").map(Number);
        overrideEntries.push({
          account_id: Number(accountId),
          group_id,
          permission_id,
          allowed: false,
        });
      }
    });

    // Detect added permissions (overridden to true)
    selectedMap.forEach((_, key) => {
      if (!defaultMap.has(key)) {
        const [group_id, permission_id] = key.split("-").map(Number);
        overrideEntries.push({
          account_id: Number(accountId),
          group_id,
          permission_id,
          allowed: true,
        });
      }
    });

    for (const entry of overrideEntries) {
      await prisma.accountCustomPermission.create({
        data: entry,
      });
    }

    return res.status(200).json({ success: true });
  } catch (err) {
    console.error("Failed to save permissions:", err);
    return res.status(503).json({ error: err.toString() });
  }
};
