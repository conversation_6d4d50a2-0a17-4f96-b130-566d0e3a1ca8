import prisma from "./../prisma";

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method not allowed" });
  }

  try {
    const permissions = await prisma.permission.findMany();

    const sectionOrder = [
      "dashboard",
      "products",
      "my orders",
      "my invoices",
      "billing",
      "locations",
      "users",
      "resources",
      "returns",
      "checkout",
    ];

    const actionPriority = {
      view: 0,
      add: 1,
      edit: 2,
      delete: 3,
    };

    // Sort permissions
    const sorted = permissions
      .sort((a, b) => {
        const sectionA = a.section.toLowerCase();
        const sectionB = b.section.toLowerCase();
        const sectionIndexA = sectionOrder.indexOf(sectionA);
        const sectionIndexB = sectionOrder.indexOf(sectionB);

        if (sectionIndexA !== sectionIndexB) {
          return sectionIndexA - sectionIndexB;
        }

        const actionA = a.action.toLowerCase();
        const actionB = b.action.toLowerCase();
        const priorityA = actionPriority[actionA] ?? 99;
        const priorityB = actionPriority[actionB] ?? 99;

        return priorityA - priorityB;
      })
      .map((perm) => ({
        id: perm.id,
        section: perm.section,
        action: perm.action,
      }));

    res.status(200).json(sorted);
  } catch (error) {
    console.error("Error fetching permissions:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}
