// pages/api/permissions/byGroup.js

import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === 'GET') {
    const groupId = Number(req.query.groupId);

    if (!groupId) {
      return res.status(400).json({ message: 'Missing group id' });
    }

    try {
      const defaultPermissions = await prisma.roleDefaultPermission.findMany({
        where: { group_id: groupId },
        include: { Permission: true }, // get permission.section and .action
      });

      const overrides = await prisma.accountCustomPermission.findMany({
        where: { group_id: groupId },
        include: { Permission: true },
      });

      const effectivePermissions = {};

      // Add default permissions
      defaultPermissions.forEach(({ Permission }) => {
        const { section, action } = Permission;
        if (!effectivePermissions[section]) {
          effectivePermissions[section] = new Set();
        }
        effectivePermissions[section].add(action);
      });

      // Apply overrides
      overrides.forEach(({ Permission, allowed }) => {
        const { section, action } = Permission;
        if (!effectivePermissions[section]) {
          effectivePermissions[section] = new Set();
        }
        if (allowed) {
          effectivePermissions[section].add(action);
        } else {
          effectivePermissions[section].delete(action);
        }
      });

      // Convert Sets to arrays
      const result = {};
      for (const section in effectivePermissions) {
        result[section] = Array.from(effectivePermissions[section]);
      }

      return res.status(200).json(result);
    } catch (err) {
      console.error('Failed to fetch permissions:', err);
      return res.status(503).json({ error: err.toString() });
    }
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
};
