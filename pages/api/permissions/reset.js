import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  const { accountId } = req.body;

  if (!accountId) {
    return res.status(400).json({ message: "Missing account id" });
  }

  try {
    await prisma.accountCustomPermission.deleteMany({
      where: { account_id: Number(accountId) },
    });

    const defaultPermissions = await prisma.roleDefaultPermission.findMany({
      include: { Group: true },
    });

    const effectivePermissions = {};
    defaultPermissions.forEach((row) => {
      const groupId = row.group_id;
      if (!effectivePermissions[groupId]) {
        effectivePermissions[groupId] = [];
      }
      effectivePermissions[groupId].push(row.permission_id);
    });

    return res.status(200).json(effectivePermissions);
  } catch (err) {
    console.error("Failed to reset permissions:", err);
    return res.status(503).json({ error: err.toString() });
  }
};
