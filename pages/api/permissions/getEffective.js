import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === 'GET') {
    const accountId = Number(req.query.id);

    if (!accountId) {
      return res.status(400).json({ message: 'Missing account id' });
    }

    try {
      const defaultPermissions = await prisma.roleDefaultPermission.findMany();
      const overrides = await prisma.accountCustomPermission.findMany({
        where: { account_id: accountId },
      });

      // Step 1: Build base from defaults
      const effectivePermissions = {};

      defaultPermissions.forEach((row) => {
        if (!effectivePermissions[row.group_id]) {
          effectivePermissions[row.group_id] = new Set();
        }
        effectivePermissions[row.group_id].add(row.permission_id);
      });

      // Step 2: Apply overrides (can add or remove)
      overrides.forEach((override) => {
        const groupId = override.group_id;
        const permId = override.permission_id;
        const allowed = override.allowed;

        if (!effectivePermissions[groupId]) {
          effectivePermissions[groupId] = new Set();
        }

        if (allowed) {
          effectivePermissions[groupId].add(permId);
        } else {
          effectivePermissions[groupId].delete(permId);
        }
      });

      // Convert Sets to arrays for output
      const result = {};
      for (const groupId in effectivePermissions) {
        result[groupId] = Array.from(effectivePermissions[groupId]);
      }

      return res.status(200).json(result);
    } catch (err) {
      console.error('Failed to fetch effective permissions:', err);
      return res.status(503).json({ error: err.toString() });
    }
  } else {
    return res.status(405).json({ message: 'Method not allowed' });
  }
};
