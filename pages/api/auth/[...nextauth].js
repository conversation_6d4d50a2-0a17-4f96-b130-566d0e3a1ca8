import NextAuth from "next-auth";
import prisma from "./../prisma";
import CredentialsProvider from "next-auth/providers/credentials";

const bcrypt = require("bcrypt");

const confirmPasswordHash = (plainPassword, hashedPassword) => {
  return new Promise((resolve) => {
    bcrypt.compare(plainPassword, hashedPassword, function (err, res) {
      resolve(res);
    });
  });
};

const configuration = {
  providers: [
    CredentialsProvider({
      id: "credentials",
      name: "Credentials",
      async authorize(credentials) {
        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email,
          },
          include: {
            Account: {
              include: {
                OasisRep: true,
              },
            },
          },
        });

        if (!user || user.Account.approved === 0) {
          return null;
        }

        const isPasswordMatch = await confirmPasswordHash(
          credentials.password,
          user.password
        );

        if (!isPasswordMatch) {
          return null;
        }

        // console.log(user);

        let location_id = 0;
        if (user.location_id) {
          console.log("location_id set");
          location_id = user.location_id;
          const location = await prisma.locations.findFirst({
            where: {
              id: location_id,
            },
          });

          user.Locations = location;
        }

        console.log("user", user);

        return user;
      },
    }),
  ],
  callbacks: {
    async session({ session, token }) {
      session.user = token.user;
      return session;
    },
    async jwt({ token, user, trigger, session }) {
      if (trigger === "update" && session?.password) {
        token.user.password = session.password;
      }
      if (trigger === "update" && session?.sync) {
        token.user.sync = session.sync;
      }
      if (trigger === "update" && session?.popSync) {
        token.user.popSync = session.popSync;
      }
      if (user) {
        token.user = user;
      }
      return token;
    },
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60,
  },
  // jwt: {
  //   maxAge: 2 * 60,
  // },
  pages: {
    signIn: "/",
    signOut: "/",
  },
};
export default (req, res) => NextAuth(req, res, configuration);

// export default NextAuth({
//   providers: [
//     CredentialsProvider({
//       async authorize(credentials, req) {
//         console.log(credentials);
//         const user = {
//           id: "1",
//           first_name: "J",
//           last_name: "Smith",
//           email: "<EMAIL>",
//         };
//         if (user) {
//           console.log("user: " + JSON.stringify(user));
//           return user;
//         } else {
//           return null;
//         }
//       },
//     }),
//   ],
//   pages: {
//     signIn: "/",
//     signOut: "/",
//   },
// });
