import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    try {
      const training = await prisma.trainingCategories.findMany({
        where: {
          disabled: 0,
        },
        include: {
          TrainingMaterials: {
            where: {
              disabled: 0
            }
          }
        },
      });

      return res.status(200).json(training);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
