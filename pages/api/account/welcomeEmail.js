import prisma from './../prisma';
import axios from "axios";

export default async (req, res) => {
  // const { info } = JSON.parse(req.body);
  if (req.method === "POST") {
    const { account_id } = req.body;
    try {
      const account = await prisma.account.findUnique({
        where: {
          id: account_id,
        },
      });

      if (!account) {
        return res.status(404).json({ error: "Error: Account not found" });
      }

      await axios({
        method: "POST",
        url: process.env.JDE_API_URL + `/Account/WelcomeEmail/${account_id}`,
        headers: {
          "Content-Type": "application/json",
          Token:
            process.env.API_TOKEN,
          WebAppId: 1,
        },
      })
        .then(async (response) => {
          if (response.status == 200) {
            console.log(response.data);
            return res.status(200).json({ message: "Welcome email sent" });
          }
        })
        .catch((error) => {
          console.log(error);
  
          return res.status(500).json({ message: error.message });
        });

    } catch (err) {
      console.log(err.message);
      return res.status(500).json({ error: err.message });
    }
  }
};
