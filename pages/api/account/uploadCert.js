import prisma from './../prisma';
import axios from "axios";
import { IncomingForm } from 'formidable'
// you might want to use regular 'fs' and not a promise one
import fs from 'fs'

// first we need to disable the default body parser
export const config = {
  api: {
    bodyParser: false,
  }
};

export default async (req, res) => {
  // parse form with a Promise wrapper
  const data = await new Promise((resolve, reject) => {
    const form = new IncomingForm()
    
    form.parse(req, async (err, fields, files) => {
        if (err) return reject(err)
        resolve({ fields, files })
        // Access fields
        const [account_id] = fields.account_id;
        console.log('Account ID:', account_id);

        // Access files
        const [uploadedFile] = files.uploadedFile;
        console.log('Uploaded File:', uploadedFile);
        if (!uploadedFile) {
            return res.status(40).json({ error: "Error: File required" })
        }
        try {
        const account = await prisma.account.findUnique({
            where: {
            id: parseInt(account_id),
            },
        });

        if (!account) {
            return res.status(404).json({ error: "Error: Account not found" });
        }

        await axios({
            method: "POST",
            url: process.env.JDE_API_URL + `/Account/UploadCert/${account_id}`,
            data: {
                uploadedFile: fs.createReadStream(uploadedFile.filepath),
                fileExtension: uploadedFile.originalFilename.split('.').pop(), // Extract file extension

            },
            headers: {
            "Content-Type": "multipart/form-data",
            Token:
                process.env.API_TOKEN,
            WebAppId: 1,
            },
        })
            .then(async (response) => {
            if (response.status == 200) {
                console.log(response.data);
                return res.status(200).json({ message: "Cert uploaded" });
            }
            })
            .catch((error) => {
            console.log(error);
    
            return res.status(500).json({ message: error.message });
            });

        } catch (err) {
        console.log(err.message);
        return res.status(500).json({ error: err.message });
        }
    })
  })

 }


