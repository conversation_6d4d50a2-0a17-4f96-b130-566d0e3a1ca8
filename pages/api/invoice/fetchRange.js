import prisma from './../prisma';
import { DateTime } from 'luxon';

export default async (req, res) => {
  if (req.method === "GET") {
    // const { info } = JSON.parse(req.body);
    

    try {
      const { startDate, endDate } = req.query;
      const userId = Number(req.query.userId);
      const groupId = Number(req.query.groupId);
      const locationId = Number(req.query.locationId);
      const accountId = Number(req.query.accountId);

      let invoices;
      
      if (groupId === 1 || groupId === 3) {
        invoices = await prisma.invoice.findMany({
          where: {
            Order: {
              account_id: accountId,
              date: {
                gte: new Date(startDate), // Filter orders created on or after startDate
                lte: new Date(endDate),   // Filter orders created on or before endDate
              },
            },
          },
          include: {
            Order: {
              include: {
                Billing: true,
                Payment: true,
                OrderedProduct: {
                  include: {
                    Product: {
                      include: {
                        ProductImage: true,
                      },
                    },
                  },
                },
                User: true, // Include the User associated with the Order
                Locations: true,
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
        });
      } else if (groupId === 2) {
        invoices = await prisma.invoice.findMany({
          where: {
            Order: {
              account_id: accountId,
              shipping_address_id:locationId,
              date: {
                gte: new Date(startDate), // Filter orders created on or after startDate
                lte: new Date(endDate),   // Filter orders created on or before endDate
              },
            },
          },
          include: {
            Order: {
              include: {
                Billing: true,
                Payment: true,
                OrderedProduct: {
                  include: {
                    Product: {
                      include: {
                        ProductImage: true,
                      },
                    },
                  },
                },
                User: true, // Include the User associated with the Order
                Locations: true,
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
        });

      } else if (groupId === 4) {

        const whereClause = {
          account_id: accountId,
          order_by: userId,
          date: {
            gte: new Date(startDate), // Filter orders created on or after startDate
            lte: new Date(endDate),   // Filter orders created on or before endDate
          },
        };
      
        // Check if locationId is defined (not null or undefined) before including it in the query
        if (locationId) {
          whereClause.shipping_address_id = locationId;
        }
        invoices = await prisma.invoice.findMany({
          where: {
            Order: whereClause
          },
          include: {
            Order: {
              include: {
                Billing: true,
                Payment: true,
                OrderedProduct: {
                  include: {
                    Product: {
                      include: {
                        ProductImage: true,
                      },
                    },
                  },
                },
                User: true, // Include the User associated with the Order
                Locations: true,
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
          take: limit,
        });

      } else{
        return res.status(403).json({ error: "Unauthorized" });
      }

      // Update invoice status based on due date
      invoices.forEach(async (invoice) => {
        // Get the current date in Pacific Time (PT) without the time component
        const todayPT = DateTime.now().setZone('America/Los_Angeles').startOf('day');

        // Convert the UTC due date from the invoice to Pacific Time (PT) and remove the time component
        const dueDatePT = DateTime.fromJSDate(invoice.due_date, { zone: 'America/Los_Angeles' }).startOf('day');

        if (invoice.status === 2 && dueDatePT < todayPT) {
          console.log("due",dueDatePT.toISODate());
        console.log("today PT", todayPT.toISODate());
          // Due date has passed and status is 2 (due)
          invoice.status = 3;
          await prisma.invoice.update({
            where: { id: invoice.id },
            data: { status: 3 }, // Set status to 3 (overdue)
          });
        }
      });

      return res.status(200).json(invoices);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
