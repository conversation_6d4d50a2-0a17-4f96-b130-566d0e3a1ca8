import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    // const { info } = JSON.parse(req.body);

    try {
      const userId = Number(req.query.userId);
      const groupId = Number(req.query.groupId);
      const locationId = Number(req.query.locationId);
      const accountId = Number(req.query.accountId);

      let invoices;
      if (groupId === 1) {
        invoices = await prisma.invoice.findMany({
          where: {
            status: 1,
            Order: {
              account_id: accountId,
            },
          },
          include: {
            Order: {
              include: {
                OrderedProduct: {
                    include: {
                      Product: true,
                    },
                  }, 
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
        });
      } else if (groupId === 2) {
        invoices = await prisma.invoice.findMany({
          where: {
            status: 1,
            Order: {
              account_id: accountId,
              shipping_address_id:locationId,
            },
          },
          include: {
            Order: {
              include: {
                OrderedProduct: {
                    include: {
                      Product: true,
                    },
                  }, 
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
        });

      } else if (groupId === 3) {
        invoices = await prisma.invoice.findMany({
          where: {
            status: 1,
            Order: {
              account_id: accountId,
            },
          },
          include: {
            Order: {
              include: {
                OrderedProduct: {
                    include: {
                      Product: true,
                    },
                  }, 
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
        });

      } else if (groupId === 4) {

        const whereClause = {
          account_id: accountId,
          order_by: userId,
        };
      
        // Check if locationId is defined (not null or undefined) before including it in the query
        if (locationId) {
          whereClause.shipping_address_id = locationId;
        }
        invoices = await prisma.invoice.findMany({
          where: {
            status: 1,
            Order: whereClause
          },
          include: {
            Order: {
              include: {
                OrderedProduct: {
                    include: {
                      Product: true,
                    },
                  }, 
              },
            },
          },
          orderBy: {
            Order: {
              date: 'desc', // Sort by date in descending order
            },
          },
        });

      } else{
        return res.status(403).json({ error: "Unauthorized" });
      }

      return res.status(200).json(invoices);
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
