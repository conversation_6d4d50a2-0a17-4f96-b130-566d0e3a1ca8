import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const id = Number(req.query.id);
    try {
      const filters = await prisma.categoryFilter.findMany({
        where: {
          category_id: id,
        },
        include: {
            Filter: {
                include: {
                    FilterAttribute: true
                }
            }
        }
      });

      console.log(filters);
      return res.status(200).json(filters);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
