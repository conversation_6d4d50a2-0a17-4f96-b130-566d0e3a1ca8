import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const id = Number(req.query.id);
    try {
      const returnForm = await prisma.ReturnForm.findUnique({
        where: {
          id: id,
        },
        include: {
            ReturnProducts: {
                include: {
                    Product:true
                }
            },
            Invoice: true
          },
      });

      return res.status(200).json(returnForm);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
