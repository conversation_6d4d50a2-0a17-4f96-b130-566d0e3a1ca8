import prisma from "./../prisma";
import axios from "axios";

export default async (req, res) => {
  if (req.method === "POST") {
    const data = req.body;
    const now = new Date();
    try {
      const products = data.lineItems.map((x) => {
        return {
          product_id: x.product_id,
          qty: x.qty,
        };
      });

      const returnForm = await prisma.ReturnForm.create({
        data: {
          invoice_id: data.invoiceNum,
          status: 1,
          reason: data.reason,
          reason_notes: data.reasonNotes,
          created_at: now,
          updated_at: now,
          ReturnProducts: {
            create: products,
          },
        },
        include: {
          ReturnProducts: true, // Include the Contact relation in the response
        },
      });

      // const contactId = user.Contact.id;

      // res.status(200).json(returnForm.id);

      try {
        const id = returnForm.id;

        console.log("return_id", id);
        await axios({
          method: "POST",
          //Staging
          // url: `https://oasisv2admin.fusionofideas.com/api/Account/SyncJDE/${account_id}`,
          //Local
          url: process.env.JDE_API_URL + `/ReturnForm/SyncJDE/${id}`,
          data: {
            action: "add",
          },
          headers: {
            "Content-Type": "application/json",
            Token:
              process.env.API_TOKEN,
            WebAppId: 1,
          },
        })
          .then((response) => {
            console.log(response);
            return res.status(200).json(response.data);
          })
          .catch((error) => console.log(error));

        // return res.status(200).json();
      } catch (error) {
        console.error(
          "An unexpected error while adding return occurred:",
          error
        );
        return res.status(500).json({ error: "Internal server error" });
      }

      // try {
      //   getRMA = await axios.post("/api/syncJDE/addReturn", returnForm.id);

      //   return res.status(200).json(returnForm.id);
      // } catch (err) {
      //   console.log(err.message);
      //   return res.status(503).json({ err: err.toString() });
      // }
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
