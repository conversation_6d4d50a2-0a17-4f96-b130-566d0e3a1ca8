import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    // const { info } = JSON.parse(req.body);

    try {
      const userId = Number(req.query.userId);
      const groupId = Number(req.query.groupId);
      const locationId = Number(req.query.locationId);
      const accountId = Number(req.query.accountId);
      const limit = req.query.limit ? parseInt(req.query.limit, 10) : 20; // Parse limit as an integer or leave it null if not provided

      let returnForms;
      if (groupId === 1) {
        returnForms = await prisma.ReturnForm.findMany({
          where: {
            Invoice:{
                Order: {
                    account_id: accountId,
                },
            },
          },
          include: {
            Invoice: true
          },
          orderBy: {
            created_at: 'desc'
          },
          take: limit,
        });
      } else if (groupId === 2) {
        returnForms = await prisma.ReturnForm.findMany({
          where: {
            Invoice:{
                Order: {
                account_id: accountId,
                shipping_address_id:locationId,
                },
            },
          },
          include: {
            Invoice: true
          },
          orderBy: {
            created_at: 'desc'
          },
          take: limit,
        });

      } else if (groupId === 3) {
        returnForms = await prisma.ReturnForm.findMany({
          where: {
            Invoice:{
                Order: {
                    account_id: accountId,
                },
            },
          },
          include: {
            Invoice: true
          },
          orderBy: {
            created_at: 'desc'
          },
          take: limit,
        });

      } else if (groupId === 4) {

        const whereClause = {
          account_id: accountId,
          order_by: userId,
        };
      
        // Check if locationId is defined (not null or undefined) before including it in the query
        if (locationId) {
          whereClause.shipping_address_id = locationId;
        }
        returnForms = await prisma.ReturnForm.findMany({
          where: {
            Invoice: {
                Order: whereClause
            },
          },
          include: {
            Invoice: true
          },
          orderBy: {
            created_at: 'desc'
          },
          take: limit,
        });

      } else{
        return res.status(403).json({ error: "Unauthorized" });
      }

      return res.status(200).json(returnForms);
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
