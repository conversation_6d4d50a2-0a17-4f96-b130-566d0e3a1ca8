import prisma from "../prisma";

export default async (req, res) => {
  console.log("incoming req");
  const { cart } = req.body;
  
  console.log(cart);
  let product_id = [];
  let product_type = [];
  let qty = 0;
  let weight = 0;
  for (const key in cart) {
    if (cart.hasOwnProperty(key)) {
      const innerArray = cart[key];
      product_id[key] = innerArray["id"];

      let id = innerArray["product_id"];

      qty += Number(innerArray['qty'])
      weight += (Number(innerArray['Product']['weight_lbs']) * Number(innerArray['qty']))


      const productCategory = await prisma.ProductCategory.findFirst({
        where: {
          product_id: id,
        },
        select: {
          category_id: true,
        },
      });
      let startCat = productCategory.category_id;
      // while (startCat !== 1 && startCat !== 2) {
        const startCatQuery = await prisma.Category.findFirst({
          where: {
            id: {
              equals: startCat,
            },
          },
          select: {
            parent_id: true,
          },
        });

        if(startCatQuery.parent_id == '-1'){

          startCat = startCat;
        } else {
          const tier2CatQuery = await prisma.Category.findFirst({
            where: {
              id: {
                equals: startCatQuery.parent_id,
              },
            },
            select: {
              parent_id: true,
            },
          });
          if(tier2CatQuery.parent_id == '-1'){

            startCat = startCatQuery.parent_id;
          } else {
            const tier3CatQuery = await prisma.Category.findFirst({
              where: {
                id: {
                  equals: tier2CatQuery.parent_id,
                },
              },
              select: {
                parent_id: true,
              },
            });
            if(tier3CatQuery.parent_id == '-1'){
  
              startCat = tier2CatQuery.parent_id;
            }
          }
        }
      // }
      product_type[key] = startCat;
    }
  }

  console.log("product types", product_type);

  const containsValue = product_type.every(num => num === 2);


  return res.status(200).json({onlyVision: containsValue, qty: qty, weight: weight});
};
