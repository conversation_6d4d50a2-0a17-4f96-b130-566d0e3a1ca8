import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === "GET") {
    const { id } = req.query;

    console.log("parent id: " + id);

    let master = [];
    let route = [];

    try {
      let query = await prisma.category.findMany({
        where: {
          parent_id: Number(id),
        },
        include: {
          ProductCategory: {
            include: {
              Product: {
                include: {
                  ProductAttribute: true,
                  ProductImage: true,
                },
              },
            },
          },
        },
      });

      // Filter products where disabled is 0
      query.forEach(category => {
        category.ProductCategory = category.ProductCategory.filter(ProdCategory => ProdCategory.Product.disabled === 0);
      });

      for (let i = 0; i < query.length; i++) {
        if (query[i].ProductCategory.length > 0) {
          query[i].ProductCategory.forEach((item) => {
            master.push(item);
          });
        }
      }

      for (const item in query) {
        const sub = await prisma.category.findMany({
          where: {
            parent_id: query[item].id,
            disabled: 0,
          },
          include: {
            ProductCategory: {
              include: {
                Product: {
                  include: {
                    ProductAttribute: true,
                    ProductImage: true,
                  },
                },
              },
            },
          },
        });

        // Filter products where disabled is 0
        sub.forEach(category => {
        category.ProductCategory = category.ProductCategory.filter(ProdCategory => ProdCategory.Product.disabled === 0);
      });
        
        // console.log(sub);
        for (let i = 0; i < sub.length; i++) {
          if (sub[i].ProductCategory.length > 0) {
            sub[i].ProductCategory.forEach((item) => {
              master.push(item);
            });
          }
        }
      }

      const routing = await prisma.category.findFirst({
        where: {
          id: Number(id)
        }
      })

      route.push({name: routing.name, id: routing.id});

      // const result = JSON.parse(
      //   JSON.stringify(master).split('"ProductImage"').join('"images"')
      // );

      return res.status(200).json({ products: master, route: route });
      // return res.status(200).json({ products, filters });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
