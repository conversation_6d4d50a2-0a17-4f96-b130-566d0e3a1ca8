import axios from "axios";

export default async (req, res) => {
  try {
    const { id, account_jde } = req.body;
    const response = await axios({
      method: "post",
      url: process.env.JDE_API_URL + `/Product/GetStockPrice/${id}`,
      data: {
        account_jde: account_jde,
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    });
    // console.log("status", response.status);

    if (response.status == 200) {
      return res.status(200).json(response.data);
    }
  } catch (error) {
    console.error(
      "An unexpected error while updating product stock and price has occurred: ",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};
