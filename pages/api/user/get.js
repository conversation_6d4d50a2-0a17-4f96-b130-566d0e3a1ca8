import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    // const { info } = JSON.parse(req.body);

    try {
      const users = await prisma.user.findMany({
        where: {
          disabled: 0,
        },
        include: {
          Group: true,
        },
      });

      return res.status(200).json(users);
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
