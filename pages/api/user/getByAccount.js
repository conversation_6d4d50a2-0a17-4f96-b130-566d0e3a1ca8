import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const account_id = Number(req.query.accountId);
    const groupId = Number(req.query.groupId);
    const locationId = Number(req.query.locationId);

    try {
      let users;
      if (groupId === 1) {
        users = await prisma.user.findMany({
          where: {
            account_id: account_id,
            disabled: 0,
          },
          include: {
            Group: true,
            Locations: true,
          },
          orderBy:{
            group_id: 'asc',
          }
        });
      } else if (groupId === 2) {
        users = await prisma.user.findMany({
          where: {
            disabled: 0,
            account_id: account_id,
            location_id: locationId,
            group_id: {
              gte: 2
            }
          },
          include: {
            Group: true,
            Locations: true,
          },
          orderBy:{
            group_id: 'asc',
          }
        });
      } else {
        return res.status(403).json({ error: "Unauthorized" });
      }

      return res.status(200).json(users);
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
