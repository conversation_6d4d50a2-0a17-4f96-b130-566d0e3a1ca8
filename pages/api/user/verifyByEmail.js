import prisma from './../prisma';

export default async (req, res) => {
  // const { info } = JSON.parse(req.body);
  if (req.method === "GET") {
    const { email } = req.query;
    try {
      const user = await prisma.user.findUnique({
        where: {
          email: email,
        },
        select: {
          id: true,
        },
      });

      if (user && user.id !== null) {
        return res.status(200).json(user.id);
      } else {
        return res.status(200).json(0);
      }
    } catch (err) {
      console.log(err.message);
      return res.status(500).json({ error: "An error occurred" });
    }
  }
};
