import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const { email } = req.query;
    try {
      const user = await prisma.user.findUnique({
        where: {
            email: email,
        },
        include: {
          Account: {
            include: {
              OasisRep: true,
            },
          },
        },
      });

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      return res.status(200).json(user);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
