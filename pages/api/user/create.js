import prisma from './../prisma';
import axios from "axios";

const bcrypt = require("bcrypt");

export default async (req, res) => {
  if (req.method === "POST") {
    const data = req.body;
    const now = new Date();
    const hash = await bcrypt.hash(data.password, 10);
    let loc_id = data.location;
    
    try {
      const getContactId = await prisma.contact.findFirst({
        where: {
          account_id: data.account_id,
        },
        orderBy: {
          contact_id: "desc",
        },
        select: {
          contact_id: true,
        },
      });

      let contact_id = Number(getContactId.contact_id)+1;

      const user = await prisma.user.create({
        data: {
          first_name: data.first,
          last_name: data.last,
          position: data.title,
          email: data.email,
          password: hash,
          phone: data.phone,
          fax: data.fax,
          mobile_type: data.phone_type,
          group_id: Number(data.user_group),
          account_id: data.account_id,
          location_id: data.location,
          created_at: now,
          updated_at: now,
          Contact: {
            create: {
              type: data.title,
              contact_id: contact_id,
              account_id: data.account_id,
              created_at: now,
              updated_at: now,
            },
          },
          Setting: {
            create: {
              created_at: now,
              updated_at: now,
            },
          },
        },
        include: {
          Contact: true, // Include the Contact relation in the response
        },
      });

      // ✅ Call backend to send New User Email
      await axios({
        method: "POST",
        url: `${process.env.JDE_API_URL}/User/NewUserEmail/${user.id}`, // Adjust this if your route differs
        headers: {
          "Content-Type": "application/json",
          Token: process.env.API_TOKEN,
          WebAppId: 1,
        },
        data: {
          tempPassword: data.password      // Send plain password from original payload
        },
      })
      .then(() => {
        console.log("New user email sent");
      })
      .catch((error) => {
        console.error("Error sending new user email:", error.message);
        // Not failing the whole API on email issue — log and continue
      });

      // const contactId = user.Contact.id;
      return res.status(200).json(user.Contact[0]);
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }else{
    return res.status(503).json({ err: "Method Not Allowed" });
  }
};
