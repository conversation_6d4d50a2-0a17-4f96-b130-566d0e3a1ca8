import prisma from './../prisma';

export default async (req, res) => {
    // Check if the password is provided and not empty
    const {email} = req.body;

    console.log('last login email', email)

    const now = new Date(); 
    console.log('last login date', now)

    try {
        const setLastLogin = await prisma.user.update({
            where: {
              email: email,
            },
            data: {
              last_login: now,
            },
          });

          console.log('last login', setLastLogin);


      return res.status(200).json(setLastLogin);
    } catch (err) {
      console.log(err);
      return res.status(503).json({ err: err.toString() });
    }
};
