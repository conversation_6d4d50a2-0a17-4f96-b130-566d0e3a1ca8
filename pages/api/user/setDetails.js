import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === "POST") {
    const { email } = req.body;
    try {
      const user = await prisma.user.findUnique({
        where: {
          email: email,
        },
        include: {
          Account: {
            include: {
              OasisRep: true,
            },
          },
        }
      });

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

      console.log("User", user);

      return res.status(200).json(user);
      
    } catch (err) {
      console.log(err.message);
      return res.status(500).json({ error: "An error occurred" });
    }
  }
};
