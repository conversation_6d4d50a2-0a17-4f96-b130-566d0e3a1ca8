import prisma from './../prisma';
import axios from "axios";

export default async (req, res) => {
  // const { info } = JSON.parse(req.body);
  if (req.method === "POST") {
    const { email } = req.body;
    try {
      const user = await prisma.user.findUnique({
        where: {
          email: email,
        },
      });

      if (!user) {
        return res.status(404).json({ error: "Error: Email not found" });
      }

      await axios({
        method: "POST",
        url: process.env.JDE_API_URL + `/User/forgot_password`,
        data: {
            email: email,
        },
        headers: {
          "Content-Type": "application/json",
          Token:
            process.env.API_TOKEN,
          WebAppId: 1,
        },
      })
        .then(async (response) => {
          if (response.status == 200) {
            console.log(response.data);
            return res.status(200).json({ message: "Reset password email sent" });
          }
        })
        .catch((error) => {
          console.log(error);
  
          return res.status(500).json({ message: error.message });
        });

    } catch (err) {
      console.log(err.message);
      return res.status(500).json({ error: err.message });
    }
  }
};
