import prisma from './../prisma';
const bcrypt = require("bcrypt");

export default async (req, res) => {
  if (req.method === "POST") {
    // Check if the password is provided and not empty
    const passwordData = req.body.password.trim();
    const password = passwordData ? await bcrypt.hash(passwordData, 10) : undefined;

    const now = new Date(); 
    try {
      const updateData = {
        first_name: req.body.first,
        last_name: req.body.last,
        position: req.body.title,
        email: req.body.email,
        group_id: req.body.user_group,
        location_id: req.body.location,
        phone: req.body.phone,
        fax: req.body.fax,
        mobile_type: req.body.phone_type,
        updated_at: now,
      };

      // Conditionally update the password field
      if (password) {
        updateData.password = password;
      }

      // Perform the update with conditionally updated data
      const user = await prisma.user.update({
        where: {
          id: req.body.id,
        },
        data: updateData,
        include: {
          Contact: true,
        }
      });

      console.log(user);

      return res.status(200).json(user);
    } catch (err) {
      console.log(err);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
