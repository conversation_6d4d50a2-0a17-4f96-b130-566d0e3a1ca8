import prisma from './../prisma';
const bcrypt = require("bcrypt");

export default async (req, res) => {
  if (req.method === "POST") {
    // Check if the password is provided and not empty
    const passwordData = req.body.password.trim();
    const password = passwordData ? await bcrypt.hash(passwordData, 10) : undefined;

    const now = Math.floor(Date.now() / 1000);
    const timestamp = new Date();
    try {
        // Check for password reset token and account type
        const { token, accountType } = req.body;
        const userTypeId = 1;
  
        // Check against account_recovery
        const recoveryRecord = await prisma.account_recovery.findFirst({
          where: {
            token,
            user_type_id: userTypeId,
            expiration: {
              gte: now,
            },
          },
        });
  
        if (!recoveryRecord) {
          throw new Error('Recovery token does not exist or has expired');
        }
  
        const userId = recoveryRecord.user_id;
  
        // Update only the password field
        const updatedUser = await prisma.user.update({
          where: {
            id: userId,
          },
          data: {
            password: password,
            updated_at: timestamp,
          },
        });
  
        // Delete recovery record
        await prisma.account_recovery.delete({
          where: { token },
        });
  
        return res.status(200).json(updatedUser);
  
    } catch (err) {
      console.log(err);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
