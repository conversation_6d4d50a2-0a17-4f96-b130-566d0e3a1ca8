import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    try {
      const topLevelCategories = await prisma.category.findMany({
        where: {
          parent_id: -1,
        },
        select: {
          id: true,
          name: true,
        },
      });
      console.log(topLevelCategories);
      return res.status(200).json(topLevelCategories);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
