import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const id = Number(req.query.id);
    try {
      const mainCategories = await prisma.category.findMany({
        where: {
          parent_id: id,
          disabled: 0,
        },
        select: {
          id: true,
          name: true,
          parent_id: true,
        },
      });

      console.log(mainCategories);

      for (const item in mainCategories) {
        const sub = await prisma.category.findMany({
          where: {
            parent_id: mainCategories[item].id,
            disabled: 0,
          },
          select: {
            id: true,
            name: true,
          },
        });
        mainCategories[item].sub = sub;
      }

      // for (const item in mainCategories) {
      //   const sub = await prisma.category.findMany({
      //     where: {
      //       parent_id: mainCategories[item].id,
      //       disabled: 0,
      //     },
      //     select: {
      //       id: true,
      //       name: true,
      //     },
      //   });
      //   mainCategories[item].sub = sub;
      // }

      console.log(mainCategories);
      return res.status(200).json(mainCategories);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
