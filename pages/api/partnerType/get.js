import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    try {
      const items = await prisma.PartnerType.findMany();

      // Map the results and rename the columns
      const formattedItems = items.map((item) => ({
        value: item.id,
        label: item.name,
        code: item.code,
      }));

      return res.status(200).json({ formattedItems });
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
