import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    try {
      const message = await prisma.globalMessages.findFirst({
        where: {
          on: 1,
        }
      });
      if (!message) {
        return res.status(404).json({ message: "No global message found." });
      }

      return res.status(200).json(message);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
