import prisma from "./prisma";

// export default async (req, res) => {
//   if (req.method === "GET") {
//     try {
//       const products = await prisma.product.findMany({
//         include: {
//           ProductCategory: true
//         }
//       });
//       return res.status(200).send(products);
//     } catch (err) {
//       return res.status(503).json({ err: err.toString() });
//     }
//   }
// };
export default async (req, res) => {
  if (req.method === "GET") {
    const { sku } = req.query;

    try {
      // product
      const product = await prisma.Product.findMany({
        where: {
          sku: sku,
        },
        include: {
          ProductImage: true,
          ProductCategory: true,
        },
      });

      console.log(product);
      console.log('catid', product[0].ProductCategory[0].category_id);

      let route = [];

      const category_id = product[0].ProductCategory[0].category_id;

      // product
      const category = await prisma.category.findFirst({
        where: {
          id: category_id,
        }
      });

      route.push({
        name: category.name,
        parent_id: category.parent_id,
        id: category.id,
      });

      console.log("First Parent ID", route[0].parent_id);

      if (route[0].parent_id != -1) {
        let parent = await prisma.Category.findMany({
          where: {
            id: Number(route[0].parent_id),
          },
          select: {
            name: true,
            parent_id: true,
            id: true,
          },
        });

        console.log("parent", parent);

        route.unshift(parent[0]);
      }


      if (route[0].parent_id != -1) {
        let parent = await prisma.Category.findMany({
          where: {
            id: Number(route[0].parent_id),
          },
          select: {
            name: true,
            parent_id: true,
            id: true
          },
        });

        console.log("parent", parent);

        route.unshift(parent[0]);
      }

      console.log("route", route);

      return res.status(200).json({ product, route });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
