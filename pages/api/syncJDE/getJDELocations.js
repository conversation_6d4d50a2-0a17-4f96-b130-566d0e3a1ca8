import prisma from './../prisma';
import axios from "axios";


const cleanUserData = (data) => {
  for (const key in data) {
    if (typeof data[key] === "string") {
      data[key] = data[key].trim();
    }
  }
  return data;
};

export default async (req, res) => {
  const now = new Date();
  try {
    const { account_id } = req.body;

    const account = await prisma.account.findFirst({
      where: {
        id: account_id,
      },
      select: {
        jde_id: true,
      },
    });

    const response = await axios({
      method: "POST",
      url: process.env.JDE_API_URL + `/Account/SyncJDE/${account.jde_id}`,
      data: {
        action: "find",
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    });
    const addresses = response.data.address;
    const addressArray = [];

    for (let i = 0; i < addresses.length; i++) {
      const item = cleanUserData(addresses[i]);
      addressArray.push(item);
    }

    return res.status(200).json(addressArray);
  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};
