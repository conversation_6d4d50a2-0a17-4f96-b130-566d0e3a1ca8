import prisma from "./../prisma";
import axios from "axios";

export default async (req, res) => {
  const { id, contacts } = req.body;

  console.log("id", id);
  console.log(contacts);

  const now = new Date();

  const contactsArray = Array.isArray(contacts) ? contacts : [contacts];

  const responses = await Promise.all(
    contactsArray.map(async (contact) => {
      try {
        const response = await axios({
          method: "post",
          //staging
          url: process.env.JDE_API_URL + `/Contact/SyncJDE/${contact.id}`,
          //local
          // url: `http://localhost/oasisv2-api/Account/SyncJDE/${account_id}`,
          data: {
            action: "add",
          },
          headers: {
            "Content-Type": "application/json",
            Token:
              process.env.API_TOKEN,
            WebAppId: 1,
          },
        });
        return response.data;
      } catch (error) {
        console.error(`An error occurred for contact id ${contact.id}:`, error);
        return { error: `Error for contact id ${contact.id}` };
      }
    })
  );

  return res.status(200).json(responses);
};
