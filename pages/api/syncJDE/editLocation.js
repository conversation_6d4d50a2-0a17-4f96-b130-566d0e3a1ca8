import axios from "axios";

export default async (req, res) => {
  try {
    const { id } = req.body;

    console.log("location_id", id);


    const response = await axios.post(
      process.env.JDE_API_URL + `/Locations/SyncJDE/${id}`,
      {
        action: "update",
      },
      {
        headers: {
          "Content-Type": "application/json",
          Token:
            process.env.API_TOKEN,
          WebAppId: 1,
        },
      }
    );

    console.log(response.data);
    return res.status(200).json(response.data);
  } catch (error) {
    console.error("An unexpected error occurred:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
