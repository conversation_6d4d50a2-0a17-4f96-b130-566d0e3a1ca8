import axios from "axios";

export default async (req, res) => {
  try {
    const response = await axios({
      method: "post",
      url: process.env.JDE_API_URL + `/Invoice/PostOrderProcess`,
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    });
    console.log("status", response.status);

    if (response.status == 200) {
      return res.status(200).json(response.data);
    }
  } catch (error) {
    console.error(
      "An unexpected error while running the Post Order Process has occurred: ",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};
