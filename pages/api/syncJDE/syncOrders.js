import axios from "axios";
import { group } from "console";


const syncOrders = async (req, res) => {

  const { account_id, location_id, group_id } = req.body;

  try {
    console.log("syncOrders account_id", account_id);
    console.log("syncOrders location_id", location_id);
    console.log("syncOrders group_id", group_id);

    if (!account_id && (group_id === 1 || group_id === 3)) {
      return res.status(404).json({ error: "No Account found" });
    } else if (!location_id && (group_id === 2 || group_id === 4)){
      return res.status(404).json({ error: "No Location found" });
    }

    let acc_loc_id = account_id;
    let childAcct = false;
    if (group_id === 2 || group_id === 4) {
      console.log("syncOrders by Location", location_id);
      acc_loc_id = location_id;
      childAcct = true;
    }  
    await axios({
      method: "POST",
      //Staging
      // url: `https://oasisv2admin.fusionofideas.com/api/Account/SyncJDE/${account_id}`,
      //Local
      url: process.env.JDE_API_URL + `/Order/UpdateStatus/${acc_loc_id}`,
      data: {
        child: childAcct,
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    })
      .then(async (response) => {
        if (response.status == 200) {
          console.log('sync Orders Response', response.data);
          return res.status(200).json({ message: "Orders Updated" });
        }
      })
      .catch((error) => {
        console.log(error);

        return res.status(500).json({ message: "Syncing Error" });
      });

    // return res
    //   .status(200)
    //   .json({ message: "Account Synced" });
  } catch (error) {
    console.error("An unexpected error while updating orders occurred:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};


export default syncOrders;