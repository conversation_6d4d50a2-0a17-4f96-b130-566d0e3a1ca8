import prisma from './../prisma';
import axios from "axios";


const restoreOrderHistory = async (req, res) => {
    const { account_id } = req.body;
    try {
      const account = await prisma.account.findUnique({
        where: {
          id: account_id,
        },
      });

      if (!account) {
        return res.status(404).json({ error: "Error: Account not found" });
      }
        await axios({
        method: "POST",
        url: process.env.JDE_API_URL + `/Invoice/SyncJDE/${account_id}`,
        data: {
            action: "restoreOrders",
        },
        headers: {
            "Content-Type": "application/json",
            Token:
            process.env.API_TOKEN,
            WebAppId: 1,
        },
        })
        .then(async (response) => {
            if (response.status == 200) {
            console.log('restore history response', response.data);
            return res.status(200).json({ message: "Order History Restored" });
            }
        })
        .catch((error) => {
            console.log(error);

            return res.status(500).json({ message: "Restoring Error" });
        });

    } catch (error) {
        console.error("An unexpected error while restoring order history occurred:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};


export default restoreOrderHistory;