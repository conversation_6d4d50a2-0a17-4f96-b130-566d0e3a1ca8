import prisma from "./../prisma";
import axios from "axios";

export default async (req, res) => {
  const now = new Date();
  try {
    const account_id = req.body;
    console.log(account_id);

    const response = await axios({
      method: "post",
      //staging
      url: process.env.JDE_API_URL + `/Account/SyncJDE/${account_id}`,
      //local
      // url: `http://localhost/oasisv2-api/Account/SyncJDE/${account_id}`,
      data: {
        action: "add",
        // action: "findByAccount",
      },
      headers: {
        "Content-Type": "application/json",
        Token: process.env.API_TOKEN,
        WebAppId: 1,
      },
    });
    console.log("status", response.status);

    if (response.status == 200) {
      let jde_id = response.data.jde_id;

      try {
        const locUpdate = prisma.locations.updateMany({
          where: {
            account_id: Number(account_id),
          },
          data: {
            jde_id: jde_id,
          },
        });
  
        const results = await locUpdate;
        console.log("Add Account location update results", results)
        console.log("Add Account location update status", results.status)
        return res.status(response.status).json(response.data);
  
        // 'results' will contain the results of both update operations.
      } catch (error) {
        console.error(
          "An unexpected error while updating Account occurred:",
          error
        );
        res.status(500).json({ error: "Internal server error" });
      }
    }


  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};
