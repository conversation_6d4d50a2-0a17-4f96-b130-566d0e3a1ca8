import prisma from './../prisma';
import axios from "axios";


const syncOfflineOrders = async (req, res) => {

    const { email } = req.body;
    try {
      const user = await prisma.user.findUnique({
        where: {
            email: email,
        },
        select: {
            id: true,
        },
      });

      if (!user) {
        return res.status(404).json({ error: "User not found" });
      }

        await axios({
        method: "POST",
        url: process.env.JDE_API_URL + `/Invoice/SyncJDE/${user.id}`,
        data: {
            action: "offlineSync",
        },
        headers: {
            "Content-Type": "application/json",
            Token:
            process.env.API_TOKEN,
            WebAppId: 1,
        },
        })
        .then(async (response) => {
            if (response.status == 200) {
            console.log('sync offline response', response.data);
            return res.status(200).json({ message: "Orders Restored" });
            }
        })
        .catch((error) => {
            console.log(error);

            return res.status(500).json({ message: "Syncing Error" });
        });

    } catch (error) {
        console.error("An unexpected error while restoring orders occurred:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};


export default syncOfflineOrders;