import prisma from './../prisma';
import axios from "axios";


const syncReturns = async (req, res) => {

    const { user_id } = req.body;
    try {

        await axios({
        method: "POST",
        url: process.env.JDE_API_URL + `/ReturnForm/SyncJDE/${user_id}`,
        data: {
            action: "update",
        },
        headers: {
            "Content-Type": "application/json",
            Token:
            process.env.API_TOKEN,
            WebAppId: 1,
        },
        })
        .then(async (response) => {
            if (response.status == 200) {
            console.log('sync return response', response.data);
            return res.status(200).json({ message: "Returns Updated" });
            }
        })
        .catch((error) => {
            console.log(error);

            return res.status(500).json({ message: "Syncing Error" });
        });

    } catch (error) {
        console.error("An unexpected error while updating returns occurred:", error);
        res.status(500).json({ error: "Internal server error" });
    }
};


export default syncReturns;