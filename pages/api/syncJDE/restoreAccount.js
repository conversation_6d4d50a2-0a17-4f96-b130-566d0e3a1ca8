// import prisma from "./../prisma";

// export default async (req, res) => {
//   const { account_id, jde_id } = req.body;

//   try {
//     const locUpdate = prisma.locations.update({
//       where: {
//         account_id: Number(account_id),
//         default: 1,
//       },
//       data: {
//         jde_id: jde_id,
//       },
//     });

//     const results = await locUpdate;
//     console.log("Restore location update results", results);
//     console.log("Restore location update status", results.status);
//     return res.status(response.status).json(response.data);

//     // 'results' will contain the results of both update operations.
//   } catch (error) {
//     console.error(
//       "An unexpected error while restoring child accounts occurred:",
//       error
//     );
//     res.status(500).json({ error: "Internal server error" });
//   }
// };
