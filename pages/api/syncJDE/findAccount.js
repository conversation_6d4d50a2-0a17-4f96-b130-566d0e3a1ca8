import prisma from "./../prisma";
import axios from "axios";

const cleanUserData = (data) => {
  if (typeof data === "string") {
    return data.trim();
  } else if (Array.isArray(data)) {
    // Ensure the array contains cleaned items
    return data.map((item) => cleanUserData(item));
  } else if (typeof data === "object" && data !== null) {
    const cleanedObject = {};

    for (const key in data) {
      if (key === "electronicAddress") {
        // If 'electronicAddress' is an object, clean it and convert it to an array
        cleanedObject[key] = Array.isArray(data[key])
          ? data[key].map((item) => cleanUserData(item))
          : typeof data[key] === "string"
          ? cleanUserData(data[key]) // Check if the inner field is a string
          : [cleanUserData(data[key])];
      } else {
        cleanedObject[key] = cleanUserData(data[key]);
      }
    }

    return cleanedObject;
  } else {
    return data;
  }
};




export default async (req, res) => {
  const now = new Date();
  try {
    const { email, account_id, zip } = req.body;

    // See if account already exists in DB
    const account = await prisma.account.findFirst({
      where: {
        jde_id: account_id,
      },
    });

    if (account) {
      return res.status(500).json("Account already restored. Please login");
    }
    // return res.status(500).json({account_id: account_id});

    const GetContactCheck = await axios({
      method: "POST",
      url: process.env.JDE_API_URL + `/Contact/SyncJDE/${account_id}`,
      data: {
        action: "find",
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    });

    // Iterate through all objects and find the one with the matching email
    let contactsArray;

    if (Object.keys(GetContactCheck.data).every(key => !isNaN(parseInt(key)))) {
      // Response object has numeric keys
      contactsArray = Object.values(GetContactCheck.data);
    } else {
      // Response object does not have numeric keys
      contactsArray = [GetContactCheck.data];
    }
    // const contactsArray = Object.values(GetContactCheck.data);
    let matchingContact;
    for (const contact of contactsArray) {
      // console.log(contact);
      const cleanedContact = cleanUserData(contact);
      const electronicAddress = cleanedContact.electronicAddress;
      // console.log(cleanedContact);


      if (electronicAddress &&
        electronicAddress.some(
          (item) =>
            item.electronicAddressTypeCode === "E" &&
            item.electronicAddress.toLowerCase().includes(email.toLowerCase())
        )
      ) {
        matchingContact = cleanedContact;
        break;
      }
    }

    if (!matchingContact) {
      return res.status(500).json("Email Not Found");
    }

    const electronicAddresses = matchingContact.electronicAddress;
    let emailJDE;
    let websiteJDE;
    let firstNameJDE = matchingContact.giveName;
    let lastNameJDE = matchingContact.surname;


    emailJDE = matchingContact.electronicAddress
    .find(
      (item) =>
        item.electronicAddressTypeCode === "E" &&
        item.electronicAddress.toLowerCase().includes(email.toLowerCase())
    )
    ?.electronicAddress;

    websiteJDE = matchingContact.electronicAddress
      .find((item) => item.electronicAddressTypeCode === "I")
      ?.electronicAddress;

    if (emailJDE) {
      const response = await axios({
        method: "POST",
        url: process.env.JDE_API_URL + `/Account/SyncJDE/${account_id}`,
        data: {
          action: "find",
        },
        headers: {
          "Content-Type": "application/json",
          Token:
            process.env.API_TOKEN,
          WebAppId: 1,
        },
      });
      const { address, categoryCodesAddressBook, electronicAddresses,entityTypeCode } =
        response.data;

      const categoryCodeJDE = categoryCodesAddressBook.categoryCode010;
      const partnerType = categoryCodesAddressBook.categoryCode001.trim();
      const entityType = entityTypeCode.trim();
      const zipJDE = address.postalCode.substring(0, 5);

      let websiteJDE;

      if (electronicAddresses) {
        // console.log("electronicAddresses set");
        // for (let i = 0; i < electronicAddresses.length; i++) {
        websiteJDE = matchingContact.electronicAddress
          .find((item) => item.electronicAddressTypeCode === "I")
          ?.electronicAddress;
        // }
      }

      //REMOVE WEB REQUIREMENT -- 11/20/2023
      // if (zipJDE === zip && categoryCodeJDE === "WEB") {
      if (zipJDE === zip && entityType === "C" && partnerType != "CNS") {
        response.data.emailJDE = emailJDE;
        response.data.websiteJDE = websiteJDE;
        response.data.firstName = firstNameJDE;
        response.data.lastName = lastNameJDE;

        const childCheck = await axios({
          method: "POST",
          url: process.env.JDE_API_URL + `/Account/SyncJDE/${account_id}`,
          data: {
            action: "child",
          },
          headers: {
            "Content-Type": "application/json",
            Token:
              process.env.API_TOKEN,
            WebAppId: 1,
          },
        });

        if (childCheck.data) {
          response.data.childLocations = childCheck.data;
        } else {
          console.log("No Child Accounts");
        }

        return res.status(response.status).json(response.data);
      } else {
        return res.status(500).json("Account Not Found");
      }
    } else {
      return res.status(500).json("Email Not Found");
    }
  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    res.status(500).json("No Account Found");
  }
};
