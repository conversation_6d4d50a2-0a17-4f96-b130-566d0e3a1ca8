// syncAll.js
import syncAccount from "./syncAccount";
import syncOrders from "./syncOrders";

export default async (req, res) => {
  try {
    // Call syncAccount function
    await syncAccount(req, res);

    // Call syncOrders function
    await syncOrders(req, res);

    // If both functions complete without errors, return a success message
    return res.status(200).json({ message: "Sync completed successfully" });
  } catch (error) {
    console.error("An unexpected error occurred:", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
