import axios from "axios";



export default async (req, res) => {
  try {
    const { id } = req.body;

    console.log("location_id", id);
    await axios({
      method: "POST",
      //Staging
      // url: `https://oasisv2admin.fusionofideas.com/api/Account/SyncJDE/${account_id}`,
      //Local
      url: process.env.JDE_API_URL + `/Locations/SyncJDE/${id}`,
      data: {
        action: "add",
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    })
      .then((response) => {
        console.log(response);
        return res.status(200).json(response.data);
      })
      .catch((error) => console.log(error));

    // return res.status(200).json();
  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};
