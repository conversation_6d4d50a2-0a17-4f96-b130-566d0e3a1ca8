import prisma from "./../prisma";
import axios from "axios";

const syncAccount = async (req, res) => {
  const { account_id } = req.body;
  try {
    if (!account_id) {
      return res.status(404).json({ error: "Account not found" });
    }
    await axios({
      method: "POST",
      //Staging
      // url: `https://oasisv2admin.fusionofideas.com/api/Account/SyncJDE/${account_id}`,
      //Local
      url: process.env.JDE_API_URL + `/Account/SyncJDE/${account_id}`,
      data: {
        action: "sync",
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    })
      .then(async (response) => {
        if (response.status == 200) {
          console.log(response.data);

          const accountData = response.data;

          if (accountData.oasis_rep_id !== accountData.territories_rep_id) {
            const setRepId = await prisma.account.update({
              where: {
                id: accountData.id,
              },
              data: {
                oasis_rep_id: accountData.territories_rep_id,
              },
            });
            return res.status(response.status).json(setRepId);
          } else {
            return res.status(response.status).json(response.data);
          }
        }
      })
      .catch((error) => {
        console.log(error);

        return res.status(500).json({ message: "Syncing Error" });
      });

    // return res
    //   .status(200)
    //   .json({ message: "Account Synced" });
  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    res.status(500).json({ error: "Internal server error" });
  }
};

export default syncAccount;
