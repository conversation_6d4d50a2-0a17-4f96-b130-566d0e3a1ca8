import axios from "axios";

export default async (req, res) => {
  try {
    // const contact_id = req.body;
    const { id } = req.body;

    await axios({
      method: "POST",
      url: process.env.JDE_API_URL + `/Contact/SyncJDE/${id}`,
      data: {
        action: "add",
      },
      headers: {
        "Content-Type": "application/json",
        Token:
          process.env.API_TOKEN,
        WebAppId: 1,
      },
    })
      .then((response) => {
        if (response.status == 200) {
          console.log(response);
          return res.status(200).json(response.data);
        }else{
          return res.status(response.status).json(response.data);
        }
      })
      .catch((error) => {
        console.log(error);
        return res.status(500).json({ error });
      });
  } catch (error) {
    console.error(
      "An unexpected error while updating Account occurred:",
      error
    );
    return res.status(500).json({ error: "Internal server error" });
  }
};
