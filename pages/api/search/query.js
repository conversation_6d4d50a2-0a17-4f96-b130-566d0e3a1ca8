import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const { q } = req.query;

    console.log(q);

    try {
      const query = await prisma.product.findMany({
        where: {
          sku: {
            search: q,
          },
          name: {
            search: q,
          },
          description: {
            search: q,
          },
          disabled: 0,
        },
        include: {
          ProductImage: true,
        },
      });

      // const result = JSON.parse(
      //   JSON.stringify(query).split('"ProductImage"').join('"images"')
      // );

      console.log(query);

      res.status(200).json({ products: query });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
