import prisma from "../prisma";
import axios from "axios";

export default async (req, res) => {
  console.log("test");
  if (req.method === "POST") {
    const { id, qty, promotions, account_jde } = req.body;

    console.log(req.body);

    try {
      // product
      const existingCartEntry = await prisma.cart.findUnique({
        where: { id: Number(id) },
      });

      if (!existingCartEntry) {
        return res.status(404).json({ message: "Cart entry not found." });
      }

      // Fetch the original price using the product ID
      const originalPriceResponse = await axios.post(
        `${process.env.JDE_API_URL}/Product/GetStockPrice/${existingCartEntry.product_id}`,
        {
          account_jde: account_jde,
        },
        {
          headers: {
            "Content-Type": "application/json",
            Token: process.env.API_TOKEN,
            WebAppId: 1,
          },
        }
      );

      const originalPrice = originalPriceResponse.data.price;

       // Promotion logic remains unchanged...
       function checkPromotionConditions(productId, quantity, promotion) {
        for (const condition of promotion.Conditions) {
          if (condition.type === 'product_quantity') {
            const productInCondition =
              condition.applicableProducts.length === 0 ||
              condition.applicableProducts.some(product => Number(product.product_id) === Number(productId));
            if (!productInCondition || quantity < condition.threshold) {
              return false;
            }
          }
          if (condition.type !== 'product_quantity') {
            return false;
          }
        }
        return true;
      }

      function checkApplicablePromotions(productId, quantity, promotions) {
        return promotions.filter(
          promotion =>
            promotion.promotion_type === 'product_specific' &&
            promotion.discount_type === 'fixed_price' &&
            checkPromotionConditions(productId, quantity, promotion )
        );
      }

      function getFinalPrice(productId, quantity, promotions) {
        const applicablePromotions = checkApplicablePromotions(productId, quantity, promotions);
        let finalPrice = originalPrice; // Start with the original price
        applicablePromotions.forEach(promo => {
          const promoPrice = promo.discount_value;
          
          if (Number(promoPrice) < Number(finalPrice)) {
            finalPrice = promoPrice; // Use the promotional price if lower
          }
        });
        return finalPrice;
      }

      const finalPrice = getFinalPrice(existingCartEntry.product_id, qty, promotions);


      const updateQty = await prisma.cart.update({
        where: {
          id: Number(id),
        },
        data: {
          qty: Number(qty),
          price: finalPrice, // Update to promotional price if conditions are met
        },
      });

      const response = updateQty;

      return res.status(200).json({
        response,
      });
    } catch (err) {
      console.log(err.message);
      return res
        .status(503)
        .json({ message: "Something went wrong. Please try again." });
    }
  }
};
