import prisma from './../prisma';

export default async (req, res) => {
  console.log("test");
  if (req.method === "DELETE") {
    const { id } = req.body;

    console.log(req.body);

    try {
      // product

      const removeItem = await prisma.cart.delete({
        where: {
          id: Number(id),
        },
      });

      return res.status(200).json({
        status: 1,
        message: "Product has been removed to your cart.",
        removeItem,
      });
    } catch (err) {
      console.log(err.message);
      return res
        .status(503)
        .json({ message: "Something went wrong. Please try again." });
    }
  }
};
