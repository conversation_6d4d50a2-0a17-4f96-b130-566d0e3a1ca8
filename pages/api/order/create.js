import prisma from "./../prisma";
import axios from "axios";

export default async (req, res) => {
  const now = new Date();

  if (req.method === "POST") {
    console.log("creating order...");

    const { cart_selections, cart, checkoutForm, total, user, discount } =
      req.body;

    console.log(cart_selections);
    console.log(cart);
    console.log(checkoutForm);
    console.log(total);
    console.log(discount);
    console.log(user);

    let auto_delivery_date;

    if (checkoutForm.auto_delivery !== 0) {
      const today = new Date(now);
      if (checkoutForm.delivery_interval == 1) {
        today.setMonth(now.getMonth() + 1);
        auto_delivery_date = today;
      } else if (checkoutForm.delivery_interval == 2) {
        today.setMonth(now.getMonth() + 2);
        auto_delivery_date = today;
      } else if (checkoutForm.delivery_interval == 3) {
        today.setMonth(now.getMonth() + 3);
        auto_delivery_date = today;
      }
    }

    // console.log("auto_delivery_date", auto_delivery_date);
    // return;

    // authorize payment
    const cardpointeAuth = `Basic ${Buffer.from(`${process.env.CARDPOINTE_USER}:${process.env.CARDPOINTE_PASS}`).toString('base64')}`;

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: cardpointeAuth,
      },
    };

    const cardInfo = {
      merchid: process.env.CARDPOINTE_MERCH_ID,
      account: checkoutForm.payment_token,
      expiry: checkoutForm.payment_expiry,
      postal: checkoutForm.payment_postal,
      amount: (Math.round(total * 100) / 100).toFixed(2),
      currency: "USD",
      name: checkoutForm.payment_name,
    };

    const products = cart.map((x) => {
      return {
        product_id: x.product_id,
        qty: x.qty,
        price: x.price,
        // last_status_code: '520',
        // next_status_code: '530',
      };
    });

    axios
      .put(
        `${process.env.CARDPOINTE_URL}/cardconnect/rest/auth`,
        cardInfo,
        config
      )
      .then(async function (payment) {
        if (payment.data.respstat === "A") {
          try {
            const order = await prisma.order
              .create({
                data: {
                  order_by: user.id,
                  shipping_address_id: checkoutForm.shipping_id,
                  account_id: user.account_id,
                  jde_id: "0",
                  date: now,
                  transaction_id: 0,
                  authcode: payment.data.authcode,
                  retref: payment.data.retref,
                  payment_id: checkoutForm.payment_id,
                  billing_id: checkoutForm.billing_id,
                  tax: checkoutForm.tax,
                  shipping: checkoutForm.shipping_cost,
                  discount: discount,
                  total: (Math.round(total * 100) / 100).toFixed(2),
                  authorized_amount: (Math.round(total * 100) / 100).toFixed(2),
                  tracking: "",
                  shipping_method: checkoutForm.shipping_method,
                  status: 1,
                  type: "web",
                  order_placed_by: checkoutForm.order_placed_by,
                  po_num: checkoutForm.po_num,
                  surgeon_names: checkoutForm.surgeon_names,
                  auto_delivery: checkoutForm.auto_delivery,
                  auto_freq: checkoutForm.delivery_interval,
                  auto_start_date: auto_delivery_date,
                  cancel_auto: 0,
                  pause_auto: 0,
                  recurred: 0,
                  parent_id: 0,
                  require_rep_consulting: Number(
                    checkoutForm.require_rep_consulting
                  ),
                  created_at: now,
                  updated_at: now,
                  OrderedProduct: {
                    create: products,
                  },
                },
              })
              .then(async (order) => {
                console.log("order_id", order.id);
                await axios({
                  method: "post",
                  //staging
                  url: process.env.JDE_API_URL + `/Order/SyncJDE/${order.id}`,
                  //local
                  // url: `http://localhost:8888/oasisv2-api/Account/SyncJDE/${account.id}`,
                  data: {
                    action: "add",
                  },
                  headers: {
                    "Content-Type": "application/json",
                    Token:
                      process.env.API_TOKEN,
                    WebAppId: 1,
                  },
                });

                axios({
                  method: "post",
                  url: process.env.JDE_API_URL + `/Order/ConfirmationEmail/${order.id}`,
                  headers: {
                    "Content-Type": "application/json",
                    Token: process.env.API_TOKEN,
                    WebAppId: 1,
                  },
                })
              })
              .then(async function (response) {
                console.log(response);
                await prisma.cart.deleteMany({
                  where: {
                    user_id: user.userId,
                    account_id: user.accountId,
                  },
                });
              });

            res
              .status(200)
              .json({ message: payment.data.resptext, status: "A" });
          } catch (err) {
            console.log("Try Error", err);
            return res.status(500).json({
              message:
                "Error: Unable to add order to database. Please try again.",
              status: "C",
              err: err,
            });
          }
        } else if (payment.data.respstat === "B") {
          console.log(payment.data);
          return res
            .status(payment.status)
            .json({ message: payment.data.resptext, status: "B" });
        } else if (payment.data.respstat === "C") {
          console.log(payment.data);
          return res
            .status(payment.status)
            .json({ message: payment.data.resptext, status: "C" });
        } else {
          console.log(payment.data);
          return res
            .status(payment.status)
            .json({ message: payment.data.resptext, status: "F" });
        }
      });
  }
};
