import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const { info } = JSON.parse(req.body);
    try {
      // fetch order by it's id

      try {
        const item = await prisma.order.create({
          data: {
            orderId: info.orderId,
            paymentId: info.paymentId,
            billingId: info.billingId,
            productIds: info.productIds,
            locationId: info.locationId,
            accountId: info.accountId,
          },
        });

        return res.status(200).json({ item });
      } catch (err) {
        const code = err.code;
        if (code === "") {
          return res.status(500).json({ err: "" });
        }
        return res.status(500).json({ err: err });
      }

      return res.status(200).json({ item });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
