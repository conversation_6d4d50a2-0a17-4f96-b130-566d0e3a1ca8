import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    try {
      const userId = Number(req.query.userId);
      const groupId = Number(req.query.groupId);
      const locationId = Number(req.query.locationId);
      const accountId = Number(req.query.accountId);
      const limit = req.query.limit ? parseInt(req.query.limit, 10) : 100; // Parse limit as an integer or leave it null if not provided


      let orders;

      if (groupId === 1) {
        // Super Admin has access to all orders across all locations.
        orders = await prisma.order.findMany({
          where: {
            account_id: accountId,
            jde_id: {
              not: "", // Exclude orders with empty jde_id
              gt: "0", // Exclude orders with jde_id less than or equal to 0
            },
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations:true,
            User:true,
          },
          orderBy: {
            date: 'desc' // Sort by date in descending order
          },
          take: limit,
        });
      } else if (groupId === 2) {
        // Admin has access to orders within their specified location (locationId).
        orders = await prisma.order.findMany({
          where: {
            account_id: accountId,
            shipping_address_id:locationId,
            jde_id: {
              not: "", // Exclude orders with empty jde_id
              gt: "0", // Exclude orders with jde_id less than or equal to 0
            },
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations:true,
            User:true,
          },
          orderBy: {
            date: 'desc' // Sort by date in descending order
          },
          take: limit,
        });
      } else if (groupId === 3) {
        // Accounting has access to all order and invoice information across all locations.
        orders = await prisma.order.findMany({
          where: {
            account_id: accountId,
            jde_id: {
              not: "", // Exclude orders with empty jde_id
              gt: "0", // Exclude orders with jde_id less than or equal to 0
            },
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations:true,
            User:true,
          },
          orderBy: {
            date: 'desc' // Sort by date in descending order
          },
          take: limit,
        });

        // You may also fetch invoice information here if needed.
      } else if (groupId === 4) {

        const whereClause = {
          account_id: accountId,
          order_by: userId,
          jde_id: {
            not: "", // Exclude orders with empty jde_id
            gt: "0", // Exclude orders with jde_id less than or equal to 0
          },
        };
      
        // Check if locationId is defined (not null or undefined) before including it in the query
        if (locationId) {
          whereClause.shipping_address_id = locationId;
        }
      
        orders = await prisma.order.findMany({
          where: whereClause,
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations:true,
            User: true,
          },
          orderBy: {
            date: 'desc' // Sort by date in descending order
          },
          take: limit,
        });
        // Standard users can only place orders (possibly limited to a location).
        // Implement logic for Standard users as needed.
      } else {
        return res.status(403).json({ error: "Unauthorized" });
      }
      orders.sort((a, b) => parseInt(b.jde_id) - parseInt(a.jde_id));

      return res.status(200).json(orders);
    } catch (err) {
      return res.status(503).json({ error: err.toString() });
    }
  }
};
