import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === "GET") {
    // const { info } = JSON.parse(req.body);
    try {
      const { startDate, endDate } = req.query;
      const userId = Number(req.query.userId);
      const groupId = Number(req.query.groupId);
      const locationId = Number(req.query.locationId);
      const accountId = Number(req.query.accountId);
      let orders;

      // Fetch the date of the 10th most recent order
      const tenthMostRecentOrder = await prisma.order.findMany({
        where: {
          account_id: accountId,
          status: {
            lt: 4, // excludes orders that have been delivered
          },
          jde_id: {
            not: "", // Exclude orders with empty jde_id
            gt: "0", // Exclude orders with jde_id less than or equal to 0
          },
        },
        orderBy: {
          date: "desc", // Sort by date in descending order
        },
        take: 10, // Take the 10th most recent orders
      });

      // Use the 10th order's date or the oldest available order
      let newStartDate;
      if (tenthMostRecentOrder.length === 10) {
        newStartDate = tenthMostRecentOrder[9].date;
      } else if (tenthMostRecentOrder.length > 0) {
        newStartDate = tenthMostRecentOrder[tenthMostRecentOrder.length - 1].date;
      } else {
        newStartDate = new Date(); // Default to the current date if no orders found
      }
      console.log("start date", newStartDate);
      // console.log("startDate", startDate);

      if (startDate != 0) {
        newStartDate = startDate;
      }

      console.log("newStartDate", newStartDate);

      if (groupId === 1 || groupId === 3) {
        // Super Admin has access to all orders across all locations.
        orders = await prisma.order.findMany({
          where: {
            account_id: accountId,
            date: {
              gte: new Date(newStartDate), // Filter orders created on or after startDate
              lte: new Date(endDate), // Filter orders created on or before endDate
            },
            jde_id: {
              not: "", // Exclude orders with empty jde_id
              gt: "0", // Exclude orders with jde_id less than or equal to 0
            },
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations: true,
            User: true,
          },
          orderBy: {
            date: "desc", // Sort by date in descending order
          },
        });
      } else if (groupId === 2) {
        orders = await prisma.order.findMany({
          where: {
            account_id: accountId,
            shipping_address_id: locationId,
            date: {
              gte: new Date(newStartDate), // Filter orders created on or after startDate
              lte: new Date(endDate), // Filter orders created on or before endDate
            },
            jde_id: {
              not: "", // Exclude orders with empty jde_id
              gt: "0", // Exclude orders with jde_id less than or equal to 0
            },
          },
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations: true,
            User: true,
          },
          orderBy: {
            date: "desc", // Sort by date in descending order
          },
        });
      } else if (groupId === 4) {
        const whereClause = {
          account_id: accountId,
          order_by: userId,
          date: {
            gte: new Date(newStartDate), // Filter orders created on or after startDate
            lte: new Date(endDate), // Filter orders created on or before endDate
          },
          jde_id: {
            not: "", // Exclude orders with empty jde_id
            gt: "0", // Exclude orders with jde_id less than or equal to 0
          },
        };

        // Check if locationId is defined (not null or undefined) before including it in the query
        if (locationId) {
          whereClause.shipping_address_id = locationId;
        }
        orders = await prisma.order.findMany({
          where: whereClause,
          include: {
            Billing: true,
            Payment: true,
            OrderedProduct: {
              include: {
                Product: {
                  include: {
                    ProductImage: true,
                  },
                },
              },
            },
            Locations: true,
            User: true,
          },
          orderBy: {
            date: "desc", // Sort by date in descending order
          },
        });
      } else {
        return res.status(403).json({ error: "Unauthorized" });
      }

      const updatedOrders = [];

      for (const order of orders) {
        if (order.Billing) {
          const locationId = order.Billing.id;

          // Fetch the location data using Prisma
          const location = await prisma.locations.findFirst({
            where: { id: locationId },
          });

          if (location) {
            // Replace the Billing data with the new location data
            order.Billing = location;
          }
        }

        // Add the modified order to the updatedOrders array
        updatedOrders.push(order);
      }

      orders = updatedOrders;
      orders.sort((a, b) => parseInt(b.jde_id) - parseInt(a.jde_id));


      // console.log("orders", orders);
      return res.status(200).json({ orders, newStartDate });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
