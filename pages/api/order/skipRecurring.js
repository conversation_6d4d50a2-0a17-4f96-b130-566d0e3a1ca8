import prisma from "../prisma";
import axios from "axios";

export default async (req, res) => {
  if (req.method === "POST") {
    const { id } = req.body;

    try {
      // Update the `pause_auto` field for the order
      const now = new Date();

      const response = await prisma.order.update({
        where: {
          id: Number(id),
        },
        data: {
          pause_auto: 1,
          auto_pause_date: now,
        },
      });

      // Send confirmation email
      const emailResponse = await axios({
        method: "post",
        url: `${process.env.JDE_API_URL}/Order/RecurringEmails/${id}`,
        data: {
            action: "skip",
          },
        headers: {
          "Content-Type": "application/json",
          Token: process.env.API_TOKEN,
          WebAppId: 1,
        },
      });

      return res.status(200).json({
        response,
        emailResponse: emailResponse.data,
      });
    } catch (err) {
      console.error(err.message);
      return res.status(503).json({
        message: "Something went wrong. Please try again.",
        error: err.message,
      });
    }
  } else {
    return res.status(405).json({ message: "Method not allowed" });
  }
};
