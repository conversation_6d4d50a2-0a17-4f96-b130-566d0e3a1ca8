import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method !== "POST") {
    return res.status(405).json({ message: "Method not allowed" });
  }

  try {
    const { cart, checkoutForm, user } =
      req.body;


    // Fetch all auto_delivery orders for the account
    const allOrders = await prisma.order.findMany({
      where: {
        account_id: user.account_id,
        auto_delivery: 1,
      },
      include: {
        OrderedProduct: true, // Include products for comparison
      },
    });

    const currentOrderDay = new Date().getDate();
    for (const order of allOrders) {
    // check shipping location
    if (checkoutForm.shipping_id !== order.shipping_address_id) {
        continue;
      } 

      const orderDay = order.auto_start_date
      ? new Date(order.auto_start_date).getUTCDate()
      : null;
      // Check if the day of `auto_start_date` matches
      if (
        currentOrderDay === null ||
        orderDay === null ||
        currentOrderDay !== orderDay
      ) {
        continue;
      }

      // Check if `auto_freq` matches
      if (checkoutForm.delivery_interval !== order.auto_freq) {
        continue;
      }

      // Compare products and quantities
      const isMatching = compareProducts(
        cart,
        order.OrderedProduct
      );

      if (isMatching) {
        // Return the first matching order's `parent_jde_id`
        return res.status(200).json({
          parent_jde_id: order.parent_jde_id,
        });
      }
    }

    // Return null if no match is found
    return res.status(200).json({ parent_jde_id: null });
  } catch (error) {
    console.error("Error checking recurring orders:", error);
    return res
      .status(500)
      .json({ message: "Internal server error", error: error.message });
  }
};

// Helper function to compare products and quantities
function compareProducts(currentProducts, orderProducts) {
  const currentProductsMap = currentProducts.reduce((map, product) => {
    map[product.product_id] = product.qty;
    return map;
  }, {});

  const orderProductsMap = orderProducts.reduce((map, product) => {
    map[product.product_id] = product.qty;
    return map;
  }, {});

  return JSON.stringify(currentProductsMap) === JSON.stringify(orderProductsMap);
}
