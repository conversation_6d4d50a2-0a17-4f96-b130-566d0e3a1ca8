import axios from "axios";

export default async (req, res) => {
  if (req.method === "POST") {
    const { token, zip, country, visionOnlyOrder, qty, weight } = req.body;

    console.log(req.body);

    const visionOnlyBoxes = {
      box1: { length: 9, width: 3, height: 11, minQty: 1, maxQty: 6 },
      box2: { length: 9, width: 8, height: 11, minQty: 7, maxQty: 12 },
      box3: { length: 12, width: 11, height: 11, minQty: 13, maxQty: 40 },
      box4: { length: 13, width: 13, height: 13, minQty: 41, maxQty: 60 },
      box5: { length: 14, width: 14, height: 20, minQty: 61, maxQty: 100 },
    };
    
    const mixedBoxes = {
      box1: { length: 9, width: 3, height: 11, minQty: 1, maxQty: 1 },
      box2: { length: 9, width: 5, height: 11, minQty: 2, maxQty: 3 },
      box3: { length: 9, width: 8, height: 11, minQty: 4, maxQty: 6 },
      box4: { length: 12, width: 11, height: 11, minQty: 7, maxQty: 12 },
      box5: { length: 13, width: 13, height: 13, minQty: 13, maxQty: 18 },
      box6: { length: 14, width: 14, height: 20, minQty: 19, maxQty: 50 },
    };
    
    const selectBoxes = (qty, visionOnlyOrder) => {
      const boxesConfig = visionOnlyOrder ? visionOnlyBoxes : mixedBoxes;
      const selectedBoxes = [];
    
      while (qty > 0) {
        let selectedBox = null;
    
        // Check if the quantity fits within the range of any box
        for (const box in boxesConfig) {
          const boxConfig = boxesConfig[box];
          if (qty >= boxConfig.minQty && qty <= boxConfig.maxQty) {
            selectedBox = box;
            break;
          }
        }
    
        // If no suitable box is found, choose the biggest box that fits the most
        if (!selectedBox) {
          let largestBoxCapacity = 0;
          for (const box in boxesConfig) {
            const boxConfig = boxesConfig[box];
            if (boxConfig.maxQty > largestBoxCapacity) {
              largestBoxCapacity = boxConfig.maxQty;
              selectedBox = box;
            }
          }
        }
    
        // Subtract the quantity of the selected box
        const boxConfig = boxesConfig[selectedBox];
        const selectedBoxQty = Math.min(qty, boxConfig.maxQty);
        selectedBoxes.push({ box: selectedBox, boxqty: selectedBoxQty });
        qty -= selectedBoxQty;
      }
    
      return selectedBoxes;
    };
    const selectedBoxes = selectBoxes(qty, visionOnlyOrder);

    console.log(selectedBoxes);
    

    //extraBox is not being added if it is needed (qty > 250), need to add in if case occurs

    let data = {
      accountNumber: {
        value:  process.env.FEDEX_ACCOUNT,
      },
      requestedShipment: {
        shipper: {
          address: {
            postalCode: 91741, // oasis zipcode
            countryCode: "US",
          },
        },
        recipient: {
          address: {
            postalCode: zip, // to address zipcode
            countryCode: country,
          },
        },
        packagingType: "YOUR_PACKAGING",
        pickupType: "USE_SCHEDULED_PICKUP",
        rateRequestType: ["ACCOUNT"],
        requestedPackageLineItems: [],
      },
    };

    selectedBoxes.forEach(({ box, boxqty }) => {
      const boxConfig = visionOnlyOrder ? visionOnlyBoxes[box] : mixedBoxes[box];
      const packLineItem = {
        weight: { units: "LB", value: (weight * boxqty) / qty },
        dimensions: { length: boxConfig.length, width: boxConfig.width, height: boxConfig.height, units: "IN" },
      };
      data.requestedShipment.requestedPackageLineItems.push(packLineItem);
    });
    console.log(data.requestedShipment.requestedPackageLineItems);

    let config = {
      method: "post",
      url: "https://apis.fedex.com/rate/v1/rates/quotes",
      headers: {
        "Content-Type": "application/json",
        "X-locale": "en_US",
        Authorization: `Bearer ${token.data}`,
      },
      data: data,
    };

    await axios
      .request(config)
      .then((response) => {
        const { rateReplyDetails } = response.data.output;
        let rates = [];
        rateReplyDetails.forEach((element) => {
          if (element.serviceType === "FEDEX_GROUND") {
            rates.push({
              oasis_code:'FXG',
              service: element.serviceType,
              description: element.serviceDescription.names[1].value,
              price: element.ratedShipmentDetails[0].totalNetCharge,
              oasis_fee: element.ratedShipmentDetails[0].totalNetCharge * 0.1,
              total:parseFloat(
                (element.ratedShipmentDetails[0].totalNetCharge +
                element.ratedShipmentDetails[0].totalNetCharge * 0.1).toFixed(2))
            });
          }
          if (element.serviceType === "FEDEX_EXPRESS_SAVER") {
            rates.push({
              oasis_code:'P3D',
              service: element.serviceType,
              description: element.serviceDescription.names[1].value,
              price: element.ratedShipmentDetails[0].totalNetCharge,
              oasis_fee: element.ratedShipmentDetails[0].totalNetCharge * 0.1,
              total:parseFloat(
                (element.ratedShipmentDetails[0].totalNetCharge +
                element.ratedShipmentDetails[0].totalNetCharge * 0.1).toFixed(2))
            });
          }
          if (element.serviceType === "FEDEX_2_DAY") {
            rates.push({
              oasis_code:'P2D',
              service: element.serviceType,
              description: element.serviceDescription.names[1].value,
              price: element.ratedShipmentDetails[0].totalNetCharge,
              oasis_fee: element.ratedShipmentDetails[0].totalNetCharge * 0.1,
              total:parseFloat(
                (element.ratedShipmentDetails[0].totalNetCharge +
                element.ratedShipmentDetails[0].totalNetCharge * 0.1).toFixed(2))
            });
          }
        });
        console.log("testing if being run...");
        res.status(200).send(rates.reverse());
      })
      .catch((error) => {
        console.log(error);
        res
          .status(error.response.status)
          .send(
            "Could not retrieve shipping rates. Please check your address and try again."
          );
      });
  }
};
