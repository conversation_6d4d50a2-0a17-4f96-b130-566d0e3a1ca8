import axios from "axios";

let data = {
  grant_type: "client_credentials",
  client_id: process.env.FEDEX_CLIENT_ID,
  client_secret: process.env.FEDEX_CLIENT_SECRET,
};

let config = {
  method: "post",
  maxBodyLength: Infinity,
  url: "https://apis.fedex.com/oauth/token",
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
    Cookie:
      "_abck=1B9AAB63FE72162CA65C630E4E37FA4F~-1~YAAQtiwtF6a+4uWIAQAAMBmICQopLSWktFSiVRtF79BCOThCz3SaMRvDtuYNJPi86GU7Z4Qt8JtunIXlY3vlNxd9h+nnr/li6dxZsFL+LAjkhtd3WsUiekuKAwxb9CfgUJyd/DNYxC0NYOqHLH9iR1KXnmbOcSL1IJfuvUiyvv0TAnjYuWcAtwAXvzBqAwPGxX1RUjiKIvY8Y2Td8Rx2sjYBz0yihWeALyhZRAxSA5RM5y1kYN9VNzcMBkP7nR4X1y3kQImNUesBt+/2eunbP/dJlxUTRsBcLUMObFikZomg3DGiCHakPdbKnzNB3i/gO5mWZneajZnt3G5hUXl81ofN8+js487rI4Kj+Fl3jWuVN4+Wfvwerg==~-1~-1~-1; bm_sz=D94A850B39A68F0DD569B17BEA1E6532~YAAQtiwtF6e+4uWIAQAAMBmICRQrzSyfhURQPq/rgjqw7IWoLBToDqrd8c1fOxJImJkeR0wFoVEIcAA5ic9CKLiVXXr1fOyO3ENX8j4mNd7LmBBi0HtVbddGYdvi6MDIjoyofNPOz+vGkQLD/1n8d8DO8U2RhnM4oba786VKZlB5NQ8bPz1H3MQRDflgcFVc5WE9EfeK4LAuTA0OZdw3eN9WxAZa2KXUIFrW8qQVL6iHJqPEXmn2Ltq+nIodmhzfj3tyzaoM/fEx/atbAiBPJYXRduNqxSxDaYvQKeTvaHsy/w==~3684675~3289156",
  },
  data: data,
};
export default async (req, res) => {
  if (req.method === "POST") {
    await axios
      .request(config)
      .then((response) => {
        let {access_token} = response.data;
        res.status(200).send(access_token);
      })
      .catch((error) => {
        console.log(error);
      });
  }
};
