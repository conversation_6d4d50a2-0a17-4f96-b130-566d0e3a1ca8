import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    try {
      const promotions = await prisma.promotion.findMany({
        where: {
          active: 1,
        },
        include: {
          Conditions: {
            include: {
                applicableProducts: true,
            }
          },
        },
      });

      return res.status(200).json(promotions);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
