import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "PUT") {
    const { id, userProfile, settings } = req.body;
    console.log(id);
    // return res.status(200).json(userProfile);
    try {
      // location create
      const item = await prisma.setting.update({
        where: {
          user_id: Number(id),
        },
        data: {
          invoice_view: settings.invoice.view,
          invoice_sort:settings.invoice.sort,
          inv_new_email: Number(settings.invoice.new.email),
          inv_new_badge: Number(settings.invoice.new.badge),
          inv_new_sms: Number(settings.invoice.new.sms),

          inv_recieved_email: Number(settings.invoice.payment_recieved.email),
          inv_recieved_badge: Number(settings.invoice.payment_recieved.badge),
          inv_recieved_sms: Number(settings.invoice.payment_recieved.sms),

          inv_pastdue_email: Number(settings.invoice.past_due.email),
          inv_pastdue_badge: Number(settings.invoice.past_due.badge),
          inv_pastdue_sms: Number(settings.invoice.past_due.sms),

          order_view: settings.orders.view,
          order_sort:settings.orders.sort,
          ord_new_email: Number(settings.orders.new.email),
          ord_new_badge: Number(settings.orders.new.badge),
          ord_new_sms: Number(settings.orders.new.sms),

          ord_recieved_email: Number(settings.orders.recieved.email),
          ord_recieved_badge: Number(settings.orders.recieved.badge),
          ord_recieved_sms: Number(settings.orders.recieved.sms),

          ord_shipped_email: Number(settings.orders.shipped.email),
          ord_shipped_badge: Number(settings.orders.shipped.badge),
          ord_shipped_sms: Number(settings.orders.shipped.sms),
          User: {
            update: {
              email: userProfile.email,
              first_name: userProfile.firstName,
              last_name: userProfile.lastName,
              position: userProfile.title,
              ext: userProfile.extension,
              phone: userProfile.mainPhone,
              mobile_type: userProfile.mainPhoneType,
              fax: userProfile.fax,
              address: userProfile.address.street,
              address_l2: userProfile.address.street2,
              city: userProfile.address.city,
              state: userProfile.address.state,
              zip: userProfile.address.zip,
            },
          },
        },
      });

      return res.status(200).json({message: 'Profile Saved.'});
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
