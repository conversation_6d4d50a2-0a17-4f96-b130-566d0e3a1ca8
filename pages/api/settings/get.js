import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const id = Number(req.query.id);
    try {
      const settings = await prisma.setting.findMany({
        where: {
          user_id: id,
        },
      });

      return res.status(200).json(settings);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
