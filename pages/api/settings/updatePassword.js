import prisma from "./../prisma";

const bcrypt = require("bcrypt");

export default async (req, res) => {
  if (req.method === "PUT") {
    const { plainPassword, hashedPassword, newPassword, user_id } = req.body;
    console.log(req.body);

    const check = await new Promise((resolve) => {
      bcrypt.compare(plainPassword, hashedPassword, function (err, res) {
        resolve(res);
      });
    });

    if (!check) {
      return res
        .status(200)
        .json({ message: "Incorrect current password", changed: false });
    } else if (check && plainPassword === newPassword) {
      return res.status(200).json({
        message: "New password cannot match current password",
        changed: false,
      });
    } else {
      try {
        const hash = await bcrypt.hash(newPassword, 10);
        const item = await prisma.user.update({
          where: {
            id: Number(user_id),
          },
          data: {
            password: hash,
          },
        });

        return res
          .status(200)
          .json({ message: "Password Updated.", changed: true, hash: hash });
      } catch (err) {
        console.log(err.message);
        return res.status(503).json({ err: err.toString() });
      }
    }
  }
};
