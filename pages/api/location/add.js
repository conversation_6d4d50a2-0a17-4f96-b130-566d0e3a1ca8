import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === "POST") {
    // const {info} = JSON.parse(req.body)
    const now = new Date();
    try {
      // location create
      const item = await prisma.locations.create({
        data: {
          practice: req.body.practice,
          website: req.body.website,
          address: req.body.address,
          address_2: req.body.address2,
          city: req.body.city,
          state: req.body.state,
          zip: req.body.zip,
          country: req.body.country,
          phone: req.body.phone,
          type: req.body.type,
          partner_type: req.body.partner_type,
          account_id: req.body.account_id,
          created_at: now,
          updated_at: now,
        },
      });

        return res.status(200).json(item);
      
    } catch (err) {
      console.log(err);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
