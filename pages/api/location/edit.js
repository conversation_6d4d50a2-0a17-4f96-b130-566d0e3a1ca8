import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === "POST") {
    const now = new Date();
    try {
      // location create
      console.log(req.body);
      // return res.status(200);
      const { id } = req.body;
      const item = await prisma.locations.update({
        where: {
          id: id,
        },
        data: {
          practice: req.body.practice,
          website: req.body.company_website,
          address: req.body.address,
          address_2: req.body.address2,
          city: req.body.city,
          state: req.body.state,
          zip: req.body.zip,
          country: req.body.country,
          phone: req.body.phone,
          partner_type: req.body.partner_type,
          type: req.body.type,
          updated_at: now,
        },
      });

      console.log(item);

      return res.status(200).json(item);
    } catch (err) {
      console.log(err);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
