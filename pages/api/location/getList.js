import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const account_id = Number(req.query.accountId);
    const groupId = Number(req.query.groupId);
    const locationId = Number(req.query.locationId);

    if (!account_id) {
     return res.status(200).json({ message: "Session data is loading..." });
    }
    try {
      // location get
      let items;
      if(groupId === 1){
        items = await prisma.locations.findMany({ 
          where: {
          account_id: account_id,
          disabled:0,
          }
         });
      } else if (groupId === 2){

        items = await prisma.locations.findMany({ 
          where: {
          account_id: account_id,
          id: locationId,
          disabled:0,
          }
        });
      } else{
        items = [];
      }

       const formattedItems = items.map((item) => ({
        value: item.id,
        label: item.practice + ' - ' + item.address,
      }));
      return res.status(200).json({ formattedItems });
    } catch (err) {
      console.log(err.message);
      return res.status(503).json({ err: err.toString() });
    }
  }
};
