import prisma from "./../prisma";

export default async (req, res) => {
  if (req.method === "GET") {
    const { account_id, user_group, user_id, location_id } = req.query;

    console.log(req.query);
    // return

    if (!account_id) {
      return res.status(200).json({ message: "Session data is loading..." });
    }
    if ((user_group == 2 || user_group == 4) && !location_id) {
      try {
        // location get
        const item = await prisma.locations.findMany({
          where: {
            account_id: Number(account_id),
            disabled:0,
          },
        });
        return res.status(200).json({ item });
      } catch (err) {
        console.log(err.message);
        return res.status(503).json({ err: err.toString() });
      }
    } else if (user_group == 1 || user_group == 3) {
      try {
        // location get
        const item = await prisma.locations.findMany({
          where: {
            account_id: Number(account_id),
            disabled:0,
          },
        });
        return res.status(200).json({ item });
      } catch (err) {
        console.log(err.message);
        return res.status(503).json({ err: err.toString() });
      }
    } else {
      try {
        // location get
        const item = await prisma.locations.findFirst({
          where: {
            id: Number(location_id),
            disabled:0,
          },
        });
        return res.status(200).json({ item });
      } catch (err) {
        console.log(err.message);
        return res.status(503).json({ err: err.toString() });
      }
    }
  }
};
