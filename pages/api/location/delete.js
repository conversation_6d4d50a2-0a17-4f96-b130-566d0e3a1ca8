import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "POST") {
    const { id } = req.body;
    try {
      // location create
      const item = await prisma.locations.update({
        where: {
          id: id,
        },
        data: {
          disabled: 1,
        },
      });

      return res.status(200).json({ item });
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
