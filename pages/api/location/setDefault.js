import prisma from './../prisma';

export default async (req, res) => {
  const now = new Date(); 
  if (req.method === "POST") {
    if (req.body.primary == 1) {
      try {
        // location create
        const item_2 = await prisma.locations.update({
          where: {
            id: req.body.location_id,
          },
          data: {
            default: 1,
            updated_at: now,
          },
        });
        // const item = await prisma.user.update({
        //   where: {
        //     id: req.body.user_id,
        //   },
        //   data: {
        //     location_id: req.body.location_id,
        //     updated_at: now,
        //   },
        // });

        const item_3 = await prisma.locations.updateMany({
          where: {
            account_id: req.body.account_id,
            id: {
              not: req.body.location_id,
            },
          },
          data: {
            default: 0,
            updated_at: now,
          },
        });

        return res.status(200).json({ item_2, item_3 });
      } catch (err) {
        console.log(err);
        return res.status(503).json({ err: err.toString() });
      }
    } else {
      // try {

      //   const item = await prisma.user.update({
      //     where: {
      //       id: req.body.user_id,
      //     },
      //     data: {
      //       location_id: req.body.location_id,
      //       updated_at: now,
      //     },
      //   });

      //   return res.status(200).json({ item });
      // } catch (err) {
      //   console.log(err);
      //   return res.status(503).json({ err: err.toString() });
      // }
    }
  }
};
