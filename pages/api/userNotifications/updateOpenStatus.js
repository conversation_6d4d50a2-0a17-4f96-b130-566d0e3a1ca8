import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "PUT") {
    const id = Number(req.query.id);
    try {
      // Assuming you are getting the 'open' value from the request body
      const { open } = req.body;

      const updatedNotification = await prisma.userNotifications.update({
        where: {
          id: id,
        },
        data: {
          open: open,
        },
      });

      return res.status(200).json(updatedNotification);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
