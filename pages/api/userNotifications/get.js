import prisma from './../prisma';

export default async (req, res) => {
  if (req.method === "GET") {
    const id = Number(req.query.id);
    try {
      const notifications = await prisma.userNotifications.findMany({
        where: {
          user_id: id,
        },
        include: {
          Notifications: true,
        },
        orderBy: {
            created_at: 'desc' // Sort by date in descending order
        },
      });

      return res.status(200).json(notifications);
    } catch (err) {
      return res.status(503).json({ err: err.toString() });
    }
  }
};
