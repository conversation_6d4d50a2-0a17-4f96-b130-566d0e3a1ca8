"use client";
import { useState, useEffect, useRef } from "react";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { useRouter } from "next/router";
import filterList from "../components/ProductData";
import axios from "axios";
import { Breadcrumb } from "flowbite-react";
import { useCart } from "../contexts/CartContext";
import { useProductDetailsContext } from "../contexts/ProductDetailsContext";
import Layout from "../components/layout";
import List from "../components/products/List";
import SearchBar from "../components/products/SearchBar";
import SideBar from "../components/products/SideBar";

const ProductList = () => {
  const [query, setQuery] = useState("");
  const { notifications, addNotifications } = useGlobalAlertContext();
  const { productDetails, setProductDetails } = useProductDetailsContext();
  const router = useRouter();
  const [noDropdown, setNoDropdown] = useState(false);
  const [isProgrammaticNavigation, setIsProgrammaticNavigation] =
    useState(false);

  const data = router.query;
  // console.log(data);

  const isOpenRef = useRef(-1);
  const isInitialRender = useRef(true);
  const prevData = useRef({});

  let top_level_id = data.parentid;
  // if (data.category == "Surgical") {
  //   top_level_id = 1;
  // } else if (data.category == "Vision") {
  //   top_level_id = 2;
  // }
  

  const fetchProducts = async (id, item_id, noDrop) => {
    console.log("noDrop", noDrop);
    isOpenRef.current = item_id;
    isOpenRef.sub = id;

    const url = `api/products?id=${id}`;
    try {
      const response = await axios.get(url);
      setProductDetails({
        ...productDetails,
        allProducts: response.data.products,
        filters: response.data.filters,
        parent: data.category,
        firstCat: {
          name: response.data.route[0].name,
          id: response.data.route[0].id,
        },
        secondCat: response.data.route[1]
          ? {
              name: response.data.route[1].name,
              id: response.data.route[1].id,
            }
          : "",
      });
      setNoDropdown(noDrop);
    } catch (err) {
      console.log(err);
    }

    const updatedUrl = `/products?category=${data.category}&parentid=${top_level_id}&catid=${item_id}&subid=${id}&maincategory=1`;
    router.replace(updatedUrl, undefined, { shallow: true });
  };

  const fetchNoSubProducts = async (id, noDrop) => {
    isOpenRef.current = id;
    const url = `api/products?id=${id}`;
    try {
      const response = await axios.get(url);
      const value = router.asPath.split("=")[1];

      const cleanedValue = value.replace("#", "");
      setProductDetails({
        ...productDetails,
        allProducts: response.data.products,
        filters: response.data.filters,
        parent: data.category,
        firstCat: {
          name: response.data.route[0].name,
          id: response.data.route[0].id,
        },
        secondCat: response.data.route[1]
          ? {
              name: response.data.route[1].name,
              id: response.data.route[1].id,
            }
          : "",
      });
      setNoDropdown(noDrop);
    } catch (err) {
      console.log(err);
    }
    const updatedUrl = `/products?category=${data.category}&parentid=${top_level_id}&catid=${id}&maincategory=0`;
    router.replace(updatedUrl, undefined, { shallow: true });
  };

  const fetchAllProducts = async (id, fromSidebar) => {
    console.log("fetching all products...");

    if (fromSidebar && isOpenRef.current === id) {
      isOpenRef.current = 0;
    } else {
      isOpenRef.current = id;
    }
    const url = `api/products/all?id=${id}`;
    try {
      const response = await axios.get(url);
      setProductDetails({
        ...productDetails,
        allProducts: response.data.products,
        filters: [],
        secondCat: "",
        parent: data.category,
        firstCat: {
          name: response.data.route[0].name,
          id: response.data.route[0].id,
        },
      });
    } catch (err) {
      console.log(err);
    }
    let pid = data.parentid;
    // if(data.category == 'Surgical'){
    //   pid = 1;
    // }else{
    //   pid = 2;
    // }
    const updatedUrl = `/products?category=${data.category}&parentid=${pid}&catid=${id}&maincategory=1`;
        router.replace(updatedUrl, undefined, { shallow: true });
  };

  const fetchAPFromProduct = async (cat_id, parent_id, sub_id) => {
    isOpenRef.current = Number(cat_id);
    setNoDropdown(true);

    let url = "";
    if (router.query.subid != "undefined") {
      url = `api/products/all?id=${cat_id}`;
    } else {
      url = `api/products?id=${cat_id}`;
    }

    const products = url;
    const categories = `api/categories/fetchCategories?id=${parent_id}`;

    try {
      const productsResponse = await axios.get(products);
      const categoriesResponse = await axios.get(categories);
      setProductDetails({
        ...productDetails,
        allProducts: productsResponse.data.products,
        filters: [],
        categories: categoriesResponse.data,
        secondCat: "",
        parent: data.category,
        firstCat: {
          name: productsResponse.data.route[0].name,
          id: productsResponse.data.route[0].id,
        },
      });
    } catch (err) {
      console.log(err);
    }
  };

  const subFromProduct = async (id, item_id, parent_id) => {
    let productsURL = "";
    if (!id) {
      console.log("no id");
      productsURL = `api/products?id=${item_id}`;
    } else {
      productsURL = `api/products?id=${id}`;
    }
    const products = productsURL;
    const categories = `api/categories/fetchCategories?id=${parent_id}`;
    try {
      const productsResponse = await axios.get(products);
      const categoriesResponse = await axios.get(categories);
      setProductDetails({
        ...productDetails,
        allProducts: productsResponse.data.products,
        filters: productsResponse.data.filters,
        parent: data.category,
        categories: categoriesResponse.data,
        firstCat: {
          name: productsResponse.data.route[0].name,
          id: productsResponse.data.route[0].id,
        },
        secondCat: productsResponse.data.route[1]
          ? {
              name: productsResponse.data.route[1].name,
              id: productsResponse.data.route[1].id,
            }
          : "",
      });
    } catch (err) {
      console.log(err);
    }
  };

  const fetchTopProducts = async () => {
    let id = top_level_id;
    // if (data.category == "Surgical") {
    //   id = 1;
    // } else if (data.category == "Vision") {
    //   id = 2;
    // }
    const url = `api/products/all?id=${top_level_id}`;
    try {
      const response = await axios.get(url);
      isOpenRef.current = 0;
      setProductDetails({
        ...productDetails,
        allProducts: response.data.products,
        filters: [],
        secondCat: "",
        firstCat: "",
      });
    } catch (err) {
      console.log(err);
    }
    const updatedUrl = `/products?category=${data.category}&parentid=${id}`;
        router.replace(updatedUrl, undefined, { shallow: true });
  };

  const handleChange = async (e) => {
    e.preventDefault();
    const url = `api/search/query?q=${e.target.value}`;
    try {
      const response = await axios.get(url);
      setProductDetails({
        ...productDetails,
        allProducts: response.data.products,
        filters: [],
        secondCat: "",
        firstCat: "",
      });
    } catch (err) {
      console.log(err);
    }
  };

  const fetchTopProductsCategories = async (id) => {
    const categories = `api/categories/fetchCategories?id=${id}`;
    const products = `api/products/all?id=${id}`;
    try {
      const catResponse = await axios.get(categories);

      const proResponse = await axios.get(products);
      isOpenRef.current = 0;
      setProductDetails({
        ...productDetails,
        // allProducts: proResponse.data.products,
        allProducts: [],
        categories: catResponse.data,
        parent: data.category,
        firstCat: "",
        secondCat: "",
      });
    } catch (err) {
      console.log(err);
    }
    const updatedUrl = `/products?category=${data.category}&parentid=${id}`;
    router.replace(updatedUrl, undefined, { shallow: true });
  };

  useEffect(() => {
    if (router.query.category) {
      console.log(router.pathname);
      if (router.query.catid) {
        if (router.query.maincategory == 1) {
          console.log("Main Category");
          fetchAPFromProduct(
            router.query.catid,
            router.query.parentid,
            router.query.subid
          );
        } else {
          isOpenRef.current = Number(router.query.catid);
          isOpenRef.sub = Number(router.query.subid);
          subFromProduct(
            router.query.subid,
            router.query.catid,
            router.query.parentid
          );
        }

      } else {
        fetchTopProductsCategories(top_level_id);
      }
    }
  }, [router.query.category]);

  // useEffect(() => {
  //   console.log("uEpD", productDetails);
  // console.log("isOpenRef", isOpenRef);

  // }, [productDetails]);

  return (
    <Layout>
      <div className="h-full w-full overflow-hidden  bg-white rounded-xl dark:bg-gray-800">
        <div className="grid grid-cols-6 grid-rows-[auto,1fr] w-full h-full overflow-hidden">
          <div className="col-span-1 border-r-2 border-[#EFF1F0] h-fit w-full">
            <div className="flex w-full px-7 justify-start items-center border-b-2 h-[60px] border-[#EFF1F0]">
              <p className="text-2xl font-bold text-center ">Products</p>
            </div>
          </div>
          <div className="col-span-5 w-full h-fit ">
            <div className="flex w-full justify-between border-b-2 h-[60px] border-[#EFF1F0]">
              <div className="pt-3 pl-7">
                <Breadcrumb
                  aria-label="Product Category Breadcrumb"
                  className="pt-[6px]"
                >
                  {productDetails.parent && (
                    <Breadcrumb.Item
                      className="hover: cursor-pointer"
                      onClick={() => fetchTopProducts()}
                    >
                      {data.category}
                    </Breadcrumb.Item>
                  )}
                  {productDetails.firstCat && (
                    <Breadcrumb.Item
                      className="hover: cursor-pointer"
                      onClick={() => {
                        noDropdown
                          ? fetchNoSubProducts(productDetails.firstCat.id, true)
                          : fetchAllProducts(productDetails.firstCat.id);
                      }}
                    >
                      {productDetails.firstCat.name}
                    </Breadcrumb.Item>
                  )}
                  {productDetails.secondCat && (
                    <Breadcrumb.Item
                      // className="hover: cursor-pointer"
                      // onClick={() => fetchProducts(productDetails.secondCat.id)}
                    >
                      {productDetails.secondCat.name}
                    </Breadcrumb.Item>
                  )}
                </Breadcrumb>
              </div>
              <div className="">
                <SearchBar didChangeQuery={handleChange} query={query} />
              </div>
            </div>
          </div>
          <div className="col-span-1 border-r-2 border-[#EFF1F0] h-full w-full overflow-y-auto">
            <SideBar
              didClick={fetchProducts}
              didClickMain={fetchAllProducts}
              didClickSingle={fetchNoSubProducts}
              didClickTop={fetchTopProductsCategories}
              // didClickTop={fetchTopProducts}
              key={Math.random()}
              mainCategories={productDetails.categories}
              isOpenRef={isOpenRef}
            />
          </div>
          <div className="col-span-5 pb-10 w-full overflow-y-auto ">
            <List
              // data={products}
              // filters={filters}
              data={productDetails.allProducts}
              filters={productDetails.filters}
              key={Math.random()}
              alert={notifications}
              setAlert={addNotifications}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProductList;
