import { useSession } from "next-auth/react";
import Layout from "../components/layout";
import { useEffect, useState } from "react";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import Switch from "../components/billing/SwitchBillingComponents";
import BillingViewEdit from "../components/billing/BillingViewEdit";
import BillingViewAdd from "../components/billing/BillingViewAdd";
import { stateNames } from "../components/utils";
import axios from "axios";
import CardListSkeleton from "../components/skeleton/billing/CardListSkeleton";
import { Alert } from 'flowbite-react';
import { usePermissions } from "../contexts/PermissionsContext";
import { createPermissionChecker } from "../components/utils";
import RouteGuard from "../components/RouteGuard";

const Billing = () => {
  const { data: session, status } = useSession();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);
  const [paymentList, setPaymentList] = useState([]);
  const [locations, setLocations] = useState([]);
  const [syncingCards, setSyncingCards] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");
  const [activeComponent, setActiveComponent] = useState({
    state: "New",
  });
  const [NewCard, setNewCard] = useState({
    nickname: "",
    card_owner: "",
    card_number: "",
    expiration_date: "",
    cvc: "",
    company_name: "",
    company_website: "",
    address: "",
    address_2: "",
    country: "US",
    zipcode: "",
    city: "",
    state: "",
    phone: "",
    default: 0,
    account_id: 0,
  });

  const updateCards = (user_id) => {
    setActiveComponent({
      state: "New",
    });
    setSyncingCards(true);
    axios.get(`/api/billing/get?user_id=${user_id}&account_id=${session.user.account_id}`).then((response) => {
      setPaymentList(response.data);
      setNewCard({
        nickname: "",
        card_owner: "",
        card_number: "",
        expiration_date: "",
        cvc: "",
        company_name: "",
        company_website: "",
        address: "",
        address_2: "",
        country: "US",
        zipcode: "",
        city: "",
        state: "",
        phone: "",
        default: 0,
        account_id: session["user"]["account_id"],
      });
      setSyncingCards(false);
    }).catch((error) => {
      if (error.response && error.response.status === 404) {
        setErrorMessage(error.response.data);
      } else {
        setErrorMessage("An unexpected error occurred. Please try again.");
      }
      setSyncingCards(false);
    });
  };

  useEffect(() => {
    if (session) {
      setSyncingCards(true);
      const account_id = session["user"]["account_id"];
      const user_id = session["user"]["id"];
      const location_id = session["user"]["location_id"];
      const group_id = session["user"]["group_id"];

      axios
        .get(
          `/api/billing/get?user_id=${user_id}&account_id=${account_id}&location_id=${location_id}&user_group=${group_id}`,
        )
        .then((response) => {
          setPaymentList(response.data);
          setSyncingCards(false);
        }).catch((error) => {
          if (error.response && error.response.status === 404) {
            setErrorMessage(error.response.data);
          } else {
            setErrorMessage("An unexpected error occurred. Please try again.");
          }
          setSyncingCards(false);
        });

      axios
        .get("/api/location/get", {
          params: {
            user_id: user_id,
            account_id: account_id,
            location_id: session["user"]["location_id"],
            user_group: session["user"]["group_id"],
          },
        })
        .then((response) => {
          setLocations(response.data.item);
        });
    }

    console.log(NewCard);
  }, [session]);

  console.log("session", session);
  // console.log("locations", locations);
  // console.log("locations length", Object.keys(locations).length);
  // console.log("locations Array?", Array.isArray(locations));

    return (
      <Layout>
        <RouteGuard section="Billing">

        <div id="header-section"></div>
        {errorMessage && (
            <Alert color="failure" onDismiss={() => setErrorMessage('')} className="mb-2">
              {errorMessage}
            </Alert>
          )}
        <div className="flex flex-row gap-5 overflow-y-auto">
        
          <div
            className={`basis-1/4 text-sm pb-10 font-medium text-gray-900 bg-white border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white ${
              syncingCards ? "h-screen" : "h-fit"
            }`}
          >
            {hasPermission('Billing', "add") && (
            <button
              aria-current="true"
              type="button"
              className="w-full px-4 py-2 font-medium text-left text-black border-b border-gray-200 rounded-t-lg cursor-pointer focus:outline-none dark:bg-gray-800 dark:border-gray-600"
            >
              <div
                className="flex flex-row justify-between h-[84px]"
                onClick={() => {
                  setActiveComponent({
                    state: "New",
                  });
                  setNewCard({
                    nickname: "",
                    card_owner: "",
                    card_number: "",
                    expiration_date: "",
                    cvc: "",
                    company_name: "",
                    company_website: "",
                    address: "",
                    address_2: "",
                    country: "US",
                    zipcode: "",
                    city: "",
                    state: "",
                    phone: "",
                    default: 0,
                    account_id: session["user"]["account_id"],
                  });
                }}
              >
                <div className="grow">
                  <p className="font-medium text-[14px] ml-[30.5px] my-[30px] w-[232px]">
                    Add New Card
                  </p>
                </div>
                <div className="grow">
                  <img
                    src="/add_icon.png"
                    className="h-[20px] w-[20px] my-[32px] mr-[30.5px]"
                  ></img>
                </div>
              </div>
            </button>
            )}
            {syncingCards && (
              // <div className="m-auto text-center text-lg pt-4">
              //   <p className="pb-2">Loading saved cards...</p> <Spinner size="xl" />
              // </div>
              <CardListSkeleton />
            )}
            {!syncingCards &&
              paymentList.map((item, index) => (
                <Card
                  setActiveComponent={setActiveComponent}
                  item={item}
                  index={index}
                  key={index}
                  syncingCards={syncingCards}
                />
              ))}
          </div>

          <Switch active={activeComponent.state} className="overflow-y-auto">
            <BillingViewAdd
              setNewCard={setNewCard}
              newCard={NewCard}
              name={"New"}
              stateNames={stateNames}
              locations={locations}
              onAdd={updateCards}
              alert={notifications}
              setAlert={addNotifications}
            />
            <BillingViewEdit
              current={paymentList[activeComponent.index]}
              session={session}
              name={"Edit"}
              stateNames={stateNames}
              locations={locations}
              onUpdate={updateCards}
              alert={notifications}
              setAlert={addNotifications}
            />
          </Switch>
        </div>
        </RouteGuard>
      </Layout>
    );
};

function Card({ setActiveComponent, item, index, syncingCards }) {
  //convert to switch or set in paymentList when more card types added
  let cardimage = "/visa.png";
  switch (item.cardpointe[0].accttype) {
    case "MC":
      cardimage = "/mc.png";
      break;
    case "AMEX":
      cardimage = "/amex.png";
      break;
    case "DISC":
      cardimage = "/discover.png";
      break;
    // Add more cases if needed
    default:
      // This is the default case if none of the above matches
      // You can set a default image here if desired
      break;
  }

  return (
    <div>
      <button
        type="button"
        className="w-full font-medium text-left border-b border-gray-200 cursor-pointer"
      >
        <div
          className="flex flex-row justify-between h-[84px] gap-[10px]"
          onClick={() =>
            setActiveComponent({
              state: "Edit",
              index: index,
            })
          }
        >
          <div className="grow">
            <img
              src={cardimage}
              className="w-[48.81px] h-[32.03px] ml-[30px] mt-[24.99px]"
            ></img>
          </div>
          <div className="grow">
            <div className="my-[20px]">
              <p className="text-[14px] font-semibold">
                {/* {item.cardpointe.card_number.substr(item.card_number.length - 4)} */}
                {/* temporily until figure out where to get last 4 from, either upon saving or from cardpointe. */}
                {item.db.last_four}
              </p>
              <p className="text-[12px]">
                Exp {item.cardpointe[0].expiry && ( item.cardpointe[0].expiry.slice(0, 2))}/
                {item.cardpointe[0].expiry && (item.cardpointe[0].expiry.slice(2, 4))}
              </p>
            </div>
          </div>
          <div className="grow">
            <p className="text-[12px] my-[31px]">{item.db.nickname}</p>
          </div>
          {item.default == 1 && (
            <div className="grow">
              <p className="bg-black text-white text-[8px] text-center font-semibold rounded-sm w-[38px] py-[5px] my-[31px] mr-[30px]">
                Primary
              </p>
            </div>
          )}
        </div>
      </button>
    </div>
  );
}

export default Billing;

// bg-[#4BB5EA]
// text-white
