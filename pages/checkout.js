import Layout from "../components/layout";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ccordion,
} from "flowbite-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import axios from "axios";
import { format } from "path";
import { useRouter } from "next/router";
import { useSession, signOut } from "next-auth/react";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { useCart } from "../contexts/CartContext";
import { HiInformationCircle } from "react-icons/hi";
import Cart from "../components/checkout/cart";
import Locations from "../components/checkout/locations";
import PaymentMethod from "../components/checkout/paymentMethod";
import { usePromotionsContext } from '../contexts/PromotionsContext'; // Import the promotion context
import ConfirmationModal from "../components/confirmationModal";
import { usePermissions } from "../contexts/PermissionsContext";
import { create<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../components/utils";

export default function Checkout() {
  const router = useRouter();
  const { push } = useRouter();
  const [cartState, setCartState] = useState([]);
  const [locations, setLocations] = useState([]);
  const [payments, setPayments] = useState([]);
  const [shippingRates, setShippingRates] = useState([]);
  const [cartQty, setCartQty] = useState(0);
  const [cartWeight, setCartWeight] = useState(0);
  const [visionOnlyOrder, setVisionOnlyOrder] = useState(false);
  const [selectedShipping, setSelectedShipping] = useState(0);
  const [totalTax, setTotalTax] = useState(0);
  const [shippingRatesIsLoading, setShippingRatesIsLoading] = useState(false);
  const [paymentAlert, setPaymentAlert] = useState(false);
  const [locationAlert, setLocationAlert] = useState(false);
  const [loading, setLoading] = useState(true);
  const [alert, setAlert] = useState({
    payment: false,
    location: false,
    success: false,
  });
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [processingPayment, setprocessingPayment] = useState(false);
  const [alertMessage, setAlertMessage] = useState("");
  const [locationAlertMessage, setLocationAlertMessage] = useState("");
  const { promotions } = usePromotionsContext(); // Access promotions from context

  const { notifications, addNotifications } = useGlobalAlertContext();
  const { cart, updateCart } = useCart();

  const query = router.query;

  const [checkoutForm, setCheckoutForm] = useState({
    shipping_id: 0,
    payment_id: 0,
    payment_profile: 0,
    shipping_cost: 0,
    shipping_method: '',
    delivery_interval: Number(query.delivery_interval) || 0,
    auto_delivery: Number(query.auto_delivery) || 0,
    tax: 0,
    order_placed_by: query.order_placed_by,
    po_num: query.po_num,
    require_rep_consulting: Number(query.require_rep_consulting),
    surgeon_names: query.surgeon_names,
    discount: Number(query.discount),
  });

  const { data: session, status } = useSession();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);
  // console.log("checkoutForm", checkoutForm);
  // console.log(session);
  // console.log(router.query);

  useEffect(() => {
    if (session) {
      const account_id = session["user"]["account_id"];
      const user_id = session["user"]["id"];
      axios
        .get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        )
        .then((response) => {
          updateCart(response.data.items);
          axios
            .post(`/api/products/getParentType`, { cart: response.data.items })
            .then((response) => {
              setVisionOnlyOrder(response.data.onlyVision);
              setCartQty(response.data.qty);
              setCartWeight(response.data.weight);
            });
        })
       

      axios
        .get("/api/location/get", {
          params: {
            user_id: user_id,
            account_id: account_id,
            location_id: session["user"]["location_id"],
            user_group: session["user"]["group_id"],
          },
        })
        .then((response) => {
          setLocations(response.data.item);
        });

      axios
        .get("/api/billing/get", {
          params: {
            user_id: user_id,
            account_id: account_id,
            location_id: session["user"]["location_id"],
            user_group: session["user"]["group_id"],
          },
        })
        .then((response) => {
          setPayments(response.data);
          setLoading(false);
        });
        
      // console.log("user", session.user);
      // console.log("cart", [cart]);
      // console.log("checkout form", checkoutForm);
      // console.log("checkout", payments);
    }
  }, [session]);

  if (status === "loading") {
    return <h1>Loading...</h1>;
  }

  if (status === "unauthenticated") {
    console.log("unauthenticated");
  }

  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  const returnSubtotal = () => {
    let sum = cart.reduce((acc, item) => acc + Number(item.price) * Number(item.qty), 0);
    const { totalDiscount, finalSubtotal } = applyPromotions(sum);
    // let org_sum = sum;
    // let discount = 0;
    // if (checkoutForm.auto_delivery == 1) {
    //   // sum = sum * 0.9;
    //   discount = org_sum * 0.1;
    // }

    return { sum, totalDiscount, finalSubtotal };
  };

  const returnTotal = () => {
    let subtotal = returnSubtotal().sum;
    let discount = returnSubtotal().totalDiscount;
    // if (checkoutForm.auto_delivery == 1) {
    //   subtotal = parseFloat((subtotal * 0.9).toFixed(2));
    // }
    console.log(selectedShipping);

    return (subtotal - discount) + selectedShipping + totalTax;
  };

  const applyPromotions = (subtotal) => {
    let totalDiscount = 0;

    promotions.forEach(promotion => {
      if (promotion.promotion_type === "order_based") {
        // Check all conditions for the promotion
        const { Conditions } = promotion;
        const conditionsMet = Conditions.every(condition => {
          switch (condition.type) {
            case 'product_quantity':
              // Check if at least one product in the cart meets the quantity threshold
              return cart.some(item => {
                const isApplicableProduct = applicableProducts.length === 0 || 
                  applicableProducts.some(ap => ap.product_id === item.product_id);
                return isApplicableProduct && item.qty >= condition.threshold;
              });
            case 'subtotal':
              return subtotal >= parseFloat(condition.value);
            case 'shipping':
              // Assume shipping value is available from some source
              return false; // Placeholder logic
            // case 'coupon':
            //   return form.coupon_code === condition.coupon_code;
            case 'auto_delivery':
              return checkoutForm.auto_delivery === 1;
            default:
              return true;
          }
        });

        if (conditionsMet) {
          // Apply the discount
          if (promotion.discount_type === "fixed_discount") {
            totalDiscount += promotion.discount_value;
          } else if (promotion.discount_type === "percentage") {
            totalDiscount += (subtotal * (promotion.discount_value / 100));
          }
        }
      }
      

      //TODO BUNDLE type wif necessary
    });

    return { totalDiscount, finalSubtotal: subtotal - totalDiscount };
  };

  const applyShippingPromotions = (selectedShipping,method) => {
    let newShipping = selectedShipping;
    promotions.forEach(promotion => {
      if (promotion.promotion_type === "shipping") {
        const { Conditions } = promotion;
        const conditionsMet = Conditions.every(condition => {
          switch (condition.type) {
            case 'product_quantity':
              // Check if at least one product in the cart meets the quantity threshold
              return cart.some(item => {
                const isApplicableProduct = applicableProducts.length === 0 || 
                  applicableProducts.some(ap => ap.product_id === item.product_id);
                return isApplicableProduct && item.qty >= condition.threshold;
              });
            case 'subtotal':
              return parseFloat(returnSubtotal().finalSubtotal) >= parseFloat(condition.value);
            case 'shipping':
              // Check shipping price threshold and applicable shipping method
              const meetsPriceThreshold = selectedShipping >= parseFloat(condition.value || 0);
              const applicableShippingMethods = (condition.applicableShipping?.split(",") || "");
              const matchesShippingMethod = applicableShippingMethods.length === 0 || applicableShippingMethods.includes(method);
              return meetsPriceThreshold && matchesShippingMethod;
            case 'auto_delivery':
              return checkoutForm.auto_delivery === 1;
            default:
              return true;
          }
        });
        if (conditionsMet) {
          if (promotion.discount_type === "percentage") {
            newShipping = selectedShipping - (selectedShipping * (promotion.discount_value / 100));
          } else if (promotion.discount_type === "fixed_discount") {
            newShipping = selectedShipping - promotion.discount_value;
          } else if (promotion.discount_type === "fixed_price") {
            newShipping = promotion.discount_value;
          }
        }
      }
    });
    console.log("newshipping",newShipping);
    setSelectedShipping(parseFloat(newShipping));
    setCheckoutForm(prev => ({ ...prev, shipping_cost: parseFloat(newShipping) }));
  
    return parseFloat(newShipping);
  };

  const getTax = ({ cart, id, interval, auto }) => {
    // console.log("disc_interval", interval);
    // console.log("disc_auto", auto);
    // console.log("cart", cart);
    // console.log("id", id);
    const taxExempt = session?.user?.Account?.exempt_resale_number || '';

    
      
    if (interval === undefined) {
      interval = checkoutForm.delivery_interval;
    }

    if (auto === undefined) {
      auto = checkoutForm.auto_delivery;
    }

      // console.log("disc_interval", interval);
      // console.log("disc_auto", auto);
    if(taxExempt == ''){
      const disc = auto;

      console.log("cart", cart);
      axios.post("api/billing/getTax", { cart, id }).then((response) => {
        if (response.status == 200) {
          if (response.data.message) {
            failureCheckoutNotification(
              "Invalid address, unable to calculate taxes. Please update address or select another."
            );
            setTotalTax(0);
          } else {
            let tax = response.data.content["tax"];
            console.log("Calc Tax", tax);
            // if (auto == 1) {
            //   tax = tax * 0.9;
            // }
            let orgTax = totalTax;
            setTotalTax(tax);
            if (orgTax == 0){
              sucessCheckoutNotification("Tax calculated for shipping.");
            }
            // sucessCheckoutNotification("Tax calculated for shipping.");
            setCheckoutForm((prevState) => ({
              ...prevState,
              delivery_interval: interval,
              auto_delivery: auto,
              tax: response.data.content["tax"],
            }));
          }
        } else {
          alert("unable to get total tax");
        }
      });
    } else {
      setCheckoutForm((prevState) => ({
        ...prevState,
        delivery_interval: interval,
        auto_delivery: auto,
      }));
      console.log("Tax Exempt");
    }
  };

  const shippingSelected = (zip, country, id) => {
    if (!zip) {
      console.log("No Zip Entered");

      failureCheckoutNotification(
        "Address is not valid. Please update address or select another."
      );
      return;
    }
    const location = id;
    const disc = checkoutForm.auto_delivery ? checkoutForm.auto_delivery : 0;

    setShippingRatesIsLoading(true);
    getTax({ cart: cart, id: location });
    setShippingRates([]);
    axios.post("/api/fedex/token/create").then((token) => {
      axios
        .post("/api/fedex/rates", {
          token: token,
          zip: zip,
          country: "US",
          visionOnlyOrder: visionOnlyOrder,
          qty: cartQty,
          weight: cartWeight,
        })
        .then((response, id) => {
          const fedexRates = response.data;

          // Fetch UPS rates if freight company is UPS and shipping_acct_num is not empty
          const freightCompany = session?.user?.Account?.freight_company || '';
          const shippingAcctNum = session?.user?.Account?.shipping_acct_num || '';

          console.log("freightCompany",freightCompany);
          console.log("shippingAcctNum",shippingAcctNum);
          if (freightCompany === 'ups' && shippingAcctNum) {
            console.log("ups");
            const upsRates = [
              {
                description: 'UPS Ground',
                total: 0.00, // Replace with actual UPS rates
                oasis_code: 'CUG',
              },
              {
                description: 'UPS 2nd Day Air',
                total: 0.00, // Replace with actual UPS rates
                oasis_code: 'CU2',
              },
              {
                description: 'UPS 3 Day Select',
                total: 0.00, // Replace with actual UPS rates
                oasis_code: 'CU3',
               
              },
            ];
            console.log(upsRates);

            setShippingRates([...upsRates, ...fedexRates]);
          } else {
            setShippingRates(fedexRates);
          }

          setShippingRatesIsLoading(false);

        })
        .catch((response) => {
          sucessCheckoutNotification(response.request.response);
          setShippingRatesIsLoading(false);
        });
    });
  };

  const handleCartUpdates = () => {
    if(checkoutForm.shipping_id !== 0){
      let locationID = checkoutForm.shipping_id;
      const currLocation = locations.find(item => item.id === locationID);
      shippingSelected(currLocation.zip.substring(0, 5), "US", locationID);      
      // getTax({ cart: cart, id: locationID });
    }
    
  };

  const handleClickScroll = () => {
    const element = document.getElementById("header-section");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const sucessCheckoutNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: message,
      show: true,
    };

    // console.log("New Notif", newNotification);

    addNotifications(newNotification);
  };

  const failureCheckoutNotification = (message) => {
    console.log("New Fail Notif");
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };

  const create = async () => {
    setprocessingPayment(true);
    if (
      checkoutForm.payment_id === 0 ||
      checkoutForm.shipping_id === 0 ||
      checkoutForm.shipping_method === ''
    ) {
      setprocessingPayment(false);
      failureCheckoutNotification(
        "Please select payment and shipping options to submit order."
      );

      return;
    }

    if (checkoutForm.auto_delivery === 1){
        // Call checkRecurring
      try {
        const response = await axios.post("/api/order/checkDuplicateRecurring", {
          cart,
          checkoutForm,
          user: session.user, // Pass additional account ID if needed
        });

        const { parent_jde_id } = response.data;

        if (parent_jde_id) {
          setIsConfirmModalOpen(true); // Show confirmation modal
          return; // Pause execution until user decides
        }
      } catch (error) {
        console.error("Error checking recurring orders:", error);
        failureCheckoutNotification("Failed to check for recurring orders. Please try again.");
        setprocessingPayment(false);
        return;
      }
    }
        // Proceed with order creation
    proceedWithOrder();
  };

  const proceedWithOrder = async () => {
    setprocessingPayment(true);
    handleClickScroll();
    const discount = returnSubtotal().totalDiscount;
    setCheckoutForm(prev => ({ ...prev, discount: parseFloat(discount) }));

    axios
      .post("/api/order/create", {
        cart: cart,
        cart_selections: router.query,
        checkoutForm: checkoutForm,
        total: returnTotal(),
        user: session.user,
        discount: discount,
      })
      .then((response) => {
        console.log("response", response);
        if (response.data.status == "A") {
          sucessCheckoutNotification(
            "Order submitted successfully! Navigating to Orders..."
          );
          console.log("Success!");
          setTimeout(() => {
            // redirect to orders
            push("/orders");
          }, 2000);
        } else if (response.data.status == "B") {
          console.log("B");
          failureCheckoutNotification(
            response.data.message + ". Please try again."
          );
          setprocessingPayment(false);
        } else if (response.data.status == "C") {
          console.log("C");
          setprocessingPayment(false);
          failureCheckoutNotification(
            response.data.message +
              ". Please select another, or update card info on Billing page."
          );
        } else {
          console.log(response);
          setprocessingPayment(false);
          failureCheckoutNotification(
            response.data.message
              ? response.data.message
              : "Error, please try again."
          );
        }
      });
  };

  useEffect(() => {
    // Check if a shipping method is already selected and exists in the new shipping rates
    const selectedShippingMethodExists = shippingRates.some(item => item.oasis_code === checkoutForm.shipping_method);
  
    // If the selected shipping method still exists, update the shipping cost
    if (selectedShippingMethodExists) {
      // Find the selected shipping rate in the new shipping rates array
      const selectedShippingRate = shippingRates.find(item => item.oasis_code === checkoutForm.shipping_method);
      let selectedShippingCost = applyShippingPromotions(parseFloat(selectedShippingRate.total.toFixed(2)), checkoutForm.shipping_method);

      // Update the shipping cost in the checkout form
      setSelectedShipping(selectedShippingCost);
      setCheckoutForm({
        ...checkoutForm,
        shipping_cost: selectedShippingCost,
        shipping_method: selectedShippingRate.oasis_code,
      });
    }
  }, [shippingRates,checkoutForm.auto_delivery]);

  useEffect(() => {
    axios
      .post(`/api/products/getParentType`, { cart: cart })
      .then((response) => {
        setVisionOnlyOrder(response.data.onlyVision);
        setCartQty(response.data.qty);
        setCartWeight(response.data.weight);
      });
  }, [cart]);
  
  // Trigger updateTax when cartQty or cartWeight changes
  useEffect(() => {
    handleCartUpdates();
  }, [cartQty, cartWeight]);

  console.log("Cart Context", cart);

  // if (session && locations) {
    return (
      <Layout>
        {/* <div className="pb-2" id="header-section">
          {alert.payment &&
            invaildPaymentAlert({ alertMessage, alert, setAlert })}
          {alert.location &&
            invaildLocationAlert({ locationAlertMessage, alert, setAlert })}
          {alert.success && successAlert({ alertMessage, alert, setAlert })}
        </div> */}
        {session && locations &&
        <div className="flex flex-row">
          <div className="basis-2/3">
            <Accordion className="bg-white rounded-xl dark:bg-gray-800 mb-5">
              <Accordion.Panel>
                <Accordion.Title className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                  <h1 className="text-[#353535] text-[20px]">
                    Shipping Location
                  </h1>{" "}
                </Accordion.Title>

                {/* for loop locations */}
                {Array.isArray(locations) && locations.length > 0 ? (
                  locations.map((item, index) => (
                    <Accordion.Content>
                      <Locations
                        item={item}
                        id={index}
                        shippingSelected={shippingSelected}
                        setCheckoutForm={setCheckoutForm}
                      />
                    </Accordion.Content>
                  ))
                ) : (
                  <Accordion.Content>
                    <Locations
                      item={locations}
                      key={locations}
                      shippingSelected={shippingSelected}
                      setCheckoutForm={setCheckoutForm}
                    />
                  </Accordion.Content>
                )}
              </Accordion.Panel>
            </Accordion>

            <Accordion className="bg-white rounded-xl dark:bg-gray-800 mb-5">
              <Accordion.Panel>
                <Accordion.Title className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                  <h1 className="text-[#353535] text-[20px]">Payment Method</h1>{" "}
                </Accordion.Title>

                {/* loop payment items */}

                <Accordion.Content>
                  {loading ? (<p className="text-center">Loading payment methods...</p>) : (
                  payments.length <= 0 ? (
                      <p className="text-center">
                      To add a credit card, please click 
                        <a href="/billing" className=" text-primary-light hover:text-primary"
                        > here </a>
                      or the "Billing" link in the main navigation area to your left.
                      </p>
                  ) : (
                    
                    payments.map((item, index) => (
                      <PaymentMethod
                        item={item}
                        id={index}
                        setCheckoutForm={setCheckoutForm}
                      />
                    ))
                  )
                )}
                </Accordion.Content>
              </Accordion.Panel>
            </Accordion>

            <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
              <div className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                <h1 className="text-[#353535] text-[20px]">Shipping Method</h1>
              </div>

              <div className="flex flex-row flex-wrap w-3/4 gap-4 justify-center py-10 mx-auto lg:w-3/5">
                {shippingRates.length <= 0 ? (
                  <>
                    {shippingRatesIsLoading ? (
                      <Spinner />
                    ) : (
                      <p>
                        Select a shipping address above to view options & rates.
                      </p>
                    )}
                  </>
                ) : (
                  shippingRates.map((item, index) => (
                    <div key={index} className="w-[156.25px] mb-4">
                      <Button
                        color="light"
                        className={`w-full h-11 ${
                          checkoutForm.shipping_method === item.oasis_code
                            ? "bg-primary text-white hover:text-black"
                            : ""
                        }`}
                        key={index}
                        onClick={() => {
                          setSelectedShipping(parseFloat(item.total.toFixed(2)));
                          setCheckoutForm({
                            ...checkoutForm,
                            shipping_cost: parseFloat(item.total.toFixed(2)),
                            shipping_method: item.oasis_code,
                          });
                          applyShippingPromotions(parseFloat(item.total.toFixed(2)), item.oasis_code);

                        }}
                      >
                        {item.description} {formatPrice.format(item.total)}
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </div>

            <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
              <div className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                <h1 className="text-[#353535] text-[20px]">Auto Delivery</h1>
              </div>
              <div className="flex flex-row gap-4 justify-center mt-[37px]">
                <div>
                  <Button
                    color="light"
                    className={`w-[156.25px] h-11 ${
                      checkoutForm.delivery_interval == 0
                        ? "bg-primary text-white hover:text-black"
                        : ""
                    }`}
                    onClick={() => {
                      let location = checkoutForm.shipping_id;
                      // console.log(location);
                      // setCheckoutForm((prevState) => ({
                      //   ...prevState,
                      //   delivery_interval: 0,
                      //   auto_delivery: 0,
                      //   tax: getTax({ cart: cart, id: location }),
                      // }));
                      const inputData = {
                        id: location,
                        interval: 0,
                        auto: 0,
                        cart: cart,
                      };
                      
                      getTax(inputData);
                    }}
                  >
                    None
                  </Button>
                </div>
                <div>
                  <Button
                    color="light"
                    className={`w-[156.25px] h-11 ${
                      checkoutForm.delivery_interval == 1
                        ? "bg-primary text-white hover:text-black"
                        : ""
                    }`}
                    onClick={() => {
                      let location = checkoutForm.shipping_id;
                      // setCheckoutForm((prevState) => ({
                      //   ...prevState,
                      //   delivery_interval: 1,
                      //   auto_delivery: 1,
                      //   tax: getTax({ cart: cart, id: location }),
                      // }));
                      const inputData = {
                        id: location,
                        interval: 1,
                        auto: 1,
                        cart: cart,
                      };

                      getTax(inputData);
                    }}
                  >
                    Every Month
                  </Button>
                </div>
                <div>
                  <Button
                    color="light"
                    className={`w-[156.25px] h-11 ${
                      checkoutForm.delivery_interval == 2
                        ? "bg-primary text-white hover:text-black"
                        : ""
                    }`}
                    onClick={() => {
                      let location = checkoutForm.shipping_id;
                      // setCheckoutForm((prevState) => ({
                      //   ...prevState,
                      //   delivery_interval: 2,
                      //   auto_delivery: 1,
                      //    tax: prevState.tax * 0.9,
                      // }));
                      const inputData = {
                        id: location,
                        interval: 2,
                        auto: 1,
                        cart: cart,
                      };

                      getTax(inputData);
                    }}
                  >
                    Every 2 Months
                  </Button>
                </div>
                <div>
                  <Button
                    color="light"
                    className={`w-[156.25px] h-11 ${
                      checkoutForm.delivery_interval == 3
                        ? "bg-primary text-white hover:text-black"
                        : ""
                    }`}
                    onClick={() => {
                      let location = checkoutForm.shipping_id;
                      // setCheckoutForm((prevState) => ({
                      //   ...prevState,
                      //   delivery_interval: 3,
                      //   auto_delivery: 1,
                      //   tax: getTax({ cart: cart, id: location }),
                      // }));
                      const inputData = {
                        id: location,
                        interval: 3,
                        auto: 1,
                        cart: cart,
                      };

                      getTax(inputData);
                    }}
                  >
                    Every 3 Months
                  </Button>
                </div>
              </div>
              <div className="flex justify-between pt-[40px] pl-[30px] pb-[31px]">
                {/* <div>
                  <p className="text-[#929791]">Next Scheduled Delivery</p>
                  <p>July 11th 2023</p>
                </div> */}
              </div>
            </div>

            <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
              <div className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                <h1 className="text-[#353535] text-[20px]">Shopping Cart</h1>
              </div>
              <div className="px-[30px] py-[20px] text-[#929791] font-[14px]">
                <p>Your Shopping Cart Items</p>
              </div>
              <hr className="h-px bg-gray-200 border-0 -mx-6" />

              {cart
                ? cart.map((value, index) => (
                    <Cart data={value} 
                      key={value.id}  
                      index = {index}
                      cart={cart} // Pass cart state
                      updateCart={updateCart} // Pass updateCart function
                      />
          
                  ))
                : "...loading"}

              <div className="flex flex-row pl-[34px] py-[23px]">
                <input
                  type="checkbox"
                  className="rounded-sm"
                  name="require_rep_consulting"
                  checked={checkoutForm.require_rep_consulting}
                  onChange={(e) => {
                    setCheckoutForm({
                      ...checkoutForm,
                      require_rep_consulting: Number(e.target.checked),
                    });
                  }}
                />
                <p className="text-[#929791] text-[12px] pl-2">
                  Click here for an Oasis representative to contact you for a
                  training session or additional product information.
                </p>
              </div>
            </div>
          </div>

          <div className="basis-1/3">
            <div className="bg-white rounded-xl dark:bg-gray-800 ml-5 mb-5 sticky top-0">
              <div className="flex justify-between border-b-2 border-[#EFF1F0]">
                <div className="py-[14px] pl-[30px]">
                  <h1 className="text-[#353535] text-[20px]">Subtotal</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>
                    {formatPrice.format(returnSubtotal().finalSubtotal)}

                    {/* Use this once Auto delivery is done
                       {checkoutForm.auto_delivery !== 0
                      ? formatPrice.format(returnSubtotal() * 0.9)
                      : formatPrice.format(returnSubtotal())} */}
                  </p>
                  <p className="text-[8px] text-[#929791]">U.S. Dollar</p>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Shippable Product</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p> {formatPrice.format(returnSubtotal().sum)}</p>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Backordered Product</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>{formatPrice.format(0)}</p>
                </div>
              </div>

              <div className="flex justify-between ">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Discount</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>-{formatPrice.format(returnSubtotal().totalDiscount)}</p>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Subtotal of Products</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>
                    {formatPrice.format(
                      returnSubtotal().sum - returnSubtotal().totalDiscount
                    )}
                  </p>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Total Freight</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>{formatPrice.format(selectedShipping)}</p>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Total Tax</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>{formatPrice.format(totalTax)}</p>
                </div>
              </div>

              {/* <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Auto Delivery Discount</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>$00.00</p>
                </div>
              </div> */}

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Total</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>{formatPrice.format(returnTotal())}</p>
                </div>
              </div>

              <div className="grid justify-items-center pb-[30px] pt-[37px]">
                <div>
                {hasPermission('Checkout', "place_order") && (
                <Button
                    pill={true}
                    className="theme-button"
                    
                    onClick={() => create()}
                  >
                    {processingPayment && (
                      <Spinner
                        aria-label="Alternate spinner button example"
                        size="sm"
                      />
                    )}{" "}
                    Place Order
                  </Button>
                )}
                {hasPermission('Checkout', "request_order") && (session?.user.group_id !=1) && (
                <Button
                    pill={true}
                    className="theme-button"
                    disabled
                    // onClick={}
                  >
                    {processingPayment && (
                      <Spinner
                        aria-label="proccesing"
                        size="sm"
                      />
                    )}{" "}
                    Submit Order Request
                  </Button>
                )}
                </div>
              </div>
            </div>
          </div>
          <ConfirmationModal
            isOpen={isConfirmModalOpen}
            onClose={() => {
              setIsConfirmModalOpen(false);
              setprocessingPayment(false);
            }}
            onConfirm={() => {
              setIsConfirmModalOpen(false);
              proceedWithOrder();
            }}
            message="An auto delivery order with the same configurations already exists. Do you wish to place order anyways?"
          />
        </div>
}
      </Layout>
    );
// }
}

// function invaildPaymentAlert({ alertMessage, alert, setAlert }) {
//   return (
//     <Alert
//       color="failure"
//       icon={HiInformationCircle}
//       onDismiss={() =>
//         setAlert({
//           ...alert,
//           payment: false,
//         })
//       }
//     >
//       <span>
//         <p>{alertMessage}</p>
//       </span>
//     </Alert>
//   );
// }

// function invaildLocationAlert({ locationAlertMessage, alert, setAlert }) {
//   return (
//     <Alert
//       color="failure"
//       icon={HiInformationCircle}
//       onDismiss={() =>
//         setAlert({
//           ...alert,
//           location: false,
//         })
//       }
//     >
//       <span>
//         <p>{locationAlertMessage}</p>
//       </span>
//     </Alert>
//   );
// }

// function successAlert({ alert, setAlert, alertMessage }) {
//   return (
//     <Alert
//       color="success"
//       icon={HiInformationCircle}
//       onDismiss={() =>
//         setAlert({
//           ...alert,
//           success: false,
//         })
//       }
//     >
//       <span>
//         <p>{alertMessage}</p>
//       </span>
//     </Alert>
//   );
// }
