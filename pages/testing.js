"use client";
import axios from "axios";
import { useSession, getSession } from "next-auth/react";
import { useEffect } from "react";
export default function Test() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <h1>Loading...</h1>;
  }

  if (status === "unauthenticated") {
    return <h1>Access Denied</h1>;
  }

  axios
    .get(`/api/getCart?location=${session.user.location_id}`)
    .then((response) => console.log(response.data.items));

  return (
    <>
      <h1>Signed in: {JSON.stringify(session)}</h1>
    </>
  );
}
