import { useSession, signOut, getSession } from "next-auth/react";
import Layout from "../components/layout";
import axios from "axios";
import { useState, useEffect } from "react";
import { FaCheckCircle, FaRegCircle } from "react-icons/fa";
import { FaArrowsRotate } from "react-icons/fa6";
import { useUserAcctContext } from "../contexts/UserAcctContext";
import { useTrainingContext } from "../contexts/TrainingContext";
import Link from "next/link";
import OrderSkeleton from "../components/skeleton/dashboard/OrderSkeleton";
import InvoiceSkeleton from "../components/skeleton/dashboard/InvoiceSkeleton";
import NotificationSkeleton from "../components/skeleton/dashboard/NotificationSkeleton";
import Image from "next/image";
import ResourceSkeleton from "../components/skeleton/dashboard/ResourceSkeleton";
import { Button } from "flowbite-react";
import { usePermissions } from "../contexts/PermissionsContext";
import { createPermission<PERSON>hecker } from "../components/utils";

const Dashboard = () => {
  const { data: session, status, update } = useSession();
  const [orders, setOrders] = useState([]);
  const [invoices, setInvoices] = useState([]);
  const [resources, setResources] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [thumbnail, setThumbnail] = useState({
    thumbnail_path: "",
    title: "",
    video_path: "",
  });
  const [latestNotification, setLatestNotification] = useState(null);
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);

  const { userAcct, setUserAcct } = useUserAcctContext();
  const { training, setTraining } = useTrainingContext();

  const trackingURLs = {
    FedEx: ['C1F', 'C2A', 'C2D', 'C3D', 'CFP', 'CFS', 'CGN', 'CIE', 'CIP', 'CSA', 'CTN', 'F14', 'FDX', 'FGN', 'FGR', 'FX3', 'FXG', 'N1A', 'N1P', 'N2A', 'N2B', 'N2D', 'N3D', 'NFS', 'NGN', 'NST', 'P1P', 'P1S', 'P2A', 'P2D', 'P3D', 'PFG', 'PFS', 'PI3', 'PIP', 'PST', 'R10', 'R11', 'R15', 'R17', 'R18', 'R20', 'R21', 'R26', 'R27', 'R2D', 'R3D', 'R3Y', 'R95', 'RG7', 'WF2', 'WFG', 'WFR'],
    UPS: ['C8A', 'CU1', 'CU2', 'CU3', 'CUG', 'CUI', 'CUN', 'NU3', 'NUG', 'P1A', 'PU2', 'PU3', 'PUA', 'PUG', 'PUI', 'PUN', 'WGP', 'WU2', 'WUG', 'WUP']
  };

  const getShippingProvider = (shippingMethod) => {
    const provider = Object.keys(trackingURLs).find(provider => trackingURLs[provider].includes(shippingMethod));
    return provider || 'neither';
  };

  const syncAccount = async (email) => {
    try {
      const res = await fetch("/api/syncJDE/syncAccount", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        console.log("Account Updated");
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while updating Account occurred:",
        error
      );
    }
  };

  const syncAll = async (account_id, location_id, group_id) => {
    try {
      const res = await fetch("/api/syncJDE/syncAll", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ account_id, location_id, group_id }),
      });
      if (res.status === 200) {
        console.log("Account, Orders Updated");
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error("An unexpected error while updating Account occurred:", error);
    }
  };


  const extractTextFromHTML = (htmlContent) => {
    const doc = new DOMParser().parseFromString(htmlContent, "text/html");

    const extractText = (node) => {
      let text = "";

      if (node.nodeType === Node.TEXT_NODE) {
        text += node.nodeValue;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        for (let childNode of node.childNodes) {
          text += extractText(childNode);
        }

        if (
          node.tagName === "P" ||
          node.tagName === "DIV" ||
          node.tagName === "BR"
        ) {
          text += "\n";
        } else if (node.tagName === "LI" || node.tagName === "TD") {
          text += " ";
        }
      }

      return text;
    };

    let finalText = "";
    for (let node of doc.body.childNodes) {
      finalText += extractText(node);
    }

    return finalText.trim();
  };

  useEffect(() => {
    setThumbnail({
      thumbnail_path: "",
      title: "",
      video_path: "",
    });
    const limit = 5;
    setIsLoading(true);

    const syncAndContinue = async () => {
      if (session) {
        if (!session.user.sync) {
          await syncAll(session.user.account_id, session.user.location_id, session.user.group_id);
          update({ sync: new Date() });
          console.log("no sync");
        } else {
          console.log("sync", session);
        }

        try {
          const ordersPromise = axios.get("/api/order/fetchAll", {
            params: {
              userId: session.user.id,
              groupId: session.user.group_id,
              locationId: session.user.location_id,
              accountId: session.user.account_id,
              limit,
            },
          });

          const resourcesPromise = axios.get("/api/resources/get");
          const invoicesPromise = axios.get("/api/invoice/all", {
            params: {
              userId: session.user.id,
              groupId: session.user.group_id,
              locationId: session.user.location_id,
              accountId: session.user.account_id,
              limit,
            },
          });

          const [ordersResponse, resourcesResponse, invoicesResponse] =
            await Promise.all([
              ordersPromise,
              resourcesPromise,
              invoicesPromise,
            ]);

          // Handle each response and set state as needed
          setOrders(ordersResponse.data);
          setResources(resourcesResponse.data);
          setInvoices(invoicesResponse.data);

          console.log("response", resourcesResponse.data);
          console.log("Refreshed orders, resources, and invoices");

          // syncAccount(session.user.email);
        } catch (error) {
          // Handle errors if any of the requests fail
          console.log("error", error);
        } finally {
          setTimeout(() => {
            setIsLoading(false);
          }, 1500);
        }
      }
    };
    syncAndContinue();

    const fetchLatestNotification = async () => {
      try {
        if (session) {
          const user_id = session.user.id;
          const response = await axios.get(
            `/api/userNotifications/get?id=${user_id}`
          );
          const notificationData = response.data;

          // Set the last notification as the first item
          const latestNotification =
            notificationData.length > 0 ? notificationData[0] : null;

          // Set the latest notification to state
          setLatestNotification(latestNotification);
        }
      } catch (error) {
        console.error("Error fetching latest notification:", error);
      }
    };

    // Fetch the latest notification when the component mounts
    fetchLatestNotification();
  }, [session]);

  if (resources[0] && thumbnail.thumbnail_path === "") {
    setThumbnail({
      thumbnail_path: resources[0].image_path,
      title: resources[0].title,
      video_path: resources[0].video_path,
    });
  }

  // console.log(resources[0]);
  // console.log("invoices", invoices);

  return (
    <Layout>
      <div className="flex flex-row gap-6">
        <div className="basis-2/3">
          {hasPermission('My Orders', "view") && (
            <>
              {isLoading ? (
                <OrderSkeleton />
              ) : (
                <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
                  <p className="text-3xl font-semibold">Orders</p>
                  <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400 overflow-auto">
                    <thead className="text-xs text-gray-700 text-center uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
                      <tr>
                        <th scope="col" className="px-4 py-3"></th>
                        <th scope="col" className="px-4 py-3">
                          Auto Delivery
                        </th>
                        <th scope="col" className="px-4 py-3">
                          Order Number
                        </th>
                        <th scope="col" className="px-4 py-3">
                          Tracking Number
                        </th>
                        <th scope="col" className="px-4 py-3">
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {orders.length < 1 ? (
                        <tr className="mt-4">
                          <td colSpan={3}>No available orders</td>
                        </tr>
                      ) : (
                        orders.map((item) => {
                          const inputDate = new Date(item.date);
                          const options = {
                            year: "numeric",
                            month: "short",
                            day: "numeric",
                            hour: "2-digit",
                            minute: "2-digit",
                          };
                          const formattedDate = inputDate.toLocaleDateString(
                            "en-US",
                            options
                          );

                          return (
                            <tr
                              className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                              key={item.id}
                            >
                              <th
                                scope="row"
                                className="px-4 py-4 text-gray-900 whitespace-nowrap dark:text-white"
                              >
                                <p className="font-medium">
                                  {item.User.first_name} {item.User.last_name}
                                </p>
                                <p className="font-light">{formattedDate}</p>
                              </th>
                              <td className="px-4 py-4 flex justify-center">
                                {item.auto_delivery === 1 ? (
                                  <FaCheckCircle color="#08447C" size="25px" />
                                ) : (
                                  <FaRegCircle color="#08447C" size="25px" />
                                )}
                              </td>
                              <td className="px-4 py-4 text-center">
                                {item.jde_id}
                              </td>
                              <td className="px-4 py-4 text-center text-blue-600">
                                {item.tracking && (
                                  <>
                                    {(() => {
                                      const provider = getShippingProvider(item.shipping_method);
                                      if (provider === 'FedEx') {
                                        return (
                                          <Link
                                            href={`https://www.fedex.com/fedextrack/no-results-found?trknbr=${item.tracking}`}
                                            target="_blank"
                                          >
                                            Track Order
                                          </Link>
                                        );
                                      } else if (provider === 'UPS') {
                                        return (
                                          <Link
                                            href={`https://www.ups.com/track?tracknum=${item.tracking}`}
                                            target="_blank"
                                          >
                                            Track Order
                                          </Link>
                                        );
                                      } else {
                                        return item.tracking;
                                      }
                                    })()}
                                  </>
                                )}
                              </td>
                              <td className="px-4 py-4">
                                {item.status === 1 && (
                                  <p className="text-center font-semibold p-2 text-blue-500 bg-blue-100 rounded-md">
                                    Submitted
                                  </p>
                                )}
                                {item.status === 2 && (
                                  <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                    In Progress
                                  </p>
                                )}
                                {item.status === 3 && (
                                  <p className="text-center font-semibold p-2 text-yellow-500 bg-yellow-100 rounded-md">
                                    Shipped
                                  </p>
                                )}
                                {item.status === 4 && (
                                  <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md">
                                    Delivered
                                  </p>
                                )}
                                {item.status === 9 && (
                                  <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                    Cancelled
                                  </p>
                                )}
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                  <div className="pt-14 pb-2 grid grid-cols-2">
                    <p className="text-xs text-gray-400 text-start my-auto mb-0 pb-0">
                      {session && session.user.sync &&
                        "Refreshed: " +
                        new Date(session.user.sync).toLocaleString("en-US", {
                          month: "2-digit",
                          day: "2-digit",
                          year: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: true, // Use 24-hour format
                        })}
                    </p>
                    <Link
                      href={"/orders"}
                      className="text-[#08447C] text-xl font-medium grid text-end my-auto"
                    >
                      View all
                    </Link>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
        <div className="basis-1/3">
          {hasPermission('My Orders', "view") && (
            <>
              {isLoading ? (
                <InvoiceSkeleton />
              ) : (
                <div className="h-full overflow-auto relative bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
                  <p className="text-3xl font-semibold">Invoices</p>
                  <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead className="text-xs text-gray-700 text-center uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
                      <tr>
                        <th scope="col" className="px-4 py-3"></th>
                        <th scope="col" className="px-4 py-3">
                          {/* Invoice Number */}
                        </th>
                        <th scope="col" className="px-4 py-3">
                          {/* Status */}
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {invoices.length < 1 ? (
                        <tr className="mt-4">
                          <td colSpan={3}>No available invoices</td>
                        </tr>
                      ) : (
                        invoices.map((item) => {
                          const inputDate = new Date(item.Order.date);
                          const options = {
                            year: "numeric",
                            month: "short",
                            day: "numeric"
                          };
                          const formattedDate = inputDate.toLocaleDateString(
                            "en-US",
                            options
                          );

                          return (
                            <tr
                              className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
                              key={item.id}
                            >
                              <th
                                scope="row"
                                className="px-4 py-4 text-gray-900 whitespace-nowrap dark:text-white"
                              >
                                <p className="font-medium">
                                  {item.Order.User.first_name}{" "}
                                  {item.Order.User.last_name}
                                </p>
                                <p className="font-light text-xs">
                                  {formattedDate}
                                </p>
                              </th>
                              <td className="px-4 py-4 text-center">
                                {/* {item.invoice_num} */}
                              </td>
                              <td className="px-4 py-4">
                                <div>
                                  {item.status === 1 && (
                                    <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md w-full float-right">
                                      Paid
                                    </p>
                                  )}
                                  {item.status === 2 && (
                                    <p className="text-center font-semibold p-2 text-amber-500 bg-amber-100 rounded-md w-full float-right">
                                      Due
                                    </p>
                                  )}
                                  {item.status === 3 && (
                                    <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md w-full float-right">
                                      Past Due
                                    </p>
                                  )}
                                  {item.status === 4 && (
                                    <p className="text-center font-semibold p-2 text-blue-500 bg-blue-100 rounded-md">
                                      Pending
                                    </p>
                                  )}
                                </div>
                              </td>
                            </tr>
                          );
                        })
                      )}
                    </tbody>
                  </table>
                  <div className="absolute inset-x-0 bottom-0 grid grid-cols-2 px-6 pb-8">
                    <p className="text-xs text-gray-400 text-start my-auto mb-0 pb-0">
                      {session && session.user.sync &&
                        "Refreshed: " +
                        new Date(session.user.sync).toLocaleString("en-US", {
                          month: "2-digit",
                          day: "2-digit",
                          year: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: true, // Use 24-hour format
                        })}
                    </p>
                    <Link
                      href={"/invoices"}
                      className="text-[#08447C] text-xl font-medium grid text-end my-auto"
                    >
                      View all
                    </Link>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
      <div className="flex flex-row gap-6 mt-6">
        <div className="basis-2/3">
          {isLoading ? (
            <ResourceSkeleton />
          ) : (
            <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800 grid grid-cols-2">
              <div className="">
                <p className="text-4xl font-semibold">Resources</p>
                <p className="mt-2 text-sm text-gray-400">
                  {resources.length} videos
                </p>
                <ol className="mt-4 text-gray-400" start={1}>
                  {resources.slice(0, 5).map((x, index) => (
                    <li
                      key={x.id}
                      onMouseEnter={() => {
                        setThumbnail({
                          thumbnail_path: x.image_path,
                          title: x.title,
                          video_path: x.video_path,
                        });
                        console.log(x.title);
                      }}
                    >
                      <Link
                        href={"/training"}
                        as={"/training"}
                        onClick={() => {
                          setTraining({
                            title: x.title,
                            video_path: x.video_path,
                          });
                        }}
                        passHref
                      >
                        {index + 1}. {x.title}
                      </Link>
                    </li>
                  ))}
                </ol>

                {/* use newly created TrainingContext.js to pass the values for each options so video can be auto selected on training page */}
                <Link
                  href={"/training"}
                  onClick={() => {
                    setTraining({
                      title: "",
                      video_path: "",
                    });
                  }}
                >
                  <p className="text-[#08447C] mt-4">
                    View all Pre-recorded videos
                  </p>
                </Link>
              </div>
              <div className="my-auto collapse lg:visible ml-16">
                {thumbnail.thumbnail_path === "" ? (
                  <></>
                ) : (
                  <Link
                    href={"/training"}
                    onClick={() => {
                      setTraining({
                        title: thumbnail.title,
                        video_path: thumbnail.video_path,
                      });
                    }}
                  >
                    <Image
                      src={thumbnail.thumbnail_path || "/knife_image.png"}
                      alt={thumbnail.title}
                      width={250}
                      height={150}
                      className=""
                    />
                  </Link>
                )}
                {/* <Image src={thumbnail} width={250} height={150} className="" /> */}
              </div>
            </div>
          )}
        </div>
        <div className="basis-1/3">
          {isLoading ? (
            <NotificationSkeleton />
          ) : (
            <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
              <p className="text-2xl font-semibold mb-2">Notifications</p>
              {latestNotification ? (
                <>
                  {/* Display the latest notification details */}
                  {latestNotification.Notifications.image_path && (
                    <div className="image-wrapper -mx-6 lg:-mx-8">
                      <Image
                        src={latestNotification.Notifications.image_path || "/knife_image.png"}
                        alt={latestNotification.Notifications.headline || "Notification"}
                        width={350}
                        height={159}
                        className="image-content"
                      />
                    </div>
                  )}
                  <div className="py-2">
                    <p className="font-bold">
                      {latestNotification.Notifications.headline}
                    </p>
                    <p className="line-clamp-2 leading-tight mt-1">
                      {extractTextFromHTML(
                        latestNotification.Notifications.body
                      )}
                    </p>
                    <Link
                      href={`/inbox?id=${latestNotification.id}`}
                      className="text-blue-500 text-md font-normal float-left mt-1"
                    >
                      Open
                    </Link>
                  </div>
                </>
              ) : (
                <p className="mt-2">No current notifications</p>
              )}
              <div className="pt-4 pb-10">
                <Link
                  href={{ pathname: "/inbox" }}
                  className="text-[#08447C] text-xl font-medium float-right"
                >
                  View all
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard;

function Orders(item) {
  return <div></div>;
}
