import { useEffect, useState } from "react";
import { useRouter } from "next/router";
import { useSession } from "next-auth/react";
import {
  Dropdown,
  But<PERSON>,
  Accordion,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  B<PERSON>crumb,
  TextInput,
} from "flowbite-react";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { useProductDetailsContext } from "../contexts/ProductDetailsContext";
import { useCart } from "../contexts/CartContext";
import { usePromotionsContext } from '../contexts/PromotionsContext'; // Import the promotion context
import Layout from "../components/layout";
import axios from "axios";

const ProductDescription = ({ description, details, videos, resources }) => {
  return (
    <Accordion flush={true} className="w-full">
      <Accordion.Panel>
        <Accordion.Title className="text-grey-800 font-normal bg-white">
          Product Description
        </Accordion.Title>
        <Accordion.Content>
          <div
          className="html-notif" // Use the 'prose' class to style the HTML content similarly to the CKEditor
          dangerouslySetInnerHTML={{ __html: description }}
          />
          {/* <div dangerouslySetInnerHTML={{ __html: description }} /> */}
        </Accordion.Content>
      </Accordion.Panel>
      <Accordion.Panel>
        <Accordion.Title className="text-grey-800 font-normal bg-white">
          Details
        </Accordion.Title>
        <Accordion.Content>
          <div
          className="html-notif product-details" // Use the 'prose' class to style the HTML content similarly to the CKEditor
          dangerouslySetInnerHTML={{ __html: details }}
          />
          {/* <div
            dangerouslySetInnerHTML={{ __html: details }}
            className="product-details"
          /> */}
        </Accordion.Content>
      </Accordion.Panel>
      <Accordion.Panel>
        <Accordion.Title className="text-grey-800 font-normal bg-white">
          Resources
        </Accordion.Title>
        <Accordion.Content>
          <div
          className="html-notif" // Use the 'prose' class to style the HTML content similarly to the CKEditor
          dangerouslySetInnerHTML={{ __html: resources }}
          />
        </Accordion.Content>
      </Accordion.Panel>
      <Accordion.Panel>
        <Accordion.Title className="text-grey-800 font-normal bg-white">
          Video
        </Accordion.Title>
        <Accordion.Content>
        <div
          className="html-notif" // Use the 'prose' class to style the HTML content similarly to the CKEditor
          dangerouslySetInnerHTML={{ __html: videos }}
          />
        </Accordion.Content>
      </Accordion.Panel>
    </Accordion>
  );
};

const Product = () => {
  const [product, setProduct] = useState(null);
  const [defaultImage, setDefaultImage] = useState(
    {
      path: "",
      extension: "",
    }
  );
  const [quantity, setQuantity] = useState(1);
  const [productAlert, setProductAlert] = useState({
    show: false,
    message: "",
    color: "",
    icon: "",
  });
  const router = useRouter();
  const { push } = useRouter();
  const data = router.query;
  const { data: session, status } = useSession();
  const { productDetails, setProductDetails } = useProductDetailsContext();
  const { cart: cartContext, updateCart } = useCart();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const [price, setPrice] = useState(null);
  const { promotions } = usePromotionsContext(); // Access promotions from context

  console.log("product cart Context", cartContext);

  // console.log(data);

  function truncateProductName(str) {
    if (str.length > 30) {
      str = str.slice(0, 50).trim();
      str += "...";
    }

    return str;
  }

  const successAddNotification = (name, qty) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: (
        <span>
          <span className="font-bold">{truncateProductName(name)}</span> added
          to cart. <span className="font-bold">Qty: {qty}</span>
        </span>
      ),
      show: true,
    };

    addNotifications(newNotification);
  };

  const failureAddNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: "Failed To Add Product To Cart. Please Try Again.",
      show: true,
    };

    addNotifications(newNotification);
  };

  

  function addToCart() {
    // console.log(data);
    // return;

    axios
      .post("api/cart", {
        id: data.id,
        account_id: session.user.account_id,
        user_id: session.user.id,
        qty: quantity,
        price: price,
        promotions: promotions
      })
      .then((response) => {
        axios
          .get(
            `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
          )
          .then((response) => {
            const updatedCart = response.data.items;
            updateCart(updatedCart);
            successAddNotification(data.name, quantity);
          });
        setQuantity(1);
        console.log("product cart", cartContext);
      })
      .catch((response) => {
        // console.log(response);
        failureAddNotification();
      });
  }

  function buyNow() {

    axios
      .post("api/cart", {
        id: data.id,
        account_id: session.user.account_id,
        user_id: session.user.id,
        qty: quantity,
        price: price,
        promotions: promotions
      })
      .then((response) => {
        push("/cart");
      })
      .catch((response) => {
        console.log(response.data);
      });
  }

  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  const parent_id = productDetails.parent.id;

  useEffect(() => {
    if (!data.sku  || !session) {
      return;
    }

    // Call the updateStockPrice API first
    axios
    .post("/api/products/updateStockPrice", { id: data.id, account_jde: session.user.Account.jde_id })
    .then((response) => {
      setPrice(response.data.price);
      fetch(`api/product?sku=${data.sku}&id=${data.id}`)
        .then((response) => response.json())
        .then((json) => {
          setProduct(json);
          if (json.product[0].ProductImage.length > 0){
            setDefaultImage({path:json.product[0].ProductImage[0].path,extension:json.product[0].ProductImage[0].extension});
          } 
          console.log("route", json.route);
          setProductDetails({
            ...productDetails,
            selectedProduct: json.product[0],
            parent: { name: json.route[0].name, id: json.route[0].id },
            firstCat: { name: json.route[1].name, id: json.route[1].id },
            secondCat: json.route[2]
              ? { name: json.route[2].name, id: json.route[2].id }
              : "",
          });
        })
        .catch((error) => console.error(error));
    })
    .catch((error) => {
      console.error("Error updating stock and price:", error);
      // Handle error if needed
    });
  }, [data.sku, session]);
  console.log("productDetails", productDetails);

  return (
    <Layout>
      <div className="h-full overflow-y-auto bg-white rounded-xl dark:bg-gray-800">
        {productAlert.show && (
          <Alert
            color={productAlert.color}
            icon={productAlert.icon}
            className="rounded-none"
            onDismiss={() => setProductAlert({})}
          >
            <span>
              <p>{productAlert.message}</p>
            </span>
          </Alert>
        )}
        <div className="bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
          <Breadcrumb>
            <Breadcrumb.Item
              href={`products?category=${productDetails.parent.name}&parentid=${productDetails.parent.id}`}
            >
              <p>{productDetails.parent.name}</p>
            </Breadcrumb.Item>
            <Breadcrumb.Item
              href={`products?category=${productDetails.parent.name}&parentid=${productDetails.parent.id}&catid=${productDetails.firstCat.id}&subid=${productDetails.secondCat.id}&maincategory=1`}
            >
              {/* <p>{productDetails.firstCat}</p> */}
              <p>{productDetails.firstCat.name}</p>
            </Breadcrumb.Item>
            {productDetails.secondCat && (
              <Breadcrumb.Item
                href={`products?category=${productDetails.parent.name}&parentid=${parent_id}&catid=${productDetails.firstCat.id}&subid=${productDetails.secondCat.id}&maincategory=0`}
              >
                {/* <p>{productDetails.secondCat}</p> */}
                <p>{productDetails.secondCat.name}</p>
              </Breadcrumb.Item>
            )}
            <Breadcrumb.Item>
              <p>{data.name}</p>
            </Breadcrumb.Item>
          </Breadcrumb>

          {/* <Link
            // href={`/products?category=${parentCat.category}#`}
            href={`/products?category=Vision`}
            className="flex m-auto text-gray-600"
          >
            <HiArrowCircleLeft className="h-6 w-6 mr-1" /> Back to Products
          </Link> */}

          <div className="flex flex-row pb-6">
            <div className="basis-1/2 pl-5 pr-11 pt-10">
              {product ? (
                product.product[0].ProductImage &&
                product.product[0].ProductImage.length > 0 ? (
                  <div className="">
                    <img
                      src={`${
                        process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                        "oasis-product-images/"
                      }${defaultImage.path}_l.${
                        defaultImage.extension
                      }`}
                      width={800}
                      height={800}
                      className="border "
                    ></img>
                  </div>
                ) : (
                  <div className="">
                    <img
                      src="/no_image.jpeg"
                      width={800}
                      height={800}
                      className="border"
                    ></img>
                  </div>
                )
              ) : (
                "...img loading"
              )}
              <div className="flex flex-row gap-2 mt-4 justify-center">
                {product
                  ? product["product"][0]["ProductImage"].map((value) => (
                      <div className="" key={value.path}>
                        <button onClick={() => setDefaultImage({path:value.path,extension:value.extension})}>
                          <img
                            src={`${
                              process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                              "oasis-product-images/"
                            }${value.path}_s.${
                              value.extension
                            }`}
                            width={100}
                            height={100}
                            className="border"
                          ></img>
                        </button>
                      </div>
                    ))
                  : "loading..."}
              </div>
            </div>
            {/* {product ?  <div className="basis-1/2 pl-32 pt-32"><img src={`/oasis-product-images/${defaultImage}_l.jpg`} width={800} height={800} ></img></div> : '...img loading' } */}
            <div className="basis-1/2 pr-5 pl-11 pt-10">
              <div className="flex justify-between pb-11">
                <div className="pr-2">
                  {data.name} <br /> <p className="text-gray-400">{data.sku}</p>
                </div>
                <div className="order-last">
                  <Badge
                    color={
                      product
                        ? product.product[0].stock == 0
                          ? "failure"
                          : "success"
                        : "success"
                    }
                    size="sm"
                    className="w-max"
                  >
                    {product
                      ? product.product[0].stock == 0
                        ? "Out Of Stock"
                        : "In Stock"
                      : "loading"}
                  </Badge>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-y-3 gap-x-4 py-11">
                <div className=" self-center font-semibold text-lg">
                  {product
                    ? formatPrice.format(price)
                    : "..loading"}
                </div>
                <div className="justify-self-end self-center ">
                  <TextInput
                    type="number"
                    // className="inline-block align-top bg-[#F8F8F8] w-[75px] rounded-md mt-2"
                    placeholder={quantity}
                    value={quantity}
                    onChange={(e) => setQuantity(e.target.value)}
                    // onBlur={(e) => updateCartQty(e)}
                  ></TextInput>
                </div>
                <div className="col-span-2">
                  <Button
                    pill={true}
                    className="theme-button w-full"
                    onClick={addToCart}
                    disabled={
                      product
                        ? product.product[0].stock == 0
                          ? true
                          : false
                        : false
                    }
                  >
                    Add to Cart
                  </Button>
                </div>

                <div className="grow self-center col-start-3 col-span-2 ">
                  <Button
                    color="light"
                    pill={true}
                    onClick={buyNow}
                    className="w-full px-4 py-2 text-primary border-primary "
                    disabled={
                      product
                        ? product.product[0].stock == 0
                          ? true
                          : false
                        : false
                    }
                  >
                    Buy Now
                  </Button>
                </div>
              </div>
              <div className="flex">
                {/* {product ? <ProductDescription description={product.description}/> : 'loading...'} */}
                {product ? (
                  <ProductDescription
                    description={product["product"][0].description}
                    details={product.product[0].details}
                    videos={product.product[0].videos}
                    resources={product.product[0].resources}
                  />
                ) : (
                  "loading..."
                )}
              </div>
              <div className="flex pt-5">
                <p className="text-2xl font-medium pt-4 hidden">
                  Frequently bought together
                </p>
              </div>
            </div>
          </div>
          {/* <div className="flex flex-row">
          {product ? product['product'][0]['ProductImage'].map((value) => <div className="h-96"><button onClick={() => setDefaultImage(value.path)}><img className="pl-32 pt-4 h-[100px]" src={`/oasis-product-images/${value.path}_s.jpg`}></img></button></div> ) : 'loading...'}
        </div> */}
        </div>
      </div>
    </Layout>
  );
};

export default Product;
