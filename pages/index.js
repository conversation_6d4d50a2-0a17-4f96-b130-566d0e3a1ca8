import * as React from "react";
import { useState, useRef, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/router";
import { UseContextProvider } from "../contexts/StepperContext";
import { Card, Modal, Navbar, Tabs } from "flowbite-react";
import Login from "../components/login";
import RegisterForm from "../components/registerForm";
import RestoreForm from "../components/RestoreForm";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import AllAlerts from "../components/allAlerts";



export default function Home() {
  const { data: session } = useSession();
  const router = useRouter();
  const currentYear = new Date().getFullYear();
  const { notifications } = useGlobalAlertContext();
  const [isSyncingComplete, setIsSyncingComplete] = useState(true);


  // Check if the user session exists, and if yes, redirect to /dashboard
  useEffect(() => {
    if (session && isSyncingComplete) {
      router.push("/dashboard");
    }
  }, [session, isSyncingComplete, router]);

  const [activeTab, setActiveTab] = useState(0);
  const [modal, setModal] = useState(false);
  const tabsRef = useRef(null);
  const props = { setActiveTab, tabsRef };


  const changeTab = (tab) => {
    props.tabsRef.current?.setActiveTab(tab);
  };


  return (
    <div className="w-screen bg-black">
      <div className="flex flex-col items-center justify-center bg-[url('/<EMAIL>')] bg-cover">
        <Card className="w-3/4 md:w-2/4 mt-20">
          <Navbar fluid={true} className="m-auto ">
            <Navbar.Brand href="/">
              <img
                src="/oasis_logo.png"
                className="mr-3 h-6 sm:h-9"
                alt="Oasis Logo"
              />
            </Navbar.Brand>
          </Navbar>
          <hr className="h-px bg-gray-200 border-0 -mx-6" />
          <UseContextProvider>
            <Tabs.Group
              aria-label="Tabs with underline"
              style="underline"
              className="-mx-3 flex flex-nowrap custom-tab"
              ref={props.tabsRef}
              onActiveTabChange={(tab) => props.setActiveTab(tab)}
            >
              <Tabs.Item title="Login" active={activeTab === 0} >
                <Login changeTab={changeTab} setIsSyncingComplete={setIsSyncingComplete}></Login>
              </Tabs.Item>
              <Tabs.Item title="Register" active={activeTab === 1}>
                <RegisterForm></RegisterForm>
              </Tabs.Item>
            </Tabs.Group>
          </UseContextProvider>
        </Card>
        <div className="mx-auto mb-10 pt-20 g:flex place-content-center justify-between items-center">
          <p className="text-center">
            © {currentYear} OASIS Medical Inc. All rights reserved
          </p>
        </div>
        <div className="fixed flex flex-col gap-4 rounded-lg right-10 bottom-10 opacity-95 shadow-sm">
              {notifications.map((notification, index) => {
                // console.log("single notification", notification);

                return <AllAlerts id={index} notification={notification} />;
              })}
            </div>
      </div>
    </div>
  );
}
