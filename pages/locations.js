import { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import { Checkbox } from "flowbite-react";
import { MdAddCircle } from "react-icons/md";
import { <PERSON><PERSON>, Modal, Alert } from "flowbite-react";
import Layout from "../components/layout";
import LocationRow from "../components/location/LocationRow";
import LocationAdd from "../components/location/LocationAdd";
import axios from "axios";
import { stateNames } from "../components/utils";
import { HiInformationCircle } from "react-icons/hi";
import { useLocationsContext } from "../contexts/LocationsContext";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { usePermissions } from "../contexts/PermissionsContext";
import { createPermissionChecker } from "../components/utils";
import RouteGuard from "../components/RouteGuard";

const Locations = () => {
  const { locations, setLocations } = useLocationsContext();
  const [partnerType, setPartnerType] = useState([]);
  const [locationID, setLocationID] = useState();
  const [updatedDefault, setUpdatedDefault] = useState();
  const [modal, setModal] = useState(false);
  const { data: session, status } = useSession();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);

  const handleLocationEdit = (data) => {
    const account_id = data;
    const user_id = session["user"]["id"];

    axios
      .get("/api/location/get", {
        params: {
          user_id: user_id,
          account_id: account_id,
          location_id: session["user"]["location_id"],
          user_group: session["user"]["group_id"],
        },
      })
      .then((response) => {
        setLocations(response.data.item);
      });
  };

  const handleLocationAdd = (account) => {
    const account_id = account;
    const user_id = session["user"]["id"];

    axios
      .get("/api/location/get", {
        params: {
          user_id: user_id,
          account_id: account_id,
          location_id: session["user"]["location_id"],
          user_group: session["user"]["group_id"],
        },
      })
      .then((response) => {
        setLocations(response.data.item);
      });
  };

  useEffect(() => {
    if (session) {
      const account_id = session["user"]["account_id"];
      const user_id = session["user"]["id"];

      axios
        .get("/api/location/get", {
          params: {
            user_id: user_id,
            account_id: account_id,
            location_id: session["user"]["location_id"],
            user_group: session["user"]["group_id"],
          },
        })
        .then((response) => {
          setLocations(response.data.item);
        });

      axios.get("/api/partnerType/get").then((response) => {
        setPartnerType(response.data.formattedItems);
      });

      axios.get(`/api/user/getById?id=${user_id}`).then((response) => {
        setLocationID(response.data[0].location_id);
      });
    }
  }, [session]);


  // if (session) {
    return (
      <Layout>
        <RouteGuard section="Locations">
        {/* {alert.alertShow && locationsAlert({ alert, setAlert })} */}
        <div className="relative h-full overflow-x-auto overflow-y-auto bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
          <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <caption className="p-8 text-2xl font-semibold text-left text-gray-900 bg-white dark:text-white dark:bg-gray-800">
              <div className="flex justify-between">
                <div className="">Locations</div>
                <div className="">
                {hasPermission('Locations', "add") && (
                  <Button
                    color="#08447C"
                    outline={1}
                    onClick={() => {
                      setModal(true);
                    }}
                  >
                    <MdAddCircle className="mr-2 h-5 w-5 text-[#08447C]" />
                    Add New
                  </Button>
                )}
                  {/* <AddLocation
                  locations={locations}
                  setLocations={setLocations}
                /> */}
                </div>

                <Modal
                  size="4xl"
                  show={modal}
                  dismissible
                  onClose={() => {
                    setModal(false);
                  }}
                >
                  <LocationAdd
                    partnerType={partnerType}
                    stateNames={stateNames}
                    locations={locations}
                    setModal={setModal}
                    onUpdate={handleLocationAdd}
                    session={session}
                    alert={notifications}
                    setAlert={addNotifications}
                  />
                </Modal>
              </div>
            </caption>
            <thead className="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
              <tr>
                <th scope="col" className="px-6 py-3"></th>
                <th scope="col" className="px-6 py-3">
                  Practice
                </th>
                <th scope="col" className="px-6 py-3">
                  Address
                </th>
                <th scope="col" className="px-6 py-3">
                  Phone
                </th>
                <th scope="col" className="px-6 py-3 text-center">
                  Type
                </th>
                <th scope="col" className="px-6 py-3 text-center">
                  Default Location
                </th>
                {hasPermission('Locations', "edit") && (
                <th scope="col" className="px-6 py-3 text-center">
                  Edit
                </th>
                )}
                {hasPermission('Locations', "delete") && (
                <th scope="col" className="px-6 py-3 text-center">
                  Delete
                </th>
                )}
              </tr>
            </thead>
            <tbody>
              {Array.isArray(locations) && locations.length > 0 ? (
                locations.map((item) => {
                  const isCheckedProp = item.default === 1 ? 1 : 0;

                  return (
                    <LocationRow
                      location={item}
                      key={item.id}
                      isCheckedProp={isCheckedProp}
                      partnerType={partnerType}
                      onUpdate={handleLocationEdit}
                      session={session}
                      alert={notifications}
                      setAlert={addNotifications}
                    />
                  );
                })
              ) : (
                <LocationRow
                  location={locations}
                  key={locations.id}
                  isCheckedProp={locations.default}
                  partnerType={partnerType}
                  onUpdate={handleLocationEdit}
                  session={session}
                  alert={notifications}
                  setAlert={addNotifications}
                />
              )}
            </tbody>
          </table>
        </div>
        </RouteGuard>
      </Layout>
    );
  // }
};

export default Locations;
