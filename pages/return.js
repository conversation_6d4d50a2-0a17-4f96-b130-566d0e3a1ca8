import { useSession, signOut } from "next-auth/react";
import Layout from "../components/layout";
import { Badge, Button, Label, TextInput, Textarea } from "flowbite-react";
import Select from "react-select";
import { MdAddCircle, MdDelete } from "react-icons/md";
import { useState, useEffect } from "react";
import axios from "axios";
import { useRouter } from "next/router";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";

const Returns = () => {
  const router = useRouter();
  const { push } = useRouter();
  const param = router.query;
  const reasons = [
    { value: "damaged", label: "Damaged/Broken" },
    { value: "extra", label: "Ordered Extra" },
    { value: "other", label: "Other" },
  ];

  const [lineItems, setLineItems] = useState([{ product_id: "", qty: 1 }]);
  const { data: session, status } = useSession();
  const [invoices, setInvoices] = useState([]);
  const [selectedInvoiceItem, setSelectedInvoiceItem] = useState(null);
  const [formDisabled, setFormDisabled] = useState(false);
  const [returnStatus, setReturnStatus] = useState(null);
  const [processing, setProcessing] = useState(false);
  const { notifications, addNotifications } = useGlobalAlertContext();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [newRmaId, setNewRmaId] = useState(null);

  const successUserNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: "Succesfully submitted return request.",
      show: true,
    };


    addNotifications(newNotification);
  };

  const failureUserNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };

  const addLineItem = () => {
    setLineItems((prevLineItems) => [
      ...prevLineItems,
      { product_id: "", qty: 1 },
    ]);

    setFormData((prevData) => ({
      ...prevData,
      lineItems: [
        ...prevData.lineItems,
        { product_id: "", qty: 1 }, // Set qty to 1 by default
      ],
    }));
  };

  const updateLineItem = (index, field, value, orderedQty) => {
    if (field == "qty") {
      console.log("Adjusting Qty...");

      if (value < 1) {
        //add global alert
        failureUserNotification("Qty cannot be less than 1");
        return;
      }
      if (value > orderedQty) {
        //add global alert
        failureUserNotification("Qty cannot be greater than what was ordered");
        return;
      }
    }
    setLineItems((prevLineItems) =>
      prevLineItems.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    );
    setFormData((prevData) => ({
      ...prevData,
      lineItems: prevData.lineItems.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  const removeLineItem = (index) => {
    setLineItems((prevLineItems) =>
      prevLineItems.filter((item, i) => i !== index)
    );
    setFormData((prevData) => ({
      ...prevData,
      lineItems: prevData.lineItems.filter((item, i) => i !== index),
    }));
  };

  const setProductOptionsBasedOnInvoice = () => {
    if (selectedInvoiceItem) {
      let products;
      if (selectedInvoiceItem.ReturnProducts) {
        products = selectedInvoiceItem.ReturnProducts.map((returnProduct) => ({
          value: returnProduct.product_id,
          label: returnProduct.Product.name,
          qty: returnProduct.qty,
        }));
      } else {
        // console.log("selected invoice", selectedInvoiceItem);
        products = selectedInvoiceItem.Order.OrderedProduct.map((product) => ({
          value: product.Product.id,
          label: product.Product.name,
          qty: product.qty,
        }));
      }
      return products;
    }
    return [];
  };

  const [formData, setFormData] = useState({
    invoiceNum: "",
    lineItems: [{ product_id: "", qty: 1 }],
    reason: "",
    reasonNotes: "",
  });

  const updateFormData = (field, value) => {
    setFormData((prevData) => ({
      ...prevData,
      [field]: value,
    }));
  };

  useEffect(() => {
    if (session) {
      axios
        .get("/api/invoice/getForReturnList", {
          params: {
            userId: session.user.id,
            groupId: session.user.group_id,
            locationId: session.user.location_id,
            accountId: session.user.account_id,
          },
        })
        .then((response) => {
          setInvoices(response.data);
        });
    }
  }, [session, lineItems]);

  const invoiceOptions = invoices.map((invoice) => ({
    value: invoice.id,
    label: invoice.invoice_num,
  }));

  useEffect(() => {
    if (!param.id) {
      return;
    }
    fetch(`/api/return/get?id=${param.id}`)
      .then((response) => response.json())
      .then((form) => {
        const updatedLineItems = form.ReturnProducts.map((returnProduct) => ({
          product_id: returnProduct.product_id,
          qty: returnProduct.qty,
        }));
        const updatedFormData = {
          invoiceNum: form.invoice_id,
          lineItems: updatedLineItems,
          reason: form.reason,
          reasonNotes: form.reason_notes,
          rma: form.rma_num,
        };
        setSelectedInvoiceItem(form);
        setLineItems(updatedLineItems);
        setFormData(updatedFormData);
        setReturnStatus(form.status);
        setFormDisabled(true);
      })
      .catch((error) => console.error(error));
  }, [param.id]);

  // Function to handle form submission
  const handleSubmit = async () => {
    setProcessing(true);
    try {
      const requestData = {
        invoiceNum: formData.invoiceNum,
        lineItems: formData.lineItems,
        reason: formData.reason,
        reasonNotes: formData.reasonNotes,
      };
      console.log(requestData);
      // return;

      await axios
        .post("/api/return/create", requestData)
        .then((response) => {
          setProcessing(false);
          successUserNotification();
          setShowSuccessMessage(true); // Hide form and show success message
          setNewRmaId(response.data.rma_num);
        })
        .catch((error) => {
          console.log(error.message)
          setProcessing(false);
          failureUserNotification("Return Form could not be submitted, please try again");
        });
    } catch (error) {
      setProcessing(false);
      console.error("Error:", error);
      // Handle errors here
      failureUserNotification("An Error Ocurred: "+error.message)
    }
  };

  return (
    <Layout>
      <div className="h-fill bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
        <div>
          <p className="text-2xl font-semibold pt-5 pb-4">Return Form</p>
          <hr className="h-px bg-gray-200 border-0 -mx-8" />
        </div>
        <div className="py-8">
        {showSuccessMessage ? (
            <div className="success-message">
              <h2 className="text-xl font-semibold mb-4">Return Created</h2>
              <p>Your return request has been submitted successfully. Please ship the item(s) to this address:</p>
              <address className="mt-4 mb-4">
                OASIS Medical<br />
                Attn: QA Department<br />
                514 S. Vermont Ave.<br />
                Glendora, CA 91741
              </address>
              <p>
                Be sure to specify RMA # <strong>{newRmaId}</strong> with the packing materials and in any correspondence with Oasis Medical regarding this return.
              </p>
              <p className="mt-4">
                Other shipping arrangements may be available depending on the situation. Please contact Customer Service at <a href="mailto:<EMAIL>" className="text-blue-500"><EMAIL></a> or (844) 820-8940 to discuss possible alternate arrangements.
              </p>
            </div>
          ) : (
            <>
          {!formDisabled && (
            <p className="font-medium">
              Please select your invoice or upload the invoice for the product
              you are returning.
            </p>
          )}
          {formDisabled && (
            <>
              <p className="font-medium pb-2">RMA# {formData.rma}</p>

              {returnStatus === 1 && (
                <Badge className="text-center font-semibold p-2 text-amber-500 bg-amber-100 rounded-md w-fit">
                  Pending
                </Badge>
              )}
              {returnStatus === 2 && (
                <Badge className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md w-fit">
                  Approved
                </Badge>
              )}
              {returnStatus === 3 && (
                <Badge className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md w-fit">
                  Denied
                </Badge>
              )}
            </>
          )}

          <div className="w-52 mt-10 ml-10">
            <div className="mb-0.5 block ">
              <Label
                htmlFor="invoice_num"
                value="Invoice Number"
                className="text-xs"
              />
            </div>
            <Select
              options={invoiceOptions}
              id="invoice_num"
              required={true}
              isDisabled={formDisabled}
              placeholder="Invoice Number"
              className="custom-select"
              onChange={(selectedOption) => {
                const invoiceNum = selectedOption.value;
                const selectedInvoice = invoices.find(
                  (option) => option.id === invoiceNum
                );
                setSelectedInvoiceItem(selectedInvoice);
                updateFormData("invoiceNum", invoiceNum);
              }}
              value={invoiceOptions.find(
                (option) => option.value === formData.invoiceNum
              )}
            />
          </div>

          <div className="mt-10">
            {!formDisabled && (
              <p className="font-medium">
                Please select your items being returned
              </p>
            )}
            {lineItems.map((item, index) => (
              <div key={index} className="flex flex-row gap-8 mt-5 ml-10 items-center">
                <div className="w-52">
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor={`product_id_${index}`}
                      value="Item Being Returned"
                      className="text-xs"
                    />
                  </div>

                  <Select
                    options={setProductOptionsBasedOnInvoice()}
                    id={`product_id_${index}`}
                    required={true}
                    isDisabled={formDisabled}
                    placeholder="Choose One"
                    className="custom-select"
                    value={setProductOptionsBasedOnInvoice().find(
                      (option) => option.value === item.product_id
                    )}
                    onChange={(selectedOption) => {
                      const product_id = selectedOption.value;
                      console.log("updated:");
                      updateLineItem(index, "product_id", product_id);
                      updateLineItem(index, "qty", 1);
                    }}
                  />
                </div>
                <div className="w-14">
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor={`qty_${index}`}
                      value={`Quantity`}
                      className="text-xs"
                    />
                  </div>
                  <TextInput
                    id={`qty_${index}`}
                    type="number"
                    placeholder="1"
                    required={true}
                    disabled={formDisabled}
                    value={item.qty}
                    onChange={(e) => {
                      const qty = parseInt(e.target.value);
                      updateLineItem(
                        index,
                        "qty",
                        qty,
                        setProductOptionsBasedOnInvoice().find(
                          (option) => option.value === item.product_id
                        ).qty
                      );
                    }}
                    className="custom-input"
                  />
                </div>
                {!formDisabled &&
                  (index !== 0 &&  (
                    <div className="flex self-end  -ml-4 ">
                      <Button
                        color="#08447C"
                        outline={1}
                        disabled={formDisabled}
                        onClick={() => removeLineItem(index)}
                      >
                        <MdDelete className="mr-2 h-5 w-5 text-[#08447C]" />
                      </Button>
                    </div>
                  ))}
              </div>
            ))}
            <div className="items-center mt-5 ml-9">
                <Button
                  color="#08447C"
                  outline={1}
                  onClick={addLineItem}
                  disabled={formDisabled}
                  className="px-0"
                >
                  <MdAddCircle className="mr-2 h-5 w-5 text-[#08447C]" />
                  Add Another Item
                </Button>
              </div>
          </div>

          <div className="mt-10">
            <p className="font-medium">Reason For Return</p>
            <div className="w-52 mt-5 ml-10">
              <div className="mb-0.5 block ">
                <Label
                  htmlFor="reason"
                  value="Reason For Return"
                  className="text-xs"
                />
              </div>
              <Select
                options={reasons}
                id="reason"
                required={true}
                isDisabled={formDisabled}
                placeholder="Chose One"
                className="custom-select"
                onChange={(selectedOption) => {
                  const selected = selectedOption.value;
                  const reason = reasons.find(
                    (option) => option.value === selected
                  );
                  updateFormData("reason", selected);
                }}
                value={reasons.find(
                  (option) => option.value === formData.reason
                )}
              />
            </div>

            <div className="w-1/3 mt-5 ml-10">
              <div className="mb-2 block">
                <Label
                  htmlFor="reason_notes"
                  value={
                    formDisabled
                      ? "Other Response"
                      : "If Other, please type your response here"
                  }
                  className="text-xs"
                />
              </div>
              <Textarea
                id="reason_notes"
                placeholder="Reason for Return"
                disabled={formDisabled}
                className="border-none bg-[#F8F8F8] text-[#353535] font-light"
                rows={4}
                value={formData.reasonNotes}
                onChange={(e) => updateFormData("reasonNotes", e.target.value)}
              />
            </div>
          </div>
          <div className="mt-10">
            {formDisabled ? (
              <>
                {/* {returnStatus === 1 && (
                  <p className="text-center font-semibold p-2 text-amber-500 bg-amber-100 rounded-md w-18 ">
                    Pending
                  </p>
                )}
                {returnStatus === 2 && (
                  <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md w-18 ">
                    Approved
                  </p>
                )}
                {returnStatus === 3 && (
                  <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md w-18">
                    Denied
                  </p>
                )} */}
              </>
            ) : (
              <Button
                type="submit"
                pill={true}
                onClick={handleSubmit}
                disabled={formDisabled}
                isProcessing={processing}
                className=" w-45 theme-button px-4 py-2 hover:bg-primary-dark focus:ring-0 focus:ring-transparent "
              >
                Submit for Review
              </Button>
            )}
          </div>
          </>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Returns;
