import { useSession, signOut } from "next-auth/react";
import { useDate } from "../contexts/DateContext";
import Layout from "../components/layout";
import OrdersTable from "../components/OrdersTable";
import { useState, useEffect } from "react";
import { useRouter } from "next/router";
import axios from "axios";
import { parseISO } from "date-fns";
import RouteGuard from "../components/RouteGuard";

const Orders = () => {
  const { startDate, endDate, handleStartDateChange, handleEndDateChange } =
    useDate();
  const [orders, setOrders] = useState([]);
  const [recurringOrders, setRecurringOrders] = useState([]);
  const [fetchingOrders, setFetchingOrders] = useState(false);
  const [ordersFetched, setOrdersFetched] = useState(false);
  // const [lastFetched, setLastFetched] = useState(null);
  const { data: session, status, update } = useSession();
  const router = useRouter();

  const refreshOrders = async () => {
    setFetchingOrders(true);
    console.log("updating...");
    try {
      const res = await fetch("/api/syncJDE/syncOrders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ account_id: session.user.account_id, location_id: session.user.location_id, group_id: session.user.group_id }),
      });
      if (res.status === 200) {
        console.log("Orders Updated");
        setFetchingOrders(false);
        setOrdersFetched(true);
        update({ sync: new Date() });
      } else {
        setFetchingOrders(false);

        throw new Error(await res.text());
      }
    } catch (error) {
      setFetchingOrders(false);

      alert(error.message);
      console.error(
        "An unexpected error while updating Orders occurred:",
        error
      );
    }
  };

  useEffect(() => {
    if (session) {
      let isMounted = true;

      const fetchData = async () => {
        try {
          const response = await axios.get("/api/order/fetchRange", {
            params: {
              startDate,
              endDate,
              userId: session.user.id,
              groupId: session.user.group_id,
              locationId: session.user.location_id,
              accountId: session.user.account_id,
            },
          });

          const recurring = await axios.get("/api/order/fetchRecurring", {
            params: {
              userId: session.user.id,
              groupId: session.user.group_id,
              locationId: session.user.location_id,
              accountId: session.user.account_id,
            },
          });

          if (isMounted) {
            setOrders(response.data.orders);
            setRecurringOrders(recurring.data);
            if (startDate == 0) {
              let newStartDate = parseISO(response.data.newStartDate);
              handleStartDateChange(newStartDate);
            }
          }
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      };

      if (startDate !== null && endDate !== null) {
        fetchData();
      }
      console.log("session ran");

      if (ordersFetched) {
        setOrdersFetched(false);
      }

      return () => {
        isMounted = false;
      };
    }
  }, [startDate, endDate, session, ordersFetched]);

  // if (session) {
    return (
      <Layout>
        <RouteGuard section="My Orders">
        {/* <head> */}
        {/* <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.7.0/datepicker.min.js"></script> */}
        {/* </head> */}
        {session && 
        <div className="relative h-full overflow-x-auto overflow-y-auto bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
          <OrdersTable
            data={orders}
            recurring={recurringOrders}
            refreshOrders={refreshOrders}
            fetchingOrders={fetchingOrders}
            lastFetched={session.user.sync}
          />

          {/* {orders.length > 0 ? (
            <OrdersTable
              data={orders}
              recurring={recurringOrders}
              refreshOrders={refreshOrders}
              fetchingOrders={fetchingOrders}
              lastFetched={session.user.sync}
            />
          ) : (
            <p className="mt-4 text-sm text-center text-gray-500">
              No Available Orders
            </p>
          )} */}
        </div>
        }
        </RouteGuard>
      </Layout>
    );
  // }
};

export default Orders;
