import { Card, Navbar } from "flowbite-react";
import { useEffect } from "react";

export default function Maintenance() {
    const currentYear = new Date().getFullYear();
    useEffect(() => {
        document.body.style.display = "block";
        return () => {
          document.body.style.display = "";
        };
      }, []);
    return (
        <div className="w-screen">
        <div className="flex flex-col h-screen items-center justify-center bg-[url('/<EMAIL>')] bg-cover">
          <Card className="w-3/4 md:w-2/4 mt-20">
            <Navbar fluid={true} className="m-auto ">
              <Navbar.Brand href="/">
                <img
                  src="/oasis_logo.png"
                  className="mr-3 h-6 sm:h-9"
                  alt="Oasis Logo"
                />
              </Navbar.Brand>
            </Navbar>
            <hr className="h-px bg-gray-200 border-0 -mx-6" />
            <div style={{ textAlign: "center", padding: "50px" }}>
                <h1>🚧 Site Under Maintenance 🚧</h1>
                <p>
                MyOasis360 is temporarily undergoing server maintenance. To make a purchase order, call us at (844) 820-8940 or email Customer <NAME_EMAIL>. Thank you for your patience.
                </p>
            </div>
          </Card>
          <div className="mx-auto mb-10 pt-20 g:flex place-content-center justify-between items-center">
            <p className="text-center">
              © {currentYear} OASIS Medical Inc. All rights reserved
            </p>
          </div>
        
        </div>
      </div>
  
    );
  }
  