import * as React from "react";
import { useState } from "react";
import {
  Card,
  Label,
  TextInput,
  Checkbox,
  Button,
  Tabs,
  Modal,
  Navbar,
} from "flowbite-react";
import { useRouter } from "next/router";
import { <PERSON>aU<PERSON>, FaUnlock } from "react-icons/fa";
import { useUserAcctContext } from "../contexts/UserAcctContext";
import axios from "axios";


export default function resetPassword() {
  const { userAcct, setUserAcct } = useUserAcctContext();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [syncing, setSyncing] = useState({
    show: false,
    message: " ",
    color: "",
  });
  const router = useRouter();
  const { token } = router.query; 

  const handleSubmit = async (event) => {
    event.preventDefault();
    event.stopPropagation();
    
    // Check if passwords match
    if (password !== confirmPassword) {
      setSyncing({
        show: false,
        message: "Passwords do not match",
        color: "text-red-600",
      });
      return;
    }
     // Check password requirements
    if (!isPasswordValid(password)) {
        setSyncing({
        show: false,
        message:
            "Password does not meet requirements (8 characters, 1 uppercase, 1 lowercase, 1 number)",
        color: "text-red-600",
        });
        return;
    }

    setSyncing({
      show: true,
      message: " ",
    });

    try {
      // Make API call to reset password
      const result = await axios.post("api/user/resetPassword", {
        token: token, // Replace with the actual reset token
        password: password,
      });

      setSyncing({
        show: false,
        message: "Success: Password reset",
        color: "text-green-600",
      });
    } catch (error) {
      console.error("Error resetting password:", error);
      setSyncing({
        show: false,
        message: `${error.response.data.err || "Unexpected error"}`,
        color: "text-red-600",
      });
    }
  };

  const isPasswordValid = (password) => {
    // Password requirements: 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return passwordRegex.test(password);
  };

  return (
    <div className="w-screen bg-black">
      <div className="flex flex-col items-center justify-center bg-[url('/<EMAIL>')] bg-cover">
        <Card className="w-3/4 md:w-2/4 mt-20">
          <Navbar fluid={true} className="m-auto ">
            <Navbar.Brand href="/">
              <img
                src="/oasis_logo.png"
                className="mr-3 h-6 sm:h-9"
                alt="Oasis Logo"
              />
            </Navbar.Brand>
          </Navbar>
          <hr className="h-px bg-gray-200 border-0 -mx-6" />

          <div className="flex flex-col justify-center">
            <div className="m-auto text-center text-2xl mt-8">
              Reset Password
            </div>

            <form
              className="flex flex-col gap-4 justify-center pt-6 pb-14 w-2/4 m-auto"
              onSubmit={handleSubmit}
            >
              <span
                className={`text-sm text-center ${syncing.color}`}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "20px",
                }}
              >
                {syncing.message}
              </span>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="password" />
                </div>
                <TextInput
                  id="password"
                  type="password"
                  placeholder="New Password"
                  required={true}
                  icon={FaUnlock}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="custom-input"
                  
                />
              </div>

              <div>
                <div className="mb-2 block">
                    <Label htmlFor="confirmPassword" />
                </div>
                <TextInput
                  id="confirmPassword"
                  type="password"
                  placeholder="Confirm Password"
                  required={true}
                  icon={FaUnlock}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="custom-input"
                />
                </div>

              <Button
                type="submit"
                isProcessing={syncing.show}
                pill={true}
                className="w-2/4 mx-auto bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent mt-6"
              >
                Submit
              </Button>
            </form>
            <div className="flex text-center m-auto">
        <a
          href="/"
          className="text-primary-light hover:underline text-xs"
        >
          Back to Login page
        </a>
      </div>
          </div>
        </Card>
        <div className="mx-auto mb-20 pt-20 g:flex place-content-center justify-between items-center">
          <p className="text-center">
            © 2022 OASIS Medical Inc. All rights reserved
            <br />
            U.S. Patent Application No.: 17/026824
          </p>
        </div>
      </div>
    </div>
  );
}
