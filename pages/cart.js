import Layout from "../components/layout";
import { <PERSON><PERSON>, Badge, TextInput, Alert } from "flowbite-react";
import Link from "next/link";
import { useEffect, useState , useRef} from "react";
import axios from "axios";
import { useSession, getSession, signOut } from "next-auth/react";
import { useRouter } from "next/router";
import { useCart } from "../contexts/CartContext";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { usePromotionsContext } from '../contexts/PromotionsContext'; // Import the promotion context


export default function Checkout() {
  const formRef = useRef(null);
  const { cart, updateCart } = useCart();
  const router = useRouter();
  const { promotions } = usePromotionsContext(); // Access promotions from context


  const handleKeyDown = (e) => {
    if (e.key === "Enter" && e.target.tagName !== "TEXTAREA") {
      e.preventDefault();
    }
  };

  const [form, setForm] = useState({
    auto_delivery: 0,
    delivery_interval: 0,
    order_placed_by: "",
    po_num: "",
    surgeon_names: "",
    require_rep_consulting: 0,
  });
  // const { cartContext, updateCart } = useCart();

  const { data: session, status } = useSession();

  useEffect(() => {
    if (session) {
      axios
        .get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        )
        .then((response) => {
          const cartResponse = response.data.items;
          // setCart(cartResponse);
          updateCart(cartResponse);
        });
    }
  }, [session]);

  if (status === "loading") {
    return <h1>Loading...</h1>;
  }

  if (status === "unauthenticated") {
    console.log("unauthenticated");
  }

  const handleInputChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const returnSubtotal = () => {
    let sum = cart.reduce((acc, item) => acc + Number(item.price) * Number(item.qty), 0);
    const { totalDiscount, finalSubtotal } = applyPromotions(sum);
    // let org_sum = sum;
    // let discount = 0;
    // let discount_total = sum;
    // if (form.auto_delivery == 1) {
    //   // sum = sum * 0.9;
    //   discount = org_sum * 0.1;
    //   discount_total = org_sum * 0.9;
    // }

    return { sum, totalDiscount, finalSubtotal };
  };

  const applyPromotions = (subtotal) => {
    let totalDiscount = 0;

    promotions.forEach(promotion => {
      if (promotion.promotion_type === "order_based") {
        // Check all conditions for the promotion
        const { Conditions } = promotion;
        const conditionsMet = Conditions.every(condition => {
          switch (condition.type) {
            case 'product_quantity':
              // Check if at least one product in the cart meets the quantity threshold
              return cart.some(item => {
                const isApplicableProduct = applicableProducts.length === 0 || 
                  applicableProducts.some(ap => ap.product_id === item.product_id);
                return isApplicableProduct && item.qty >= condition.threshold;
              });
            case 'subtotal':
              return subtotal >= parseFloat(condition.value);
            case 'shipping':
              // Assume shipping value is available from some source
              return false; // Placeholder logic
            // case 'coupon':
            //   return form.coupon_code === condition.coupon_code;
            case 'auto_delivery':
              return form.auto_delivery === 1;
            default:
              return true;
          }
        });

        if (conditionsMet) {
          // Apply the discount
          if (promotion.discount_type === "fixed_discount") {
            totalDiscount += promotion.discount_value;
          } else if (promotion.discount_type === "percentage") {
            totalDiscount += (subtotal * (promotion.discount_value / 100));
          }
        }
      }

      //TODO BUNDLE type wif necessary
    });

    return { totalDiscount, finalSubtotal: subtotal - totalDiscount };
  };

  const handleAutoDeliveryChange = () => {
    const { delivery_interval } = form;
  };

  const submit = (e) => {
    console.log("Checking out");
    e.preventDefault();
    router.push({
      pathname: "/checkout",
      query: {
        auto_delivery: form.auto_delivery,
        delivery_interval: form.delivery_interval,
        order_placed_by: form.order_placed_by,
        po_num: form.po_num,
        require_rep_consulting: form.require_rep_consulting,
        surgeon_names: form.surgeon_names,
      },
    });
  };

  // console.log(form);
  // console.log("cart context", cartContext);

  return (
    <>
      <Layout>
        <form onSubmit={submit} ref={formRef} onKeyDown={handleKeyDown}>
          <div className="flex flex-row">
            <div className="basis-2/3">
              <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
                <div className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                  <h1 className="text-[#353535] text-[20px]">Auto Delivery</h1>
                </div>
                <div className="py-[25px] px-[30px]">
                  <Alert color="success" className="">
                    Sign Up for Auto Delivery and get <b>free</b> ground shipping! Annual Fee Waived <s>$100</s>
                  </Alert>
                </div>
                <div className="flex flex-row gap-4 justify-center">
                  <div>
                    <Button
                      color={"light"}
                      className={`w-[156.25px] h-11 ${
                        form.delivery_interval === 0
                          ? "bg-primary text-white hover:text-black"
                          : ""
                      }`}
                      onClick={() => {
                        setForm({
                          ...form,
                          delivery_interval: 0,
                          auto_delivery: 0,
                        });
                        handleAutoDeliveryChange();
                      }}
                    >
                      None
                    </Button>
                  </div>
                  <div>
                    <Button
                      color={"light"}
                      className={`w-[156.25px] h-11 ${
                        form.delivery_interval === 1
                          ? "bg-primary text-white hover:text-black"
                          : ""
                      }`}
                      onClick={() => {
                        setForm({
                          ...form,
                          delivery_interval: 1,
                          auto_delivery: 1,
                        });
                      }}
                    >
                      Every Month
                    </Button>
                  </div>
                  <div>
                    <Button
                      color={"light"}
                      className={`w-[156.25px] h-11 ${
                        form.delivery_interval === 2
                          ? "bg-primary text-white hover:text-black"
                          : ""
                      }`}
                      onClick={() => {
                        setForm({
                          ...form,
                          delivery_interval: 2,
                          auto_delivery: 1,
                        });
                      }}
                    >
                      Every 2 Months
                    </Button>
                  </div>
                  <div>
                    <Button
                      color={"light"}
                      className={`w-[156.25px] h-11 ${
                        form.delivery_interval === 3
                          ? "bg-primary text-white hover:text-black"
                          : ""
                      }`}
                      onClick={() => {
                        setForm({
                          ...form,
                          delivery_interval: 3,
                          auto_delivery: 1,
                        });
                      }}
                    >
                      Every 3 Months
                    </Button>
                  </div>
                </div>
                <div className="flex justify-between pt-[40px] pl-[30px]">
                  {/* <div>
                  <p className="text-[#929791]">Next Scheduled Delivery</p>
                  <p>July 11th 2023</p>
                </div> */}
                  <div className="pr-[33px] pt-4">
                    {/* <button className="text-[#4BB5EA]">Stop Auto Delivery</button> */}
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
                <div className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                  <h1 className="text-[#353535] text-[20px]">
                    Order Form Details
                  </h1>
                </div>
                <div className="grid grid-cols-2 gap-4 px-[30px] py-[30px]">
                  <div>
                    <input
                      className="h-[46px] w-full max-w-[327.5px] bg-[#F8F8F8] rounded-md"
                      placeholder="* Order Placed By"
                      name="order_placed_by"
                      required
                      value={form.order_placed_by}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div>
                    <input
                      className="h-[46px] w-full max-w-[327.5px] bg-[#F8F8F8] rounded-md"
                      placeholder="P.O. #"
                      name="po_num"
                      value={form.po_num}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className="pt-5">
                    <p>Which Surgeons will be using these?</p>
                    <input
                      className="h-[46px] w-full max-w-[327.5px] bg-[#F8F8F8] rounded-md"
                      placeholder="Surgeon name"
                      name="surgeon_names"
                      value={form.surgeon_names}
                      onChange={handleInputChange}
                    />
                  </div>
                </div>
                <div className="pl-[30px] pb-[25px]">
                  <p>*Required to continue to Checkout</p>
                </div>
              </div>

              <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
                <div className="py-[14px] pl-[30px] border-b-2 border-[#EFF1F0]">
                  <h1 className="text-[#353535] text-[20px]">Shopping Cart</h1>
                </div>
                <div className="px-[30px] py-[20px] text-[#929791] font-[14px]">
                  <p>Your Shopping Cart Items</p>
                </div>
                <hr className="h-px bg-gray-200 border-0 -mx-6" />

                {cart
                  ? cart.map((value, index) => (
                    <Cart
                      key={value.id}
                      index = {index}
                      data={value}
                      cart={cart} // Pass cart state
                      updateCart={updateCart} // Pass updateCart function
                    />
                    ))
                  : "...loading"}

                <div className="flex flex-row pl-[34px] py-[23px]">
                  <input
                    type="checkbox"
                    className="rounded-sm"
                    name="require_rep_consulting"
                    checked={form.require_rep_consulting}
                    onChange={(e) => {
                      setForm({
                        ...form,
                        require_rep_consulting: Number(e.target.checked),
                      });
                    }}
                  />
                  <p className="text-[#929791] text-[12px] pl-2">
                    Click here for an Oasis representative to contact you for a
                    training session or additional product information.
                  </p>
                </div>
              </div>
            </div>

            <div className="basis-1/3">
              <div className="bg-white rounded-xl dark:bg-gray-800 ml-5 mb-5 sticky top-0">
                <div className="flex justify-between border-b-2 border-[#EFF1F0]">
                  <div className="py-[14px] pl-[30px]">
                    <h1 className="text-[#353535] text-[20px]">Subtotal</h1>
                  </div>
                  <div className="pr-[40px] pt-[16px]">
                    <p>{formatPrice.format(returnSubtotal().finalSubtotal)}</p>
                    <p className="text-[8px] text-[#929791]">U.S. Dollar</p>
                  </div>
                </div>

                <div className="flex justify-between">
                  <div className="py-[14px] pl-[30px] text-[12px]">
                    <h1 className="text-black">Shippable Product</h1>
                  </div>
                  <div className="pr-[40px] pt-[16px]">
                    <p>{formatPrice.format(returnSubtotal().sum)}</p>
                  </div>
                </div>

                <div className="flex justify-between">
                  <div className="py-[14px] pl-[30px] text-[12px]">
                    <h1 className="text-black">Backordered Product</h1>
                  </div>
                  <div className="pr-[40px] pt-[16px]">
                    <p> {formatPrice.format(0)}</p>
                  </div>
                </div>

                <div className="flex justify-between ">
                  <div className="py-[14px] pl-[30px] text-[12px]">
                    <h1 className="text-black">Promotion Discount</h1>
                  </div>
                  <div className="pr-[40px] pt-[16px]">
                    <p>-{formatPrice.format(returnSubtotal().totalDiscount)}</p>
                  </div>
                </div>

                <div className="flex justify-between">
                  <div className="py-[14px] pl-[30px] text-[12px]">
                    <h1 className="text-black">Subtotal of Products</h1>
                  </div>
                  <div className="pr-[40px] pt-[16px]">
                    <p>{formatPrice.format(returnSubtotal().finalSubtotal)}</p>
                  </div>
                </div>
                {/* for this one and Tax, come back and have it use the default location to calculate these values */}
                {/* <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Total Freight</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>$00.00</p>
                </div>
              </div>

              <div className="flex justify-between">
                <div className="py-[14px] pl-[30px] text-[12px]">
                  <h1 className="text-black">Total Tax</h1>
                </div>
                <div className="pr-[40px] pt-[16px]">
                  <p>$00.00</p>
                </div>
              </div> */}

                <div className="grid justify-items-center pb-[30px] pt-[37px]">
                  <div>
                    {/* <Link
                    disabled
                    href={{
                      pathname: "/checkout",
                      query: form,
                    }}
                    className="bg-primary text-white py-4 px-10 rounded-full hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
                  >
                    Checkout
                  </Link> */}
                    <Button
                      type="submit"
                      className="bg-primary text-white py-2 px-8 rounded-full hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
                    >
                      Checkout
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </Layout>
    </>
  );
}

const formatPrice = new Intl.NumberFormat("en-US", {
  style: "currency",
  currency: "USD",
});

function Cart({ data, cart, updateCart,index }) {
  // const { cart, updateCart } = useCart();
  const { data: session, status } = useSession();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const { promotions } = usePromotionsContext(); // Access promotions from context


  const [qty, setQty] = useState(cart[index].qty);
  console.log("cart id",data.id);

  const router = useRouter();

  function truncateProductName(str) {
    if (str.length > 50) {
      str = str.slice(0, 50).trim();
      str += "...";
    }

    return str;
  }

  const removeById = () => {
    axios({
      method: "delete",
      url: "/api/cart/removeItemById",
      data: {
        id: data.id,
      },
    }).then((response) => {
      if (response.status == 200) {
        // console.log("response", response.data);
        onUpdate("delete");
        // console.log("THIS IS DELETING");
        successUpdateNoti(
          `${truncateProductName(data.Product.name)} removed from cart.`
        );
  
      }
    });
  };

  const updateCartQty = (e) => {
    e.preventDefault();
    console.log("qty prev", e.target.placeholder);
    console.log("qty", e.target.value);
    console.log("Index:", data.id);
    const index = data.id;
    const enteredQty = e.target.value;
    setQty(enteredQty);
    if (enteredQty < 0) {
      setQty(e.target.placeholder);
      setCartAlert({
        color: "failure",
        show: true,
        message: `Quantity cannot be less than 0`,
      });
      // setTimeout(() => {
      //   setCartAlert({
      //     message: "",
      //     color: "",
      //     show: false,
      //   });
      // }, 2000);
    } else if (enteredQty == 0) {
      axios({
        method: "delete",
        url: "/api/cart/removeItemById",
        data: {
          id: data.id,
        },
      }).then((response) => {
        if (response.status == 200) {
          // console.log("response", response.data);
          onUpdate("delete");
          // console.log("THIS IS DELETING");
          successUpdateNoti(
            `${truncateProductName(data.Product.name)} removed from cart.`
          );
          // setTimeout(() => {
          //   setCartAlert({
          //     message: "",
          //     color: "",
          //     show: false,
          //   });
          // }, 2000);
        }
      });
    } else if (enteredQty > 0) {
      axios({
        method: "POST",
        url: "/api/cart/updateQty",
        data: {
          id: data.id,
          qty: qty,
          account_jde: session.user.Account.jde_id,
          promotions:promotions
        },
      }).then((response) => {
        if (response.status == 200) {
          console.log("response", response.data);
          onUpdate("update");
          console.log("THIS IS UPDATING");
          successUpdateNoti(
            `${truncateProductName(data.Product.name)} quantity updated.`
          );
          // setTimeout(() => {
          //   setCartAlert({
          //     message: "",
          //     color: "",
          //     show: false,
          //   });
          // }, 2000);

          console.log("Getting Cart?", cart);
        }
      });
    }
  };

  const onUpdate = (e) => {
    // console.log("updateCartQty Running...");
    if (session) {
      axios
        .get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        )
        .then((response) => {
          const updatedCart = response.data.items;
          updateCart(updatedCart);
        });
    }
  };

  const successUpdateNoti = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: message,
      show: true,
    };

    // console.log("New Notif", newNotification);

    addNotifications(newNotification);
  };

  const failureUpdateNoti = (message) => {
    console.log("New Fail Notif");
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };
  useEffect(() => {
    setQty(cart[index].qty);
  }, [cart]);

  return (
    <div className="px-5">
      <a
        href="#"
        className="flex items-center gap-4 py-3 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:border-gray-600"
      >
      <div className="flex-shrink-0">
        <img
          className="w-11 h-11 sqaure"
          src={
            data.Product.ProductImage && data.Product.ProductImage.length < 1
              ? "/no_image.jpeg"
              : `${
            process.env.NEXT_PUBLIC_STAGING_BASE_URL + "oasis-product-images/"
          }${data.Product.ProductImage[0].path}_l.${
            data.Product.ProductImage[0].extension
          }`}
          alt=""
        />
      </div>
      <div className="flex-grow">
        <div className="text-black font-normal text-sm mb-1.5">
          {data.Product.name}
        </div>
        <div className="inline-flex items-center ">
          <div className="text-xs font-medium text-gray-400 pr-4">
            {data.Product.sku}
          </div>
          {data.Product && data.Product.stock > 0 ? (
            <Badge color="success" size="xs">
              In Stock
            </Badge>
          ) : (
            <Badge color="failure" size="xs">
              Out of Stock
            </Badge>
          )}
        </div>
      </div>
      <div className="flex-shrink-0">
      <div className="flex items-center gap-5">
        <div className="flex-none">
          <TextInput
            className="inline-block align-top bg-[#F8F8F8] w-[75px] text-center rounded-md mt-2"
            type="number"
            placeholder={qty}
            value={qty}
            onChange={(e) => setQty(e.target.value)}
            onBlur={(e) => updateCartQty(e)}
          />
        </div>
        {/* <div className="flex-none bg-[#F8F8F8] h-[46px] w-[75px] text-center rounded-md mt-1">
          <span className="inline-block align-top mt-2">{data.qty}</span>
        </div> */}
        <div className="flex-1 w-20 text-right">
          {formatPrice.format(data.price)}
        </div>
        <div className="">
          <button onClick={removeById}>
            <img src="/trashcan.png"></img>
          </button>
        </div>
      </div>
      </div>
      </a>
      <hr className="h-px bg-gray-200 border-0 -mx-6" />
    </div>
  );
}
