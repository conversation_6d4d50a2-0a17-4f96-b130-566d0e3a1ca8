import * as React from "react";
import { useState } from "react";
import {
  Card,
  Label,
  TextInput,
  Checkbox,
  Button,
  Tabs,
  Modal,
  Navbar,
} from "flowbite-react";
import { useRouter } from "next/router";
import { <PERSON>aU<PERSON>, FaUnlock } from "react-icons/fa";
import { useUserAcctContext } from "../contexts/UserAcctContext";
import axios from "axios";


export default function ForgotPassword() {
  const { userAcct, setUserAcct } = useUserAcctContext();
  const [email, setEmail] = useState("");
  const [syncing, setSyncing] = useState({
    show: false,
    message: " ",
    color: "",
  });
  const router = useRouter();

  const handleSubmit = async (event) => {
    event.preventDefault();
    event.stopPropagation();
    setSyncing({
      show: true,
      message: " ",
    });
    // Temp swithc redirect to settings
    // console.log(email);
    try {
      const result = await fetch("/api/user/forgotPassword", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (result.ok) {
        setSyncing({
          show: false,
          message: `Success: Password reset sent to ${email}`,
          color: "text-green-600",
        });
      } else {
        const errorData = await result.text();
        console.log(result.status)
        if (result.status === 404) {
          setSyncing({
            show: false,
            message: `Error: Email ${email} did not match any user accounts. Please try again.`,
            color: "text-red-600",
          });
        } else {
          setSyncing({
            show: false,
            message: `Error: ${errorData} `,
            color: "text-red-600",
          });
        }
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while sending reset password email",
        error
      );
    }
  };

  return (
    <div className="w-screen bg-black">
      <div className="flex flex-col items-center justify-center bg-[url('/<EMAIL>')] bg-cover">
        <Card className="w-3/4 md:w-2/4 mt-20">
          <Navbar fluid={true} className="m-auto ">
            <Navbar.Brand href="/">
              <img
                src="/oasis_logo.png"
                className="mr-3 h-6 sm:h-9"
                alt="Oasis Logo"
              />
            </Navbar.Brand>
          </Navbar>
          <hr className="h-px bg-gray-200 border-0 -mx-6" />

          <div className="flex flex-col justify-center">
            <div className="m-auto text-center text-2xl mt-8">
              Forgot Password?
            </div>

            <form
              className="flex flex-col gap-4 justify-center pt-6 pb-14 w-2/4 m-auto"
              onSubmit={handleSubmit}
            >
              <span
                className={`text-sm text-center ${syncing.color}`}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  height: "20px",
                }}
              >
                {syncing.message}
              </span>

              <div>
                <div className="mb-2 block">
                  <Label htmlFor="email1" />
                </div>
                <TextInput
                  id="email1"
                  type="email"
                  placeholder="Email"
                  required={true}
                  icon={FaUser}
                  value={email}
                  helperText={
                    <span className="m-auto text-center text-sm">
                      Enter the Email address associated with your account
                    </span>
                  }
                  onChange={(e) => setEmail(e.target.value)}
                  className="custom-input"
                />
              </div>

              <Button
                type="submit"
                isProcessing={syncing.show}
                pill={true}
                className="w-2/4 mx-auto bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent mt-6"
              >
                Submit
              </Button>
            </form>
            <div className="flex text-center m-auto">
        <a
          href="/"
          className="text-primary-light hover:underline text-xs"
        >
          Back to Login page
        </a>
      </div>
          </div>
        </Card>
        <div className="mx-auto mb-20 pt-20 g:flex place-content-center justify-between items-center">
          <p className="text-center">
            © 2022 OASIS Medical Inc. All rights reserved
            <br />
            U.S. Patent Application No.: 17/026824
          </p>
        </div>
      </div>
    </div>
  );
}
