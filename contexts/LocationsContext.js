// This context is to have locations across the app set so NavBar, Locations page, Billing, Checkout, Users, etc, dont have to have their own indvidual states with locations

import { createContext, useContext, useState } from "react";

const FORM_STATE = {};

const LocationsContext = createContext({
  locations: FORM_STATE,
  setLocations: (locations) => {},
});

export function UseLocationsProvider({ children }) {
  const [locations, setLocations] = useState(FORM_STATE);

  return (
    <LocationsContext.Provider value={{ locations, setLocations }}>
      {children}
    </LocationsContext.Provider>
  );
}

export function useLocationsContext() {
  const { locations, setLocations } = useContext(LocationsContext);

  return { locations, setLocations };
}
