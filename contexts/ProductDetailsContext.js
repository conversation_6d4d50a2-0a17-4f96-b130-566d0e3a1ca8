import { createContext, useContext, useState } from "react";

const FORM_STATE = {
  allProducts: [],
  selectedProduct: [],
  filters: [],
  categories: [],
  parent: "",
  firstCat: "",
  secondCat: "",
};

const ProductDetailsContext = createContext({
  productDetails: FORM_STATE,
  setProductDetails: (productDetails) => {},
});

export function UseProductDetailsProvider({ children }) {
  const [productDetails, setProductDetails] = useState(FORM_STATE);

  return (
    <ProductDetailsContext.Provider
      value={{ productDetails, setProductDetails }}
    >
      {children}
    </ProductDetailsContext.Provider>
  );
}

export function useProductDetailsContext() {
  const { productDetails, setProductDetails } = useContext(
    ProductDetailsContext
  );

  return { productDetails, setProductDetails };
}
