import { createContext, useContext, useState, useEffect } from "react";

// const INITIAL_STATE = {
//   notifications: [
//     {
//       iconColor: {
//         bg: "bg-green-100",
//         text: "text-green-500",
//       },
//       bgColor: "success",
//       message: "Test Message",
//       show: true,
//     },
//     {
//       iconColor: {
//         bg: "bg-red-100",
//         text: "text-red-500",
//       },
//       bgColor: "fail",
//       message: "Test Message 2",
//       show: true,
//     },
//   ],
// };
const INITIAL_STATE = {
  notifications: [],
};

const GlobalAlertContext = createContext({
  notifications: INITIAL_STATE.notifications,
  addNotifications: (notification) => {},
});

export function UseGlobalAlertProvider({ children }) {
  const [notifications, setNotifications] = useState(
    INITIAL_STATE.notifications
  );

  const addNotifications = (notification) => {
    setNotifications((prevNotifications) => [
      ...prevNotifications,
      notification,
    ]);
  };

  const removeNotification = (index) => {
    setNotifications((prevNotifications) =>
      prevNotifications.filter((_, i) => i !== index)
    );
  };

  useEffect(() => {
    const clearNotificationAfterDelay = (index) => {
      setTimeout(() => {
        removeNotification(index);
      }, 5000);
    };

    if (notifications.length > 0) {
      const lastIndex = notifications.length - 1;
      clearNotificationAfterDelay(lastIndex);
    }
  }, [notifications]);

  return (
    <GlobalAlertContext.Provider value={{ notifications, addNotifications }}>
      {children}
    </GlobalAlertContext.Provider>
  );
}

export function useGlobalAlertContext() {
  const { notifications, addNotifications } = useContext(GlobalAlertContext);

  return { notifications, addNotifications };
}
