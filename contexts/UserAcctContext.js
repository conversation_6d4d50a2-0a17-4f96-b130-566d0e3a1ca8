import { createContext, useContext, useState } from "react";

const FORM_STATE = {
    user: {},
    account: {},
    location: {},
};

const UserAcctContext = createContext({ userAcct: FORM_STATE, setUserAcct:(userAcct) => {}});

export function UseUserAcctProvider({ children }) {
  const [userAcct, setUserAcct] = useState(FORM_STATE);

  return (
    <UserAcctContext.Provider value={{ userAcct, setUserAcct }}>
      {children}
    </UserAcctContext.Provider>
  );
}

export function useUserAcctContext() {
  const { userAcct, setUserAcct } = useContext(UserAcctContext);

  return {
    userAcct,
    setUserAcct,
  };
}
