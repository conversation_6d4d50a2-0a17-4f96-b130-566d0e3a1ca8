import { createContext, useContext, useState } from "react";

const FORM_STATE = {
    user: {},
    account: {},
    contacts: {},
    source: 0, //0 = register, 1 = restore
    approved: 0, //0 = account needs to be confirmed by <PERSON><PERSON>, 1 = coming from restore, good to login after account creation
};

const StepperContext = createContext({ userData: FORM_STATE, setUserData:(userData) => {}});

export function UseContextProvider({ children }) {
  const [userData, setUserData] = useState(FORM_STATE);

  return (
    <StepperContext.Provider value={{ userData, setUserData }}>
      {children}
    </StepperContext.Provider>
  );
}

export function useStepperContext() {
  const { userData, setUserData } = useContext(StepperContext);

  return (
    { userData, setUserData }
    );
}