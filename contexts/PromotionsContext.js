// PromotionsContext.js
import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';

export const PromotionsContext = createContext();
export const usePromotionsContext = () => useContext(PromotionsContext);

export const PromotionsProvider = ({ children }) => {
  const [promotions, setPromotions] = useState([]);

  useEffect(() => {
    // Fetch active promotions when the context is first used
    const fetchPromotions = async () => {
      try {
        const response = await axios.get('/api/promotions/get');
        setPromotions(response.data);
      } catch (error) {
        console.error('Error fetching promotions:', error);
      }
    };

    fetchPromotions();
  }, []);

  return (
    <PromotionsContext.Provider value={{ promotions }}>
      {children}
    </PromotionsContext.Provider>
  );
};
