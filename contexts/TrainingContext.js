import { createContext, useContext, useState } from "react";

const FORM_STATE = {
    title: "",
    video_path: "",
};

const TrainingContext = createContext({ training: FORM_STATE, setTraining:(training) => {}});

export function UseTrainingProvider({ children }) {
  const [training, setTraining] = useState(FORM_STATE);

  return (
    <TrainingContext.Provider value={{ training, setTraining }}>
      {children}
    </TrainingContext.Provider>
  );
}

export function useTrainingContext() {
  const { training, setTraining } = useContext(TrainingContext);

  return (
    { training,
    setTraining}
  );
}
