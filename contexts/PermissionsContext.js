import { createContext, useContext, useEffect, useState } from "react";
import axios from "axios";
import { useSession } from "next-auth/react";

const PermissionsContext = createContext(null);

export const PermissionsProvider = ({ children }) => {
  const { data: session, status } = useSession();
  const [permissions, setPermissions] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status !== "authenticated" || !session?.user?.group_id) return;
    console.log("Session status:", status, "Session data:", session);

    axios
      .get(`/api/permissions/getByGroup?groupId=${session.user.group_id}`)
      .then((res) => {
        console.log("Permissions received:", res.data); // 👈
        setPermissions(res.data);
      })
      .finally(() => setLoading(false));
  }, [status, session]);

  return (
    <PermissionsContext.Provider value={{ permissions, loading }}>
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = () => useContext(PermissionsContext);
