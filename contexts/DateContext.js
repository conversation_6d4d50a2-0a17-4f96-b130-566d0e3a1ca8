// DateContext.js
import React, { createContext, useContext, useState } from "react";
import { startOfWeek, addDays, endOfDay,startOfDay } from "date-fns"; // Import date-fns functions
import { fromZonedTime, toZonedTime } from "date-fns-tz";


const DateContext = createContext();

export function DateProvider({ children }) {
  // Calculate the current week's start date and end date
  const [timeZone, setTimeZone] = useState(Intl.DateTimeFormat().resolvedOptions().timeZone); // Get client's timezone

  const today = new Date();
  const startOfWeekDate = startOfWeek(today, { weekStartsOn: 1 });
  const endOfWeekDate = endOfDay(addDays(startOfWeekDate, 6)); // 6 days to get to the end of the week
  const [startDate, setStartDate] = useState(0);
  const [endDate, setEndDate] = useState(fromZonedTime(endOfWeekDate, timeZone));

  const handleStartDateChange = (date) => {
    setStartDate(fromZonedTime(startOfDay(date), timeZone));
  };

  const handleEndDateChange = (date) => {
    setEndDate(fromZonedTime(endOfDay(date), timeZone));
  };

  return (
    <DateContext.Provider value={{ startDate, endDate, handleStartDateChange, handleEndDateChange }}>
      {children}
    </DateContext.Provider>
  );
}

export function useDate() {
  return useContext(DateContext);
}