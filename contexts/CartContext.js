// Create a file named CartContext.js
import axios from "axios";
import React, { createContext, useContext, useState, useEffect } from "react";

const CartContext = createContext({
  products: [],
  details: [],
});

export const CartProvider = ({ children }) => {
  const [cartContext, setCartContext] = useState([]);

  useEffect(() => {
    // Fetch the cart data from your API and update the cart state here
    // You can use axios or any other method to fetch the cart data
    // Update the cart state with the fetched data
    if (cartContext.products > 0) {
      console.log("This is comming from cart context");
      console.log(cartContext.length);
      axios
        .post(`/api/products/getParentType`, { cart: cartContext })
        .then((response) => {
          setCartContext({
            ...cartContext,
            details: {
              onlyVision: response.data.onlyVision,
              cartQty: response.data.qty,
              cartWeight: response.data.weight,
            },
          });
        });
    }
  }, [cartContext]);

  const updateCart = (newCart) => {
    // Update the cart state with the newCart
    setCartContext(newCart);
  };

  const addToCartContext = (newCart) => {
    // Update the cart state with the newCart
    setCartContext({
      ...cartContext,
      newCart,
    });
  };

  return (
    <CartContext.Provider value={{ cart: cartContext, addToCartContext, updateCart }}>
      {children}
    </CartContext.Provider>
  );
};

export const useCart = () => {
  return useContext(CartContext);
};
