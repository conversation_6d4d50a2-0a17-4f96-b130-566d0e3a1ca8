import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { useSession, signOut } from "next-auth/react";
import { Sidebar, Navbar, TextInput, Checkbox, Button } from "flowbite-react";
import {
  HiShoppingBag,
  HiShoppingCart,
  HiCog,
  HiOfficeBuilding,
  HiUsers,
  HiCurrencyDollar,
} from "react-icons/hi";
import { HiBuildingOffice2 } from "react-icons/hi2";
import { FaStore, FaFileInvoiceDollar, FaUserFriends } from "react-icons/fa";
import {
  MdKeyboardReturn,
  MdDashboard,
  MdOutlineStorefront,
  MdOutlineLogin,
  MdVideoLibrary,
  MdSupervisorAccount,
  MdOutlineSettings,
  MdMoney,
  MdLocalGroceryStore,
} from "react-icons/md";
import axios from "axios";
import useSWR from "swr";
import { useUserAcctContext } from "../contexts/UserAcctContext";
import { data } from "autoprefixer";
import { usePermissions } from "../contexts/PermissionsContext";
import { createPermissionChecker } from "./utils";



const fetcher = (url) => axios.get(url).then((res) => res.data);

function CategoriesMenuItem({ data }) {
  const router = useRouter(); // Get the current router

  const isSubItemActive = (expectedQueryValue) => {
    const currentQueryValue = router.query.category; // Replace with your actual query parameter name

    // Compare the current query value with the expected value
    return currentQueryValue === expectedQueryValue;
  };
  return (
    <>
      <Sidebar.Item href={"/products?category=" + data.name + "&parentid="+ data.id} className={`${isSubItemActive(`${data.name}`) ? "sidebar-subitem-active" : ""}`}>
        {data.name}
      </Sidebar.Item>
    </>
  );
}

function SidebarItemWithPermission({ section, icon, href, isItemActive, children }) {
  const { userAcct } = useUserAcctContext();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, userAcct?.user.group_id);

  if (!hasPermission(section, "view")) {
    return null;
  }

  return (
    <Sidebar.Item href={href} icon={icon} className={isItemActive(href) ? "sidebar-item-active" : ""}>
      {children}
    </Sidebar.Item>
  );
}


export default function SidebarMenu() {
  const { data: session, status } = useSession();
  const { userAcct, setUserAcct } = useUserAcctContext();
  const [categories, setCategories] = useState([]);
  const [gotCategories, setGotCategories] = useState(0);
  const { permissions, loading } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, userAcct?.user.group_id);

  // if (!permissions || loading) return null;

  const router = useRouter(); // Get the current router

  // Function to determine if a sidebar item is active based on the current route
  const isItemActive = (href) => router.pathname.startsWith(href);
  const isSubItemActive = (expectedQueryValue) => {
    const currentQueryValue = router.query.category; // Replace with your actual query parameter name

    // Compare the current query value with the expected value
    return currentQueryValue === expectedQueryValue;
  };

  // Function to add a class to the sidebar item if it's active
  const getItemClassName = (href) =>
    isItemActive(href) ? "sidebar-item-active" : "";

  const getTopCategories = () => {
    axios.get(`/api/categories/topLevel`).then((response) => {
      setCategories(response.data);
      setGotCategories(1);
    });
  };

  // const { data, isLoading } = useSWR("/api/categories/topLevel", fetcher);

  function logoutHandler() {
    signOut({ callbackUrl: "/" });
  }

  const formatPhoneNumber = (phoneNumber) => {
    const cleanedInput = phoneNumber.replace(/\D/g, "");
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }
    return formattedPhone;
  };

  useEffect(() => {
    getTopCategories();
  },[session] );

  if (session && userAcct && gotCategories && permissions) {

  return (
    <Sidebar
      aria-label="Sidebar with multi-level dropdown example"
      className="w-72 h-full"
    >
      <Sidebar.Items className="w-10/12 mx-auto">
        <Sidebar.ItemGroup>
          <img
            src="/oasis_logo.png"
            className="mx-auto mt-6 mb-3 h-6 sm:h-8"
            alt="Oasis Logo"
          />
        </Sidebar.ItemGroup>
        <Sidebar.ItemGroup className="space-y-3">
        <Sidebar.Item href="/dashboard" icon={MdDashboard} className={getItemClassName("/dashboard")}>
        Dashboard
        </Sidebar.Item>

        {(userAcct?.user.group_id === 1 || hasPermission("Products", "view")) && (

          <Sidebar.Collapse icon={MdOutlineStorefront} label="Products" className={getItemClassName("/product")}>
            {(categories) && categories.map((item) => (
              <CategoriesMenuItem data={item} key={item.id}/>
            ))}
            {/* <Sidebar.Item href={"/products?category=Surgical"} className={`${isSubItemActive("Surgical") ? "sidebar-subitem-active" : ""}`}>
              Surgical
            </Sidebar.Item>
            <Sidebar.Item href={"/products?category=Vision"} className={`${isSubItemActive("Vision") ? "sidebar-subitem-active" : ""}`}>
              Vision
            </Sidebar.Item> */}
          </Sidebar.Collapse>
          )}

          <SidebarItemWithPermission section="My Orders" icon={MdLocalGroceryStore} href="/orders" isItemActive={isItemActive}>
            My Orders
          </SidebarItemWithPermission>
          <SidebarItemWithPermission href="/invoices" icon={MdMoney} section="My Invoices" isItemActive={isItemActive}>
            My Invoices
          </SidebarItemWithPermission>
          <SidebarItemWithPermission href="/billing" icon={HiCurrencyDollar} section="Billing" isItemActive={isItemActive}>
            Billing
          </SidebarItemWithPermission>
          <SidebarItemWithPermission href="/locations" icon={HiBuildingOffice2} section="Locations" isItemActive={isItemActive}>
            Locations
          </SidebarItemWithPermission>
          <SidebarItemWithPermission href="/users" icon={MdSupervisorAccount} section="Users" isItemActive={isItemActive}>
            Users
          </SidebarItemWithPermission>
          <SidebarItemWithPermission href="/training" icon={MdVideoLibrary} section="Resources" isItemActive={isItemActive}>
            Resources
          </SidebarItemWithPermission>
          <SidebarItemWithPermission href="/returns" icon={MdKeyboardReturn} section="Returns" isItemActive={isItemActive}>
            Returns
          </SidebarItemWithPermission>
          <Sidebar.Item href="/settings" icon={MdOutlineSettings} className={getItemClassName("/settings")}>
            Settings
          </Sidebar.Item>
          <div className="flex flex-col">
            {/* <Button
              color="light"
              pill={true}
              className="mx-auto my-4 text-primary w-full font-light"
            >
              <FaUserFriends className="mr-2 h-5 w-5" />
              Referrals
            </Button> */}
            <p className="text-primary mt-4 text-lg font-semibold">
              {userAcct && userAcct.account.oasis_rep_id
                ? "Your Oasis Resource"
                : ""}
            </p>
            <p className="text-gray-400 text-base font-light">
              {userAcct && userAcct.account.oasis_rep_id
                ? userAcct.account.oasis_rep_name
                : ""}
            </p>
            <a
              href={`tel:${
                userAcct && userAcct.account.oasis_rep_id
                  ? userAcct.account.oasis_rep_phone
                  : ""
              }`}
              className="text-primary-light text-base font-light"
            >
              { userAcct.account && userAcct.account.oasis_rep_phone
                ? formatPhoneNumber(userAcct.account.oasis_rep_phone)
                : ""}

            </a>
            <a
              href={`mailto:${
                userAcct && userAcct.account.oasis_rep_id
                  ? userAcct.account.oasis_rep_email
                  : ""
              }`}
              className="text-primary-light text-base font-light"
            >
              {userAcct && userAcct.account.oasis_rep_id
                ? userAcct && userAcct.account.oasis_rep_email
                : ""}
            </a>
          </div>
        </Sidebar.ItemGroup>
        <Sidebar.ItemGroup className="">
          {session && (
            <Sidebar.Item
              icon={MdOutlineLogin}
              onClick={logoutHandler}
              className=""
            >
              Log Out
            </Sidebar.Item>
          )}
        </Sidebar.ItemGroup>
      </Sidebar.Items>
    </Sidebar>
  );
  }
}
