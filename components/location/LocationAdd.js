import { Label, TextInput, Modal, <PERSON><PERSON>, Spinner } from "flowbite-react";
import { useState, useEffect } from "react";
import Select from "react-select";
import axios from "axios";

function AddLocation({
  partnerType,
  stateNames,
  locations,
  setModal,
  onUpdate,
  session,
  alert,
  setAlert,
}) {
  const [formData, setFormData] = useState({
    practice: "",
    website: "",
    partner_type: "",
    address: "",
    address_2: "",
    city: "",
    zip: "",
    state: "",
    country: "US",
    phone: "",
    type: "",
    default: 0,
    account_id: session["user"]["account_id"],
  });
  const [syncing, setSyncing] = useState(false);

  function handleChange(e) {
    const value = e.target.value;
    setFormData({
      ...formData,
      [e.target.name]: value,
    });
  }

  const successAddNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: `Location Added`,
      show: true,
    };

    setAlert(newNotification);
  };

  const failureAddNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "",
      message: "Failed To Add Location, Please Try Again.",
      show: true,
    };

    setAlert(newNotification);
  };

  const handleChangeSelectState = (selectedOption) => {
    setFormData({
      ...formData,
      state: selectedOption.value,
    });
  };
  const handleChangeSelectPartner = (selectedOption) => {
    setFormData({
      ...formData,
      partner_type: selectedOption.value,
      type: selectedOption.label,
    });
  };

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setFormData({
      ...formData,
      phone: cleanedInput,
    });
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  const create = (e) => {
    e.preventDefault();
    setSyncing(true);
    // console.log(formData);
    axios.post("/api/location/add", formData).then((response) => {
      if (response.status == 200) {
        // console.log("response", response.data);
        axios
          .post("api/syncJDE/addLocation", response.data)
          .then((response) => {
            if (response.status == 200) {
              successAddNotification();
              setModal(false);
              setSyncing(false);
              onUpdate(session["user"]["account_id"]);
            }
          });
      } else {
        failureAddNotification();
      }
    });
  };

  const dataProp = () => {
    setFormData({
      practice: "",
      website: "",
      partner_type: "",
      address: "",
      address_2: "",
      city: "",
      zip: "",
      state: "",
      country: "US",
      phone: "",
      type: "",
      default: 0,
      account_id: session["user"]["account_id"],
    });
  };

  useEffect(() => {
    dataProp();
  }, []);

  return (
    <form onSubmit={create}>
      <Modal.Header>Add Location</Modal.Header>
      <Modal.Body className="h-fit">
        <div className="text-sm text-gray-500">* fields are required</div>
        <div className="grid gap-4 mb-4 sm:grid-cols-3">
          <div>
            <label
              htmlFor="practice"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="practice"
              required={true}
              className={"custom-input"}
              placeholder="* Company"
              value={formData.practice}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="website"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="website"
              className="custom-input"
              placeholder="Company Website"
              value={formData.website}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="partner_type"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={partnerType}
              name="partner_type"
              required={true}
              className={"custom-select text-sm"}
              placeholder="* Business Type"
              value={
                formData.partner_type !== ""
                  ? partnerType.find(
                      (option) => option.value === formData.partner_type
                    )
                  : ""
              }
              onChange={handleChangeSelectPartner}
            />
          </div>
          <div>
            <label
              htmlFor="address"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="address"
              required={true}
              className={"custom-input"}
              placeholder="* Address"
              value={formData.address}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="address_2"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="address_2"
              className="custom-input"
              placeholder="Address Line 2"
              value={formData.address_2}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="city"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="city"
              required={true}
              className={"custom-input"}
              placeholder="* City"
              value={formData.city}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="zip"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="zip"
              required={true}
              className={"custom-input"}
              placeholder="* Zipcode"
              value={formData.zip}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="state"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={stateNames}
              onChange={handleChangeSelectState}
              name="state"
              required={true}
              value={
                formData.state !== ""
                  ? stateNames.find((option) => option.value === formData.state)
                  : ""
              }
              className={"custom-select text-sm"}
              placeholder="* State"
            />
          </div>
          <div>
            <label
              htmlFor="phone"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="phone"
              maxLength={14}
              className="custom-input"
              placeholder="Phone"
              value={formatPhone}
              onChange={handlePhoneChange}
            />
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex flex-col justify-center mx-auto">
          <Button
            type="submit"
            pill={true}
            isProcessing={syncing}
            className="theme-button w-56 hover:bg-primary-dark focus:ring-0 focus:ring-transparent"
            // onClick={() => {
            //   create(formData);
            // }}
          >
            Add New Location
          </Button>
        </div>
      </Modal.Footer>
    </form>
  );
}

export default AddLocation;
