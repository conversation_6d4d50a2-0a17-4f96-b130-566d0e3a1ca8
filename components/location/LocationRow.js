import { useState, useEffect, useRef } from "react";
import { useSession, signOut } from "next-auth/react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "flowbite-react";
import { HiBuildingOffice2 } from "react-icons/hi2";
import { BiPencil } from "react-icons/bi";
import { FaTrash } from "react-icons/fa";
import { usePermissions } from "../../contexts/PermissionsContext";
import { createPermissionChecker } from "../../components/utils";
import axios from "axios";
// import Select from "react-select";

import { stateNames } from "../../components/utils";
import LocationEdit from "./LocationEdit";
import { data } from "autoprefixer";

const LocationRow = ({
  location,
  partnerType,
  onUpdate,
  isCheckedProp,
  alert,
  setAlert,
}) => {
  const [editModal, setEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isChecked, setIsChecked] = useState(isCheckedProp);
  const { data: session, status } = useSession();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);

  const primaryNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: "Error: This is the Primary Location, please reach out to Oasis Medical to change.",
      show: true,
    };

    setAlert(newNotification);
  };

  const successAddNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: message,
      show: true,
    };

    setAlert(newNotification);
  };

  const failureAddNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    setAlert(newNotification);
  };

  const handleRowClick = (e) => {
    // console.log("row click", e.jde_id);
    // console.log("acct jde", session.user.Account.jde_id);
    if (e.jde_id == session.user.Account.jde_id) {
      primaryNotification();
      return;
    }
    setEditModal(true);
  };

  const defaultLocationChangeHandler = (location_id) => {
    if (isChecked) {
      failureAddNotification("Default Location must be set");
      return;
    }
    setIsChecked((prev) => !prev);
    const defaultData = {
      location_id: location_id,
      account_id: session["user"]["account_id"],
      email: session["user"]["email"],
      user_id: session["user"]["id"],
      primary: session["user"]["primary_user"],
    };

    setDefault(defaultData);
  };

  const setDefault = (defaultData) => {
    axios
      .post("/api/location/setDefault", defaultData)
      .then(async (response) => {
        if (response.status === 200) {
          // alert("Default location updated");
          // console.log("item_2", response.data.item.location_id);
          // let newLocationId = response.data.item.location_id;
          // update({ location_id: newLocationId }).then(() => {

          successAddNotification("Default location updated");
          onUpdate(session["user"]["account_id"]);
          // });
        } else {
          failureAddNotification("Something went wrong. Please try again.");
        }
      });
  };

  const handleDeleteLocation = async (location_id) => {
    if (location.jde_id === session.user.Account.jde_id) {
      primaryNotification();
      setShowDeleteModal(false);
      return;
    }
  
    try {
      const response = await axios.post(`/api/location/delete`, {
        id: location_id,
      });
  
      if (response.status === 200) {
        successAddNotification("Location deleted successfully.");
        setShowDeleteModal(false);
        onUpdate(session.user.account_id);
      } else {
        failureAddNotification("Something went wrong. Please try again.");
      }
    } catch (error) {
      console.error(error);
      failureAddNotification("Error deleting location. Please try again.");
    }
  };
  

  useEffect(() => {
    setIsChecked(isCheckedProp);
  }, [isCheckedProp]);

  return (
    <>
      <tr
        className="bg-white border-b dark:bg-gray-800 dark:border-gray-700"
        // onClick={handleRowClick}
      >
        <td className="px-6 py-4 flex justify-center">
          <HiBuildingOffice2
            className="bg-gray-100 rounded-full px-3"
            size="40px"
          />
        </td>
        <td
          scope="row"
          className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
        >
          {location ? location.practice : "no data"}
        </td>
        <td className="px-6 py-4">
          {location ? location.address + " " : "no data"}
        </td>
        <td className="px-6 py-4">
          {location.phone && (
            <>
              {`(${location.phone.slice(0, 3)}) `}
              {`${location.phone.slice(3, 6)}-`}
              {location.phone.slice(6)}
            </>
          )}
        </td>
        <td className="px-6 py-4 text-center">
          {location ? location.type : "no data"}
        </td>
        <td className="px-6 py-4">
          <div className="px-6 py-4 flex flex-row justify-center items-center ">
            <input
              type="radio"
              className="custom-radio"
              disabled={session.user.primary_user !== 1}
              checked={isChecked}
              onClick={(e) => {
                e.stopPropagation();
              }}
              onChange={(e) => {
                defaultLocationChangeHandler(location.id);
              }}
            />
          </div>
          {/* // <input
          //   type="radio"
          //   checked={isChecked}
          //   onClick={(e) => {
          //     e.stopPropagation();
          //   }}
          //   onChange={(e) => {
          //     defaultLocationChangeHandler(location.id);
          //   }}
          // /> */}
        </td>
        {hasPermission('Locations', "edit") && (
        <td className="px-6 py-4 self-center">
          <BiPencil
            className="hover:text-blue-500 cursor-pointer"
            size="20px"
            onClick={() => {
              handleRowClick(location);
            }}
          />
        </td>
        )}
        {hasPermission('Locations', "delete") && (
        <td className="px-6 py-4 justify-items-center">
          <FaTrash
            className="hover:text-red-800  text-red-500 cursor-pointer"
            size="15px"
            onClick={() => {
              setShowDeleteModal(true);
            }}
          />
        </td>
        )}
      </tr>

      <Modal
        size="4xl"
        dismissible
        show={editModal}
        onClose={() => setEditModal(false)}
      >
        <LocationEdit
          partnerType={partnerType}
          stateNames={stateNames}
          location={location}
          session={session}
          setEditModal={setEditModal}
          onUpdate={onUpdate}
          setAlert={setAlert}
          alert={alert}
        />
      </Modal>

      <Modal show={showDeleteModal} size="md" onClose={() => showDeleteModal(false)} popup>
        <Modal.Header/>
        <Modal.Body>
          <div className="text-center">
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Are you sure you want to delete this location?
            </h3>
            <div className="flex justify-center gap-4">
              <Button className="secondary-button w-36" pill onClick={() => showDeleteModal(false)}>
                {"No"}
              </Button>
              <Button className="theme-button w-36" pill onClick={() => handleDeleteLocation(location.id)}>
                Yes, Delete
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </>
  );
};

export default LocationRow;
