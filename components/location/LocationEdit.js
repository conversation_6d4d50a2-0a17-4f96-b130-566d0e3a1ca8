import Select from "react-select";
import { useState, useEffect } from "react";
import { Label, TextInput, Modal, Button } from "flowbite-react";
import axios from "axios";

const LocationEdit = ({
  partnerType,
  stateNames,
  location,
  setEditModal,
  onUpdate,
  session,
  setAlert,
  alert,
}) => {
  const [formData, setFormData] = useState({
    practice: "",
    website: "",
    partner_type: "",
    address: "",
    address_2: "",
    city: "",
    zip: "",
    state: "",
    phone: "",
  });
  const [syncing, setSyncing] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const successAddNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: `Location Updated.`,
      show: true,
    };

    setAlert(newNotification);
  };

  const failureAddNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: "Failed To Update Location, Please Try Again.",
      show: true,
    };

    setAlert(newNotification);
  };

  const handleChangeSelectState = (selectedOption) => {
    setFormData({
      ...formData,
      state: selectedOption.value,
    });
  };
  const handleChangeSelectPartner = (selectedOption) => {
    setFormData({
      ...formData,
      partner_type: selectedOption.value,
      type: selectedOption.label,
    });
  };

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setFormData({
      ...formData,
      phone: cleanedInput,
    });
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  const dataProp = (loc) => {
    setFormData({
      id: loc["id"],
      practice: loc["practice"],
      website: loc["website"],
      partner_type: loc["partner_type"],
      type: loc["type"],
      address: loc["address"],
      address2: loc["address_2"],
      city: loc["city"],
      zip: loc["zip"],
      state: loc["state"],
      phone: loc["phone"],
    });
    // console.log("hello", formData);

    let formattedPhone = "";
    if (loc["phone"]) {
      for (let i = 0; i < loc["phone"].length; i++) {
        if (i === 0) {
          formattedPhone = `(${loc["phone"][i]}`;
        } else if (i === 3) {
          formattedPhone += `) ${loc["phone"][i]}`;
        } else if (i === 6) {
          formattedPhone += `-${loc["phone"][i]}`;
        } else {
          formattedPhone += loc["phone"][i];
        }
      }
    }
    setFormatPhone(formattedPhone);
  };

  const edit = (e) => {
    e.preventDefault();
    setSyncing(true);
    axios.post("/api/location/edit", formData).then((response) => {
      if (response.status == 200) {
        // console.log("response", response.data);
        axios
          .post("api/syncJDE/editLocation", response.data)
          .then((response) => {
            if (response.status == 200) {
              successAddNotification();
              setEditModal(false);
              setSyncing(false);
              onUpdate(session["user"]["account_id"]);
            }
          });
      } else {
        failureAddNotification();
      }
    });
  };

  useEffect(() => {
    dataProp(location);
  }, []);

  return (
    <form onSubmit={edit}>
      <Modal.Header>Edit Location</Modal.Header>
      <Modal.Body>
        {/* <div className="text-sm text-gray-500">* fields are required</div> */}
        <div className="grid gap-4 mb-4 sm:grid-cols-3">
          <div>
            <Label
              htmlFor="practice"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></Label>
            <TextInput
              id="practice"
              type="text"
              name="practice"
              required={true}
              placeholder="* Company"
              className="custom-input"
              value={formData.practice}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="website"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              placeholder="Company Website"
              name="website"
              className="custom-input"
              value={formData["website"]}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="partner_type"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={partnerType}
              required={true}
              name="partner_type"
              placeholder="* Business Type"
              className="custom-select text-sm"
              value={partnerType.find(
                (option) => option.value === formData.partner_type
              )}
              data-id="account"
              onChange={handleChangeSelectPartner}
            />
          </div>
          <div>
            <label
              htmlFor="address"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="address"
              required={true}
              placeholder="* Address"
              className="custom-input"
              value={formData["address"]}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="address_2"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="address_2"
              placeholder="Address Line 2"
              className="custom-input"
              value={formData["address_2"]}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="city"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="city"
              required={true}
              placeholder="* City"
              className="custom-input"
              value={formData["city"]}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="zip"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="zip"
              required={true}
              placeholder="* Zipcode"
              className="custom-input"
              value={formData["zip"]}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="state"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={stateNames}
              onChange={handleChangeSelectState}
              name="state"
              placeholder="* State"
              required={true}
              className="custom-select text-sm"
              value={
                formData.state !== ""
                  ? stateNames.find((option) => option.value === formData.state)
                  : ""
              }
            />
          </div>
          <div>
            <label
              htmlFor="phone"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="phone"
              placeholder="Phone"
              className="custom-input"
              value={formatPhone}
              onChange={handlePhoneChange}
              maxLength={14}
            />
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex flex-col justify-center mx-auto">
          <Button
            isProcessing={syncing}
            pill={true}
            type="submit"
            className="theme-button w-48 hover:bg-primary-dark focus:ring-0 focus:ring-transparent"
          >
            Save Changes
          </Button>
        </div>
      </Modal.Footer>
    </form>
  );
};

export default LocationEdit;
