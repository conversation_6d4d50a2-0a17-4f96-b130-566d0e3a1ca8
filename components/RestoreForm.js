import * as React from "react";
import { useState, useEffect } from "react";
import {
  Card,
  Label,
  TextInput,
  Checkbox,
  Button,
  Spinner,
  Modal,
} from "flowbite-react";
import { useRouter } from "next/router";
import { useStepperContext } from "../contexts/StepperContext";
import axios from "axios";

// const syncAccount = async (email) => {
//   try{
//     const res = await fetch("/api/syncJDE/syncAccount", {
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify({ email: email }),
//     });
//     if (res.status === 200) {
//       console.log("Account Updated")
//     } else {
//       throw new Error(await res.text());
//     }
//   }catch (error) {
//     alert(error.message);
//     console.error("An unexpected error while updating Account occurred:", error);
//   }
// }

//TODO: Finish setting up call to api/register with data, also need to make sure userData is cleared if user clicks between
// register and restore.
// Also need to have Restore reset to Account # lookup if clicked out of

export default function Restore({ setModal, changeTab }) {
  const [accountId, setAccountId] = useState("");
  const [formData, setFormData] = useState({
    email: "",
    account_id: "",
    zip: "",
  });
  const [accountFound, setAccountFound] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loginError, setLoginError] = useState("");
  const { router, push } = useRouter();

  const { userData, setUserData } = useStepperContext();

  const cleanUserData = (data) => {
    for (const key in data) {
      if (typeof data[key] === "string") {
        data[key] = data[key].trim();
      }
    }
    return data;
  };

  function hasNumber(str) {
    return /\d/.test(str);
  }

  const handleLookup = async (event) => {
    event.preventDefault();
    event.stopPropagation();

    console.log(accountId);

    //check db for account# and email
    //if not present, send email to JDE to verify

    try {
      setLoginError("");
      setIsLoading(true);
      const res = await fetch("/api/syncJDE/findAccount", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData),
      });
      if (res.status === 200) {
        setIsLoading(false);
        const responseData = await res.json();
        console.log("Response data:", responseData);

        function isObjectEmpty(obj) {
          for (const key in obj) {
            if (obj.hasOwnProperty(key) && obj[key].trim() !== "") {
              return false;
            }
          }
          return true;
        }

        if (
          isObjectEmpty(responseData.categoryCodesAddressBook.categoryCode008)
        ) {
          console.log("NO TERRITORY CODE");
          setUserData({
            ...userData,
            account: cleanUserData({
              business_name: responseData.entityName,
              jde_id: responseData.entity.entityId.toString(),
              // territory_code: territory_code,
              exempt_resale_number:  responseData.taxExemptCertificate,
              address_l1: responseData.address.addressLine1,
              address_l2: responseData.address.addressLine2,
              city: responseData.address.city,
              state: responseData.address.stateCode,
              website: responseData.websiteJDE,
              zip: responseData.address.postalCode,
              // partner_type: partner_type_id,
              email_marketing_on: 1,
              num_locations: 1,
            }),
            user: cleanUserData({
              firstName: responseData.firstName,
              lastName: responseData.lastName,
              address: responseData.address.addressLine1,
              address_l2: responseData.address.addressLine2,
              city: responseData.address.city,
              state: responseData.address.stateCode,
              zip: responseData.address.postalCode,
              email: formData.email,
              // email: responseData.emailJDE,
            }),
            childLocations: cleanUserData(responseData.childLocations),
            source: 1, //restore
            approved: 1, //Account can login after register
          });
        } else {
          setUserData({
            ...userData,
            account: cleanUserData({
              business_name: responseData.entityName,
              jde_id: responseData.entity.entityId.toString(),
              territory_code:
                responseData.categoryCodesAddressBook.categoryCode008,
                exempt_resale_number:  responseData.taxExemptCertificate,
              address_l1: responseData.address.addressLine1,
              address_l2: responseData.address.addressLine2,
              city: responseData.address.city,
              state: responseData.address.stateCode,
              website: responseData.websiteJDE,
              zip: responseData.address.postalCode,
              // partner_type: partner_type_id,
              email_marketing_on: 1,
              num_locations: 1,
            }),
            user: cleanUserData({
              firstName: responseData.firstName,
              lastName: responseData.lastName,
              address: responseData.address.addressLine1,
              address_l2: responseData.address.addressLine2,
              city: responseData.address.city,
              state: responseData.address.stateCode,
              zip: responseData.address.postalCode,
              email: formData.email,
              // email: responseData.emailJDE,
            }),
            childLocations: cleanUserData(responseData.childLocations),
            source: 1, //restore
            approved: 1, //Account can login after register
          });
        }

        setAccountFound(true);
      } else {
        setIsLoading(false);
        setLoginError((await res.text()).replace(/^"(.*)"$/, "$1"));
        // throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error("An unexpected error happened occurred:", error);
    }
  };

  const handleChangeLookup = (e) => {
    const value = e.target.value;
    setFormData({
      ...formData,
      [e.target.name]: value,
    });
  };

  const handleLinkClick = () => {
    console.log("Restore Data", userData);
    changeTab(1);
    setModal(false);
  };

  useEffect(() => {}, []);

  return (
    <>
      <Modal.Header>Restore Your Account</Modal.Header>
      <Modal.Body>
        <div className="flex flex-col justify-center ">
          {!accountFound && (
            <form
              className="flex flex-col gap-4 justify-center pb-10 pt-0 w-3/4 m-auto"
              onSubmit={handleLookup}
            >
              <div className="flex flex-col text-center pb-0 text-sm font-normal">
                Please provide the following information in order to restore
                your existing account:
              </div>
              <div>
                {isLoading && (
                  <div className="text-center">
                    <div className="m-auto text-center pb-2">
                      Looking for account...
                    </div>
                    <Spinner aria-label="Default status example" size="md" />
                  </div>
                )}
                <div className="text-center text-red-500 pb-2">
                  {loginError}
                </div>
                <div className="mb-2">
                  <Label
                    htmlFor="email"
                    value="Email Address:"
                    className="text-xs font-normal"
                  />
                </div>
                <div className="mb-4">
                  <TextInput
                    name="email"
                    type="text"
                    placeholder="<EMAIL>"
                    required={true}
                    value={formData.email}
                    onChange={handleChangeLookup}
                    className="custom-input"
                  />
                </div>
                <div className="mb-2 block">
                  <Label
                    htmlFor="account_id"
                    value="OasisMedical.com Account # (5-7 digits):"
                    className="text-xs font-normal"
                  />
                </div>
                <div className="mb-4">
                  <TextInput
                    name="account_id"
                    type="text"
                    placeholder="Account #"
                    required={true}
                    value={formData.account_id}
                    onChange={handleChangeLookup}
                    className="custom-input"
                  />
                </div>
                <div className="mb-2 block">
                  <Label
                    htmlFor="zip"
                    value="Billing Zip Code (5 digits):"
                    className="text-xs font-normal"
                  />
                </div>
                <div className="mb-4">
                  <TextInput
                    name="zip"
                    type="text"
                    placeholder="Zipcode"
                    required={true}
                    value={formData.zip}
                    onChange={handleChangeLookup}
                    className="custom-input"
                  />
                </div>
              </div>
              <Button
                type="submit"
                pill={true}
                className="w-2/4 mx-auto bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
              >
                Lookup
              </Button>
            </form>
          )}
          {accountFound && (
            <div className="pb-8 w-3/4 m-auto">
              <div className="flex flex-col text-center pb-4 font-normal">
                Account Found. Please complete registration to finish setting up
                your account.
              </div>
              <Button
                className="w-2/4 mx-auto bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
                onClick={handleLinkClick}
              >
                Go To Register
              </Button>
            </div>
          )}

          <div className="text-center text-xs text-primary">
            Please call Customer Service at (844) 820-8940 if assistance is
            required with restoring your account.
          </div>
        </div>
      </Modal.Body>
    </>
  );
}
