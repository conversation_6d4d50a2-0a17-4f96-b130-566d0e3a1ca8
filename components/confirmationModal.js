import { Mo<PERSON>, But<PERSON> } from "flowbite-react";
import { useState } from "react";

function ConfirmationModal({ isOpen, onClose, onConfirm, message }) {
  return (
    <Modal show={isOpen} onClose={onClose}>
      <Modal.Header>Confirm</Modal.Header>
      <Modal.Body>
        <div className="text-base ">{message}</div>
      </Modal.Body>
      <Modal.Footer>
      <div className="flex flex-row justify-end gap-x-3" style={{ marginLeft: "auto" }}>
      <Button pill={true} color="gray" className="secondary-button" onClick={onClose}>
          Cancel
        </Button>
        <Button
            pill={true}
            className="theme-button"
            onClick={() => {
            onConfirm();
            onClose();
          }}
        >
          Confirm
        </Button>
        
        </div>
      </Modal.Footer>
    </Modal>
  );
}

export default ConfirmationModal;
