import { Radio } from "flowbite-react";

export default function PaymentMethod({ item, setCheckoutForm, id }) {
    // console.log(item);
    const selection = () => {
      setCheckoutForm((prevState) => ({
        ...prevState,
        payment_id: item.db.id,
        payment_profile: item.db.profileid,
        payment_token: item.cardpointe[0].token,
        payment_expiry: item.cardpointe[0].expiry,
        payment_postal: item.cardpointe[0].postal,
        payment_name: item.cardpointe[0].name,
        billing_id: item.db.billing_id,
      }));
    };
  
    return (
      <div className="flex flex-row gap-10 justify-center py-[30px] px-[34px]" id={id}>
        <div>
          <Radio
            onChange={selection}
            id={item}
            name="billing"
            value={item}
            className="custom-radio"
          />
        </div>
        <div className="grow">
          <p className="text-[#353535] font-semibold">{item.db.nickname}</p>
        </div>
        <div className="grow">
          <p className="text-[#353535]">Exp. {item.cardpointe[0].expiry}</p>
        </div>
        <div className="grow">
          <p className="text-[#353535]">{item.db.last_four}</p>
        </div>
        {/* <div>
                    <button>
                      <img
                        src="/edit.png"
                        className="w-[16.67px] h-[16.67px]"
                      ></img>
                    </button>
                  </div> */}
      </div>
    );
  }