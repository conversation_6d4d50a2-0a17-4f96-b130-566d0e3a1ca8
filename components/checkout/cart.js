import { Badge, TextInput } from "flowbite-react";
import { useRouter } from "next/router";
import { useCart } from "../../contexts/CartContext";
import { useEffect, useState } from "react";

import axios from "axios";
import { useSession } from "next-auth/react";
import { useGlobalAlertContext } from "../../contexts/GlobalAlertContext";
import { usePromotionsContext } from '../../contexts/PromotionsContext'; // Import the promotion context

export default function Cart({data, cart,updateCart, index}) {
  // const { cart, updateCart } = useCart();
  const { data: session, status } = useSession();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const { promotions } = usePromotionsContext(); // Access promotions from context


  const [qty, setQty] = useState(cart[index].qty);
  // const [qty, setQty] = useState();

  function truncateProductName(str) {
    if (str.length > 50) {
      str = str.slice(0, 50).trim();
      str += "...";
    }

    return str;
  }

  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  // console.log("cart: " + value);

  const router = useRouter();

  const removeById = () => {
    axios({
      method: "delete",
      url: "/api/cart/removeItemById",
      data: {
        id: data.id,
      },
    }).then((response) => {
      if (response.status == 200) {
        // console.log("response", response.data);
        onUpdate("delete");
        // console.log("THIS IS DELETING");
        successUpdateNoti(
          `${truncateProductName(data.Product.name)} removed from cart.`
        );
      }
    });
  };

  const updateCartQty = (e) => {
    e.preventDefault();
    console.log("qty prev", e.target.placeholder);
    console.log("qty", e.target.value);
    console.log("Index:", data.id);
    const index = data.id;
    const enteredQty = e.target.value;

    if (enteredQty < 0) {
      setQty(e.target.placeholder);
      setCartAlert({
        color: "failure",
        show: true,
        message: `Quantity cannot be less than 0`,
      });
      // setTimeout(() => {
      //   setCartAlert({
      //     message: "",
      //     color: "",
      //     show: false,
      //   });
      // }, 2000);
    } else if (enteredQty == 0) {
      axios({
        method: "delete",
        url: "/api/cart/removeItemById",
        data: {
          id: data.id,
        },
      }).then((response) => {
        if (response.status == 200) {
          // console.log("response", response.data);
          onUpdate("delete");
          // console.log("THIS IS DELETING");
          successUpdateNoti(
            `${truncateProductName(data.Product.name)} removed from cart.`
          );
          // setTimeout(() => {
          //   setCartAlert({
          //     message: "",
          //     color: "",
          //     show: false,
          //   });
          // }, 2000);
        }
      });
    } else if (enteredQty > 0) {
      axios({
        method: "POST",
        url: "/api/cart/updateQty",
        data: {
          id: data.id,
          qty: qty,
          account_jde: session.user.Account.jde_id,
          promotions:promotions
        },
      }).then((response) => {
        if (response.status == 200) {
          console.log("response", response.data);
          onUpdate("update");
          console.log("THIS IS UPDATING");
          successUpdateNoti(
            `${truncateProductName(data.Product.name)} quantity updated.`
          );
          // setTimeout(() => {
          //   setCartAlert({
          //     message: "",
          //     color: "",
          //     show: false,
          //   });
          // }, 2000);
          console.log("Getting Cart?", cart);
        }
      });
    }
  };

  const onUpdate = (e) => {
    // console.log("updateCartQty Running...");
    if (session) {
      axios
        .get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        )
        .then((response) => {
          const updatedCart = response.data.items;
          updateCart(updatedCart);
          // updateTax();
        });
    }
  };

  const successUpdateNoti = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: message,
      show: true,
    };

    // console.log("New Notif", newNotification);

    addNotifications(newNotification);
  };

  const failureUpdateNoti = (message) => {
    console.log("New Fail Notif");
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };

  useEffect(() => {
    setQty(cart[index].qty);
    // updateTax();
  }, [cart]);

  // console.log("checkout cart", cart);

  return (
    <div className="px-5" key={index}>
      <a
        href="#"
        className="flex items-center gap-4 py-3 px-4 hover:bg-gray-100 dark:hover:bg-gray-600 dark:border-gray-600"
      >
      <div className="flex-shrink-0">
        <img
          className="w-11 h-11 sqaure"
          src={
            data.Product.ProductImage &&
            data.Product.ProductImage.length < 1
              ? "/no_image.jpeg"
              : `${
                  process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                  "oasis-product-images/"
                }${data.Product.ProductImage[0].path}_l.${
                  data.Product.ProductImage[0].extension
                }`
          }
          alt=""
        />
      </div>
      <div className="flex-grow">
        <div className="text-black font-normal text-sm mb-1.5">
          {data.Product.name}
        </div>
        <div className="inline-flex items-center ">
          <div className="text-xs font-medium text-gray-400 pr-4">
            {data.Product.sku}
          </div>
          {data.Product && data.Product.stock > 0 ? (
            <Badge color="success" size="xs">
              In Stock
            </Badge>
          ) : (
            <Badge color="failure" size="xs">
              Out of Stock
            </Badge>
          )}
        </div>
      </div>
      <div className="flex-shrink-0">
      <div className="flex items-center gap-5">
        <div className="flex-none">
          <TextInput
            className="inline-block align-top bg-[#F8F8F8] w-[75px] text-center rounded-md mt-2"
            type="number"
            placeholder={qty}
            value={qty}
            onChange={(e) => setQty(e.target.value)}
            onBlur={(e) => updateCartQty(e)}
          />
        </div>
        {/* <div className="flex-none bg-[#F8F8F8] h-[46px] w-[75px] text-center rounded-md mt-1">
          <span className="inline-block align-top mt-2">{data.qty}</span>
        </div> */}
        <div className="flex-1 w-20 text-right">
          {formatPrice.format(data.price)}
        </div>
        <div className="">
          <button onClick={removeById}>
            <img src="/trashcan.png"></img>
          </button>
        </div>
      </div>
      </div>
      </a>
      <hr className="h-px bg-gray-200 border-0 -mx-6" />
    </div>
  );
}
