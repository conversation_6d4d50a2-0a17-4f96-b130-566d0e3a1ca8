import { Radio, Accordion } from "flowbite-react";
import { useSession, signOut } from "next-auth/react";


export default function Locations({ item, shippingSelected, setCheckoutForm, id }) {
  const selection = () => {
    // console.log("Locations ID", item.id);
    setCheckoutForm((prevState) => ({
      ...prevState,
      shipping_id: item.id,
    }));
    shippingSelected(item.zip.substring(0, 5), "US", item.id);
  };

  const { data: session, status } = useSession();
  // console.log(item)
  if (session) {
    return (
      <div className="flex flex-row gap-10 justify-center py-[5px] px-[34px]" id={id}>
        <div>
          <Radio
            id={item.id}
            name="shipping"
            value={item}
            className="custom-radio"
            onChange={selection}
            // onClick={() => {
            //   shippingSelected(item.zip.substring(0, 5), "US", item.id);
            // }}
          />
          {/* <input
            type="radio"
            checked={isChecked}
            onClick={() => {
              setIsChecked(item.id);
              selection(item);
              shippingSelected(item.zip, "US");
            }}
          /> */}
        </div>
        <div className="grow">
          <p className="text-[#353535] font-semibold">{item.practice}</p>
        </div>
        <div className="grow text-right mr-8">
          <p className="text-[#353535]">
            {item.address +
              ", " +
              item.city +
              ", " +
              item.state +
              " " +
              item.zip?.substring(0, 5)}
          </p>
        </div>
        {/* <div>
                    <button>
                      <img
                        src="/edit.png"
                        className="w-[16.67px] h-[16.67px]"
                      ></img>
                    </button>
                  </div> */}
      </div>
    );
  }
}
