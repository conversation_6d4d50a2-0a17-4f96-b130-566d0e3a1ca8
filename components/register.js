import * as React from "react";
import {useState} from "react";
import Link from 'next/link'
import Head from 'next/head'
import Image from 'next/image'
import { Card, Label ,TextInput,Checkbox,Button,Tabs} from "flowbite-react";
import {useRouter} from "next/router";
import {signIn} from "next-auth/react";



export default function Register() {
  // const [password, setPassword] = useState('');
  // const [email, setEmail] = useState('');
  // const [firstName, setFirstName] = useState('');
  // const [lastName, setLastName] = useState('');
  // const router = useRouter();

  // const registerUser = async (event) => {
  //   event.preventDefault();
    
  //   const data = {
  //       firstName: firstName,
  //       lastName: lastName,
  //       email: email,
  //       password: password
  //   }
  //     console.log(data);
  //   try {
  //     const res = await fetch('/api/register', {
  //       method: 'POST',
  //       headers: { 'Content-Type': 'application/json' },
  //       body: JSON.stringify(data),
  //     })
  //     if (res.status === 200) {
  //       Router.push('/')
  //     } else {
  //       throw new Error(await res.text())
  //     }
  //   } catch (error) {
  //     console.error('An unexpected error happened occurred:', error)
  //   }
  //   signIn("credentials", {
  //       email, password, callbackUrl: `${window.location.origin}/dashboard`, redirect: false }
  //   ).then(function(result) {
  //       router.push(result.url)
  //   }).catch(err => {
  //       alert("Failed to register: " + err.toString())
  //   });
  // }
  return //(
    // <div className="flex justify-center">
      
    //     <form className="grid grid-cols-3  gap-4 w-3/4 mx-auto" onSubmit={registerUser}>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="firstName"
    //             value="First Name"
    //           />
    //         </div>
    //         <TextInput
    //           id="firstName"
    //           type="text"
    //           placeholder="First Name"
    //           required={true}
    //           value={firstName} 
    //           onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="lastName"
    //             value="Last Name"
    //           />
    //         </div>
    //         <TextInput
    //           id="lastName"
    //           type="text"
    //           placeholder="Last Name"
    //           required={true}
    //           value={lastName} 
    //           onChange={(e) => setLastName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="jobTitle"
    //             value="Job Title"
    //           />
    //         </div>
    //         <TextInput
    //           id="jobTitle"
    //           type="text"
    //           placeholder="Job Title"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="businessName"
    //             value="Business Name"
    //           />
    //         </div>
    //         <TextInput
    //           id="businessName"
    //           type="text"
    //           placeholder="Business Name"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="website"
    //             value="Website"
    //           />
    //         </div>
    //         <TextInput
    //           id="website"
    //           type="text"
    //           placeholder="Website"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="phone"
    //             value="Phone"
    //           />
    //         </div>
    //         <TextInput
    //           id="phone"
    //           type="text"
    //           placeholder="Phone"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="fax"
    //             value="Fax"
    //           />
    //         </div>
    //         <TextInput
    //           id="fax"
    //           type="text"
    //           placeholder="Fax"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="address_l1"
    //             value="Street Address"
    //           />
    //         </div>
    //         <TextInput
    //           id="address_l1"
    //           type="text"
    //           placeholder="Street Address"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="address_l2"
    //             value="Address Line 2"
    //           />
    //         </div>
    //         <TextInput
    //           id="address_l2"
    //           type="text"
    //           placeholder="Address Line 2"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="city"
    //             value="City"
    //           />
    //         </div>
    //         <TextInput
    //           id="city"
    //           type="text"
    //           placeholder="City"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="zip"
    //             value="Zip"
    //           />
    //         </div>
    //         <TextInput
    //           id="zip"
    //           type="text"
    //           placeholder="Zip"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="state"
    //             value="State"
    //           />
    //         </div>
    //         <TextInput
    //           id="state"
    //           type="text"
    //           placeholder="State"
    //           // required={true}
    //           // value={firstName} 
    //           // onChange={(e) => setFirstName(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="email2"
    //             value="Email/Username"
    //           />
    //         </div>
    //         <TextInput
    //           id="email2"
    //           type="email"
    //           placeholder="Email/Username"
    //           required={true}
    //           shadow={true}
    //           value={email} onChange={(e) => setEmail(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="password2"
    //             value="Password"
    //           />
    //         </div>
    //         <TextInput
    //           id="password2"
    //           type="password"
    //           placeholder="Password"
    //           required={true}
    //           shadow={true}
    //           value={password} 
    //           onChange={(e) => setPassword(e.target.value)}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div>
    //         <div className="mb-2 block">
    //           <Label
    //             htmlFor="repeat-password"
    //             value="Confirm password"
    //           />
    //         </div>
    //         <TextInput
    //           id="repeat-password"
    //           type="password"
    //           placeholder="Confirm Password"
    //           required={true}
    //           shadow={true}
    //           className="custom-input"
    //         />
    //       </div>
    //       <div className="flex justify-center col-start-2">
    //       <p className="text-center text-xs">*Passowrds must contain (x?) characters with, 1 uppercase, 1 lowercase and 1 number</p>
    //       </div>
    //       <Button className="col-span-3" type="submit" pill={true} className="bg-primary">
    //         Next
    //       </Button>
    //     </form>
      
    //   </div>
  // )
}
