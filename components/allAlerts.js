import { Toast } from "flowbite-react";
import { HiInformationCircle, HiX, HiFire, Hi<PERSON>he<PERSON> } from "react-icons/hi";
import { MdAnnouncement } from "react-icons/md";

function AllAlerts({notification, id }) {
  // console.log("All Alerts", notification);
  return (
    <Toast className={`${notification.bgColor} fade-out`} id={id}>
      <div>
        <HiCheck className={`${notification.iconColor.bg} ${notification.iconColor.text} inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg`} />
      </div>
      <div className="mx-4 text-sm text-black font-normal">{notification.message}</div>
      <Toast.Toggle />
    </Toast>
  );
}

export default AllAlerts;
