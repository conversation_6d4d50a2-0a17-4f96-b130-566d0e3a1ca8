import * as React from "react";
import { useState } from "react";
import {
  Card,
  Label,
  TextInput,
  Checkbox,
  Button,
  Tabs,
  Modal,
} from "flowbite-react";
import { useRouter } from "next/router";
import { signIn } from "next-auth/react";
import { FaUser, FaUnlock } from "react-icons/fa";
import RestoreForm from "./RestoreForm";
import { useUserAcctContext } from "../contexts/UserAcctContext";

export default function Login({ changeTab , setIsSyncingComplete}) {
  const { userAcct, setUserAcct } = useUserAcctContext();
  const [email, setEmail] = useState("");
  const [syncing, setSyncing] = useState(false);
  const [password, setPassword] = useState("");
  const [loginError, setLoginError] = useState("");
  const [modal, setModal] = useState(false);
  const router = useRouter();

  const handleLogin = async (event) => {
    setIsSyncingComplete(false);
    setSyncing(true);
    event.preventDefault();
    event.stopPropagation();
  
    try {
      const result = await signIn("credentials", {
        email,
        password,
        // callbackUrl: `${window.location.origin}/dashboard`,
        redirect: false,
      });
  
      if (result.error !== null) {
        if (result.status === 401) {
          setLoginError("Your email/password combination was incorrect. Please try again");
        } else {
          setLoginError(result.error);
        }
        setSyncing(false);
        return;
      }
  
      setDetails(email);
  
      await updateInvoices(email);

      // Wait for syncOfflineOrders to finish before moving to setLastLogin
      await syncOfflineOrders(email);
  
      // After syncOfflineOrders completes, setLastLogin will run
      await setLastLogin(email);
  
    } catch (error) {
      alert(error.message);
      console.error("An unexpected error occurred:", error);
    } finally {
      setSyncing(false);
      setIsSyncingComplete(true);
      // window.location.href = `${window.location.origin}/dashboard`;
    }
  };
  

  const syncAccount = async (email) => {
    try {
      const res = await fetch("/api/syncJDE/syncAccount", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        console.log("Account Updated");
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while updating Account occurred:",
        error
      );
    }
  };

  const setLastLogin = async (email) => {
    try {
      const res = await fetch("/api/user/setLastLogin", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        console.log(res.response);
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while updating Account occurred:",
        error
      );
    }
  };

  const syncOfflineOrders = async (email) => {
    try {
      const res = await fetch("/api/syncJDE/syncOfflineOrders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        console.log(res.response);
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while syncing offline orders occurred:",
        error
      );
    }
  };

  const updateInvoices = async (email) => {
    try {
      const res = await fetch("/api/syncJDE/updateInvoices", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        console.log(res.response);
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while updating invoices occurred:",
        error
      );
    }
  };

  const setDetails = async (email) => {
    try {
      const res = await fetch("/api/user/setDetails", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        const data = await res.json();
        const tc =
          data.Account.territory_code && data.Account.oasis_rep_id
            ? true
            : false;
        setUserAcct({
          user: {
            id: data.id,
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            location_id: data.location_id,
            account_id: data.account_id,
            primary_user: data.primary_user,
            group_id: data.group_id,
            phone: data.phone,
            position: data.position,
            address: data.address,
            address_l2: data.address_2,
            city: data.city,
            state: data.state,
            zip: data.zip,
          },
          account: {
            id: data.Account.id,
            jde_id: data.Account.jde_id,
            business_name: data.Account.business_name,
            approved: data.Account.approved,
            territory_code: tc ? data.Account.territory_code : "",
            oasis_rep_id: tc ? data.Account.oasis_rep_id : "",
            oasis_rep_name: tc ? data.Account.OasisRep.name : "",
            oasis_rep_email: tc ? data.Account.OasisRep.email : "",
            oasis_rep_phone: tc ? data.Account.OasisRep.phone : "",
          },
        });
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      alert(error.message);
      console.error(
        "An unexpected error while updating Account occurred:",
        error
      );
    }
  };

  return (
    <div className="flex flex-col justify-center">
      <form
        className="flex flex-col gap-4 justify-center pt-24 pb-24 w-2/4 m-auto"
        onSubmit={handleLogin}
      >
        <div>
          {loginError}
          <div className="mb-2 block">
            <Label htmlFor="email1" />
          </div>
          <TextInput
            id="email1"
            type="email"
            placeholder="Email"
            required={true}
            icon={FaUser}
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-2 block">
            <Label htmlFor="password1" />
          </div>
          <TextInput
            id="password1"
            type="password"
            placeholder="Password"
            required={true}
            icon={FaUnlock}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="custom-input"
          />
        </div>
        <div className="flex items-center gap-2 pb-2">
          <a
            href="/forgotPassword"
            className="text-primary-light hover:underline text-xs"
          >
            Forgot Password
          </a>
        </div>

        <Button
          type="submit"
          isProcessing={syncing}
          pill={true}
          className="w-2/4 mx-auto bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
        >
          Login
        </Button>
      </form>
      <div className="flex text-center m-auto py-2">
        <a
          href="#"
          onClick={() => {
            setModal(true);
          }}
          className="text-primary-light hover:underline text-md"
        >
          Click here to restore your existing OasisMedical.com account
        </a>
        <Modal
          show={modal}
          onClose={() => {
            setModal(false);
          }}
        >
          <RestoreForm setModal={setModal} changeTab={changeTab}></RestoreForm>
        </Modal>
      </div>
    </div>
  );
}
