import { useEffect, useState } from "react";
import Link from "next/link";
import axios from "axios";
import { useRouter } from "next/router";
import { useSession, signOut } from "next-auth/react";
import {
  FaCheckCircle,
  FaRegCircle,
  FaDesktop,
  FaPhoneAlt,
} from "react-icons/fa";
import { Modal, Table, Accordion, Pagination } from "flowbite-react";

const SearchModal = ({
  searchResults,
  setSearchResults,
  setSearchModal,
  clearInputField,
}) => {
  const { data: session, status } = useSession();
  const [filteredOrders, setFilteredOrders] = useState({
    orders: [],
    selectedProduct: [],
    selectedOrders: false,
  });
  const { products, orders, invoices } = searchResults;
  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  const router = useRouter();

  // User is currently on this page
  const [currentPage, setCurrentPage] = useState(1);
  // No of Records to be displayed on each page
  const [recordsPerPage] = useState(5);

  const indexOfLastRecord = currentPage * recordsPerPage;
  const indexOfFirstRecord = indexOfLastRecord - recordsPerPage;

  // Records to be displayed on the current page
  const currentRecords = products.slice(indexOfFirstRecord, indexOfLastRecord);

  const nPages = Math.ceil(products.length / recordsPerPage);

  const pageNumbers = [...Array(nPages + 1).keys()].slice(1);

  const onPageChange = (page) => {
    console.log(page);
    setCurrentPage(page);
  };

  const handleSearchEdit = (e) => {
    e.preventDefault();
    console.log(e.target[0].value);

    const query = e.target[0].value;

    axios
      .get("/api/globalSearch/getGlobalSearchResults", {
        params: {
          query: query,
          user_id: session.user.id,
          group_id: session.user.group_id,
          location_id: session.user.location_id,
          account_id: session.user.account_id,
        },
      })
      .then((response) => {
        const results = response.data;
        // console.log(results);
        setSearchResults({
          products: results.products ? results.products : [],
          orders: results.orders ? results.orders : [],
          invoices: results.invoices ? results.invoices : [],
        });
        setFilteredOrders({
          orders: [],
          selectedProduct: [],
          selectedOrders: false,
        });
        setCurrentPage(1);
        // setSearchModal(true);
      });
  };

  const handleOrdersClick = (e, item) => {
    e.preventDefault();
    // console.log("item", item);
    // console.log("e", e);
    axios
      .get("/api/globalSearch/getOrdersForProduct", {
        params: {
          id: item.id,
          account_id: session.user.account_id,
        },
      })
      .then((response) => {
        const results = response.data;
        // console.log(results);
        setFilteredOrders({
          orders: results ? results : [],
          selectedOrders: true,
          selectedProduct: item,
        });
      });
  };

  const handleProductClick = (e, item) => {
    e.preventDefault();
    // console.log("item", item.id);

    setSearchModal(false);
    setSearchResults({
      products: [],
      orders: [],
      invoices: [],
      filtered: false,
    });
    clearInputField();

    router.push({
      pathname: "/product",
      query: {
        name: item.Product ? item.Product.name : item.name,
        sku: item.Product ? item.Product.sku : item.sku,
        id: item.Product ? item.Product.id : item.id,
      },
    });
  };

  // console.log(
  //   "filteredOrders",
  //   filteredOrders.selectedProduct ? "true" : "false"
  // );
  // console.log("filteredOrders", filteredOrders.selectedProduct);

  function truncateProductName(str) {
    if (str.length > 60) {
      str = str.slice(0, 60).trim();
      str += "...";
    }

    return str;
  }

  function trimWS(str) {
    str = str.trim();

    return str;
  }

  const trackingURLs = {
    FedEx: ['C1F', 'C2A', 'C2D', 'C3D', 'CFP', 'CFS', 'CGN', 'CIE', 'CIP', 'CSA', 'CTN', 'F14', 'FDX', 'FGN', 'FGR', 'FX3', 'FXG', 'N1A', 'N1P', 'N2A', 'N2B', 'N2D', 'N3D', 'NFS', 'NGN', 'NST', 'P1P', 'P1S', 'P2A', 'P2D', 'P3D', 'PFG', 'PFS', 'PI3', 'PIP', 'PST', 'R10', 'R11', 'R15', 'R17', 'R18', 'R20', 'R21', 'R26', 'R27', 'R2D', 'R3D', 'R3Y', 'R95', 'RG7', 'WF2', 'WFG', 'WFR'],
    UPS: ['C8A', 'CU1', 'CU2', 'CU3', 'CUG', 'CUI', 'CUN', 'NU3', 'NUG', 'P1A', 'PU2', 'PU3', 'PUA', 'PUG', 'PUI', 'PUN', 'WGP', 'WU2', 'WUG', 'WUP']
  };

  const getShippingProvider= (shippingMethod) => {
      const provider = Object.keys(trackingURLs).find(provider => trackingURLs[provider].includes(shippingMethod));
      return provider || 'neither';
  };

  return (
    <>
      {/* <Modal.Header className="">Results</Modal.Header> */}
      <Modal.Body>
        <form
          // action="#"
          // method="GET"
          className="text-center mb-2"
          onSubmit={handleSearchEdit}
        >
          <label htmlFor="modal-search" className="sr-only">
            Search
          </label>
          <div className="relative mt-1 w-1/3">
            <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
              <svg
                className="w-5 h-5 text-gray-400 dark:text-gray-400"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                  clipRule="evenodd"
                ></path>
              </svg>
            </div>
            <input
              type="text"
              name="query"
              id="modal-search"
              className="bg-neutral-200 text-gray-800 border-0 sm:text-sm rounded-lg block w-full pl-10 p-3.5 dark:bg-gray-700 "
              placeholder="New Search"
            />
          </div>
        </form>
        <Accordion flush>
          <Accordion.Panel id="products">
            <Accordion.Title className="text-gray-800">
              {filteredOrders.selectedOrders ? "Selected Product" : "Products"}{" "}
              <span className=" text-gray-400">
                {filteredOrders.selectedOrders ? "1" : products.length}
              </span>
            </Accordion.Title>
            <Accordion.Content>
              {(
                filteredOrders.selectedOrders
                  ? filteredOrders.selectedProduct
                  : products.length > 0
              ) ? (
                <Table hoverable>
                  <Table.Head>
                    <Table.HeadCell className="text-left w-1/43">
                      Product name
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Ordered?
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">SKU</Table.HeadCell>
                    {/* <Table.HeadCell className="text-center">
                      Price
                    </Table.HeadCell> */}
                  </Table.Head>
                  <Table.Body className="">
                    {filteredOrders.selectedOrders ? (
                      <Table.Row className="">
                        <Table.Cell
                          className="text-blue-600 hover: cursor-pointer text-left"
                          onClick={(e) => {
                            handleProductClick(e, item);
                          }}
                        >
                          {/* <Link
                              href={{
                                pathname: "/product",
                                query: {
                                  name: item.Product
                                    ? item.Product.name
                                    : item.name,
                                  sku: item.Product
                                    ? item.Product.sku
                                    : item.sku,
                                  id: item.Product ? item.Product.id : item.id,
                                },
                              }}
                            > */}
                          {filteredOrders.selectedProduct.name}
                          {/* </Link> */}
                        </Table.Cell>
                        <Table.Cell
                          className="text-center px-4 py-4 text-blue-600 hover: cursor-pointer"
                          onClick={(e) => {
                            handleOrdersClick(
                              e,
                              filteredOrders.selectedProduct
                            );
                          }}
                        >
                          View
                        </Table.Cell>

                        <Table.Cell className="text-center">
                          {filteredOrders.selectedProduct.sku}
                        </Table.Cell>
                        {/* <Table.Cell className="text-center">
                          {formatPrice.format(
                            filteredOrders.selectedProduct.price
                          )}
                        </Table.Cell> */}
                      </Table.Row>
                    ) : (
                      currentRecords.map((item) => {
                        return (
                          <Table.Row className="">
                            <Table.Cell
                              className="text-left text-blue-600 hover: cursor-pointer"
                              onClick={(e) => {
                                handleProductClick(e, item);
                              }}
                            >
                              {/* <Link
                              href={{
                                pathname: "/product",
                                query: {
                                  name: item.Product
                                    ? item.Product.name
                                    : item.name,
                                  sku: item.Product
                                    ? item.Product.sku
                                    : item.sku,
                                  id: item.Product ? item.Product.id : item.id,
                                },
                              }}
                            > */}
                              {truncateProductName(item.name)}
                              {/* </Link> */}
                            </Table.Cell>

                            {orders.some((orderGroup) =>
                              orderGroup.OrderedProduct.some(
                                (order) => order.Product.id === item.id
                              )
                            ) ? (
                              <Table.Cell
                                className="text-center px-4 py-4 text-blue-600 hover: cursor-pointer"
                                onClick={(e) => {
                                  handleOrdersClick(e, item);
                                }}
                              >
                                View
                              </Table.Cell>
                            ) : (
                              <Table.Cell className="text-center px-4 py-4 "></Table.Cell>
                            )}
                            <Table.Cell className="text-center">
                              {trimWS(item.sku)}
                            </Table.Cell>
                            {/* <Table.Cell className="text-center">
                              {formatPrice.format(item.price)}
                            </Table.Cell> */}
                          </Table.Row>
                        );
                      })
                    )}
                  </Table.Body>
                </Table>
              ) : (
                "No Results Found"
              )}

              {products.length > recordsPerPage &&
                !filteredOrders.selectedOrders && (
                  <div className="flex overflow-x-auto justify-center">
                    <Pagination
                      layout="navigation"
                      currentPage={currentPage}
                      totalPages={nPages}
                      onPageChange={onPageChange}
                      showIcons
                    />
                  </div>
                )}
            </Accordion.Content>
          </Accordion.Panel>
          <Accordion.Panel
            id="orders"
            setOpen={() => filteredOrders.selectedOrders}
            // open={filteredOrders.selectedOrders}
          >
            <Accordion.Title className="text-gray-800">
              {filteredOrders.selectedOrders
                ? "Orders Containing Product"
                : "Orders"}{" "}
              <span className=" text-gray-400">
                {filteredOrders.selectedOrders
                  ? filteredOrders.orders.length
                  : orders.length}
              </span>
            </Accordion.Title>

            <Accordion.Content>
              {(filteredOrders.selectedOrders
                ? filteredOrders.orders.length
                : orders.length) > 0 ? (
                <Table hoverable>
                  <Table.Head>
                    <Table.HeadCell className="text-center">
                      Date
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Customer
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Auto Delivery
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Tracking
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Order Number
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Total
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Status
                    </Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="">
                    {(filteredOrders.selectedOrders
                      ? filteredOrders.orders
                      : orders
                    ).map((item) => {
                      const inputDate = new Date(item.date);
                      const options = {
                        year: "2-digit",
                        month: "numeric",
                        day: "numeric",
                      };
                      const formattedDate = inputDate.toLocaleDateString(
                        "en-US",
                        options
                      );
                      return (
                        <Table.Row className="">
                          <Table.Cell className="text-center">{formattedDate}</Table.Cell>
                          <Table.Cell className="text-center">
                            {item.User.first_name} {item.User.last_name}
                          </Table.Cell>
                          <Table.Cell className="flex justify-center">
                            {" "}
                            {item.auto_delivery === 1 ? (
                              <FaCheckCircle color="#08447C" size="25px" />
                            ) : (
                              <FaRegCircle color="#08447C" size="25px" />
                            )}
                          </Table.Cell>
                          <Table.Cell className="px-4 py-4 text-blue-600 text-center">
                          {item.tracking  && ( 
                                <>
                              {(() => {
                                const provider = getShippingProvider(item.shipping_method);
                                if (provider === 'FedEx') {
                                  return (
                                    <Link
                                      href={`https://www.fedex.com/fedextrack/no-results-found?trknbr=${item.tracking}`}
                                      target="_blank"
                                    >
                                      Track Order
                                    </Link>
                                  );
                                } else if (provider === 'UPS') {
                                  return (
                                    <Link
                                      href={`https://www.ups.com/track?tracknum=${item.tracking}`}
                                      target="_blank"
                                    >
                                      Track Order
                                    </Link>
                                  );
                                } else {
                                  return item.tracking;
                                }
                              })()}
                              </>
                              )}
                          </Table.Cell>
                          <Table.Cell className="text-center">
                            {trimWS(item.jde_id)}
                          </Table.Cell>
                          <Table.Cell className="text-center">
                            {formatPrice.format(item.total)}
                          </Table.Cell>
                          <Table.Cell>
                            {item.status === 1 && (
                              <p className="text-center font-semibold p-2 text-blue-500 bg-blue-100 rounded-md">
                                Submitted
                              </p>
                            )}
                            {item.status === 2 && (
                              <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                In Progress
                              </p>
                            )}
                            {item.status === 3 && (
                              <p className="text-center font-semibold p-2 text-yellow-500 bg-yellow-100 rounded-md">
                                Shipped
                              </p>
                            )}
                            {item.status === 4 && (
                              <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md">
                                Delivered
                              </p>
                            )}
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              ) : (
                "No Results Found"
              )}
            </Accordion.Content>
          </Accordion.Panel>
          <Accordion.Panel id="invoices">
            <Accordion.Title className="text-gray-800">
              Invoices <span className=" text-gray-400">{invoices.length}</span>
            </Accordion.Title>

            <Accordion.Content>
              {invoices.length > 0 ? (
                <Table hoverable>
                  <Table.Head>
                    <Table.HeadCell className="text-center">
                      Date
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Customer
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Invoice Number
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Type
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Amount
                    </Table.HeadCell>
                    <Table.HeadCell className="text-center">
                      Status
                    </Table.HeadCell>
                  </Table.Head>
                  <Table.Body className="">
                    {invoices.map((item) => {
                      const inputDate = new Date(item.Order.date);
                      const options = {
                        year: "2-digit",
                        month: "numeric",
                        day: "numeric",
                      };
                      const formattedDate = inputDate.toLocaleDateString(
                        "en-US",
                        options
                      );
                      return (
                        <Table.Row className="">
                          <Table.Cell className="text-center">{formattedDate}</Table.Cell>
                          <Table.Cell className="text-center">
                            {item.Order.User.first_name}{" "}
                            {item.Order.User.last_name}
                          </Table.Cell>
                          <Table.Cell className="text-center">{item.invoice_num}</Table.Cell>
                          <Table.Cell className="">
                            {" "}
                            {item.type === "web" && <FaDesktop className="mx-auto"/>}
                            {item.type === "phone" && <FaPhoneAlt className="mx-auto"/>}
                          </Table.Cell>
                          <Table.Cell className="text-center">
                            {formatPrice.format(item.Order.total)}
                          </Table.Cell>
                          <Table.Cell>
                            {item.status === 1 && (
                              <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md ">
                                Paid
                              </p>
                            )}
                            {item.status === 2 && (
                              <p className="text-center font-semibold p-2 text-yellow-500 bg-yellow-100 rounded-md">
                                Due
                              </p>
                            )}
                            {item.status === 3 && (
                              <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                Past Due
                              </p>
                            )}
                            {item.status === 4 && (
                              <p className="text-center font-semibold p-2 text-blue-500 bg-blue-100 rounded-md ">
                                Pending
                              </p>
                            )}
                          </Table.Cell>
                        </Table.Row>
                      );
                    })}
                  </Table.Body>
                </Table>
              ) : (
                "No Results Found"
              )}
            </Accordion.Content>
          </Accordion.Panel>
        </Accordion>
      </Modal.Body>
    </>
  );
};

export default SearchModal;
