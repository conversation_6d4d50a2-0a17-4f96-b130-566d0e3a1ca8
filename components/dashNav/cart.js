import { TextInput, Badge } from "flowbite-react";
import axios from "axios";
import { useEffect, useState } from "react";
import { useCart } from "../../contexts/CartContext";
import { usePromotionsContext } from '../../contexts/PromotionsContext'; // Import the promotion context
import { useSession } from "next-auth/react";

function Cart({ data, onUpdate, cartAlert, setCartAlert, index }) {
  const { data: session, status } = useSession();
  const { promotions } = usePromotionsContext(); // Access promotions from context
  const { cart } = useCart();
  const [qty, setQty] = useState(cart[index].qty);
  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  function truncateProductName(str) {
    if (str.length > 50) {
      str = str.slice(0, 50).trim();
      str += "...";
    }

    return str;
  }

  const removeById = () => {
    axios({
      method: "delete",
      url: "/api/cart/removeItemById",
      data: {
        id: data.id,
      },
    }).then((response) => {
      if (response.status == 200) {
        // console.log("response", response.data);
        onUpdate("delete");
        // console.log("THIS IS DELETING");
        setCartAlert({
          color: "failure",
          show: true,
          message: `Product: ${truncateProductName(
            data.Product.name
          )} removed from cart.`,
        });
        setTimeout(() => {
          setCartAlert({
            message: "",
            color: "",
            show: false,
          });
        }, 2000);
      }
    });
  };

  const updateCartQty = (e) => {
    e.preventDefault();
    console.log("QTY Cart", data);
    console.log("qty prev", e.target.placeholder);
    console.log("qty", e.target.value);
    console.log("Index:", data.index);
    const index = data.index;
    const enteredQty = e.target.value;

    if (enteredQty < 0) {
      setQty(e.target.placeholder);
      setCartAlert({
        color: "failure",
        show: true,
        message: `Quantity cannot be less than 0`,
      });
      setTimeout(() => {
        setCartAlert({
          message: "",
          color: "",
          show: false,
        });
      }, 2000);
    } else if (enteredQty == 0) {
      axios({
        method: "delete",
        url: "/api/cart/removeItemById",
        data: {
          id: data.id,
        },
      }).then((response) => {
        if (response.status == 200) {
          // console.log("response", response.data);
          onUpdate("delete");
          // console.log("THIS IS DELETING");
          setCartAlert({
            color: "failure",
            show: true,
            message: `${truncateProductName(
              data.Product.name
            )} removed from cart.`,
          });
          setTimeout(() => {
            setCartAlert({
              message: "",
              color: "",
              show: false,
            });
          }, 2000);
        }
      });
    } else if (enteredQty > 0) {
      axios({
        method: "POST",
        url: "/api/cart/updateQty",
        data: {
          id: data.id,
          qty: qty,
          account_jde: session.user.Account.jde_id,
          promotions:promotions
        },
      }).then((response) => {
        if (response.status == 200) {
          // console.log("response", response.data);
          onUpdate("update");
          // console.log("THIS IS UPDATING");
          setCartAlert({
            color: "success",
            show: true,
            message: `${truncateProductName(
              data.Product.name
            )} quantity updated.`,
          });
          setTimeout(() => {
            setCartAlert({
              message: "",
              color: "",
              show: false,
            });
          }, 2000);

          // console.log("Getting Cart?", getCart);
        }
      });
    }
  };

  useEffect(() => {
    setQty(cart[index].qty);
  }, [cart]);

  return (
    <div>
      <a
        draggable="false"
        href="#"
        className="flex items-center gap-4 py-3 px-4  hover:bg-gray-100 dark:hover:bg-gray-600 dark:border-gray-600"
      >
        <div className="flex-shrink-0">
          <img
            className="w-11 h-11 sqaure"
            src={
              data.Product.ProductImage && data.Product.ProductImage.length < 1
                ? "/no_image.jpeg"
                : `${
                    process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                    "/oasis-product-images/"
                  }${data.Product && data.Product.ProductImage[0].path}_l.${
                    data.Product && data.Product.ProductImage[0].extension
                  }`
            }
            alt=""
          />
      
        </div>
        <div className="flex-grow">
          <div className="text-black font-normal text-sm mb-1.5">
            {data.Product && data.Product.name}
          </div>
          <div className="inline-flex items-center ">
            <div className="text-xs font-medium text-gray-400 pr-4">
              {data.Product && data.Product.sku}
            </div>
            {data.Product && data.Product.stock > 0 ? (
              <Badge color="success" size="xs">
                In Stock
              </Badge>
            ) : (
              <Badge color="failure" size="xs">
                Out of Stock
              </Badge>
            )}
          </div>
        </div>
        <div className="flex-shrink-0">
        <div className="flex items-center gap-6">
          <div className="flex-none">
            <TextInput
              type="number"
              className="inline-block align-top bg-[#F8F8F8] w-[75px] rounded-md mt-2"
              placeholder={qty}
              value={qty}
              onChange={(e) => setQty(e.target.value)}
              onBlur={(e) => updateCartQty(e)}
            />
          </div>
          <div className="flex-1 w-20 text-right">
            {data.Product && formatPrice.format(data.price)}
          </div>
          <div className="">
            <button onClick={removeById}>
              <img src="/trashcan.png" alt="delete"></img>
            </button>
          </div>
        </div>
        </div>
      </a>
      <hr className="h-px bg-gray-200 border-0 -mx-6" />
    </div>
  );
}

export default Cart;
