const stateNames = [
    { value: "AL", label: "AL" },
    { value: "AK", label: "AK" },
    { value: "AZ", label: "AZ" },
    { value: "AR", label: "AR" },
    { value: "CA", label: "CA" },
    { value: "CO", label: "CO" },
    { value: "CT", label: "CT" },
    { value: "DC", label: "DC" },
    { value: "DE", label: "DE" },
    { value: "FL", label: "FL" },
    { value: "GA", label: "GA" },
    { value: "HI", label: "HI" },
    { value: "ID", label: "ID" },
    { value: "IL", label: "IL" },
    { value: "IN", label: "IN" },
    { value: "IA", label: "IA" },
    { value: "KS", label: "KS" },
    { value: "KY", label: "KY" },
    { value: "LA", label: "LA" },
    { value: "ME", label: "ME" },
    { value: "MD", label: "MD" },
    { value: "MA", label: "MA" },
    { value: "MI", label: "MI" },
    { value: "MN", label: "MN" },
    { value: "MS", label: "MS" },
    { value: "MO", label: "MO" },
    { value: "MT", label: "MT" },
    { value: "NE", label: "NE" },
    { value: "NV", label: "NV" },
    { value: "NH", label: "NH" },
    { value: "NJ", label: "NJ" },
    { value: "NM", label: "NM" },
    { value: "NY", label: "NY" },
    { value: "NC", label: "NC" },
    { value: "ND", label: "ND" },
    { value: "OH", label: "OH" },
    { value: "OK", label: "OK" },
    { value: "OR", label: "OR" },
    { value: "PA", label: "PA" },
    { value: "RI", label: "RI" },
    { value: "SC", label: "SC" },
    { value: "SD", label: "SD" },
    { value: "TN", label: "TN" },
    { value: "TX", label: "TX" },
    { value: "UT", label: "UT" },
    { value: "VT", label: "VT" },
    { value: "VA", label: "VA" },
    { value: "WA", label: "WA" },
    { value: "WV", label: "WV" },
    { value: "WI", label: "WI" },
    { value: "WY", label: "WY" }
];
  
  export { stateNames };


  const countries = [
    { label: "Argentina", value: "AR" },
    { label: "Australia", value: "AU" },
    { label: "Brazil", value: "BR" },
    { label: "Canada", value: "CA" },
    { label: "China", value: "CN" },
    { label: "France", value: "FR" },
    { label: "Germany", value: "DE" },
    { label: "India", value: "IN" },
    { label: "Indonesia", value: "ID" },
    { label: "Italy", value: "IT" },
    { label: "Japan", value: "JP" },
    { label: "Mexico", value: "MX" },
    { label: "Russia", value: "RU" },
    { label: "Saudi Arabia", value: "SA" },
    { label: "South Africa", value: "ZA" },
    { label: "South Korea", value: "KR" },
    { label: "Turkey", value: "TR" },
    { label: "United Kingdom", value: "UK" },
    { label: "United States", value: "US" },
  ];

  export { countries }

  export const createPermissionChecker = (permissions, group_id) => {
    return (section, action) => {
      if (group_id === 1) return true;
      if (!permissions || !permissions[section]) return false;
      return permissions[section].includes(action);
    };
  };
  
  
  