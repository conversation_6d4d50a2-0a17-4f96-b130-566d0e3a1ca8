import { <PERSON><PERSON>, <PERSON><PERSON> } from "flowbite-react";
import { useStepperContext } from "../../contexts/StepperContext";
import { useState, useEffect } from "react";

export default function StepperControl({
  handleClick,
  currentStep,
  steps,
  registerUser,
  loading,
}) {
  const { userData, setUserData } = useStepperContext();
  const [submitError, setSubmitError] = useState("");
  // console.log(userData);

  return (
    <>
      {loading.validating && (
        <div className="text-center">
          <div className="m-auto text-center pb-2">Validating email...</div>
          <Spinner aria-label="Default status example" size="md" />
        </div>
      )}
      {loading.registering && (
        <div className="text-center">
          <div className="m-auto text-center pb-2">Registering Account...</div>
          <Spinner aria-label="Default status example" size="md" />
        </div>
      )}

      {loading.syncing && (
        <div className="text-center">
          <div className="m-auto text-center pb-2">Retrieving Account#...</div>
          <Spinner aria-label="Default status example" size="md" />
        </div>
      )}
      {loading.finalizing && (
        <div className="text-center">
          <div className="m-auto text-center pb-2">
            Finishing Account Setup...
          </div>
          <Spinner aria-label="Default status example" size="md" />
        </div>
      )}
      {submitError && (
        <div className="text-center text-red-600 ">{submitError}</div>
      )}
      <div className="container mt-8 mb-8 mx-auto w-3/5 flex justify-around">
        <Button
          pill={true}
          onClick={() => handleClick()}
          color="gray"
          className={`w-40 text-primary border-primary px-8 focus:ring-0 focus:ring-transparent hover:text-primary focus:text-primary ${
            currentStep === 1 ? " cursor-not-allowed opacity-50 " : ""
          }`}
        >
          Previous
        </Button>

        <Button
          type="submit"
          pill={true}
          onClick={
            currentStep === steps.length
              ? () => registerUser(userData)
              : () => handleClick("next")
          }
          className=" w-40 bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
        >
          {currentStep === steps.length ? "Submit" : "Next"}
        </Button>
      </div>
    </>
  );
}
