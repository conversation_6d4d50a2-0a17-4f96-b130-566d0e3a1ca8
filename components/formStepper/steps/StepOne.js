import { useStepperContext } from "../../../contexts/StepperContext";
import { Label, TextInput, Checkbox, Button } from "flowbite-react";
import Select from "react-select";
import { allStates, stateNames } from "../../utils";
import React, { useState, useEffect } from "react";

export default function StepOne({}) {
  const { userData, setUserData } = useStepperContext();

  const handleChange = (e) => {
    const { dataset, id, value } = e.target;
    setUserData({
      ...userData,
      [dataset.id]: { ...userData[dataset.id], [id]: value },
    });
  };

  const handleChangeSelect = (selectedOption) => {
    setUserData({
      ...userData,
      account: {
        ...userData.account,
        state: selectedOption.value,
      },
    });
  };

  const stateNamesWithInfo = stateNames.map((state) => ({
    ...state,
    info: { dataset: { id: "account" }, id: "State" },
  }));
  // setRestore();

  useEffect(() => {}, []);

  // console.log("register Data", userData);

  return (
    <div className="flex justify-center">
      <form className="grid grid-cols-3  gap-4 w-3/5 mx-auto">
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="firstName"
              value="First Name"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="firstName"
            name="firstName"
            type="text"
            placeholder="First Name"
            data-id="user"
            required={true}
            onChange={handleChange}
            value={userData["user"]["firstName"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
            onInvalid={e => e.target.setCustomValidity('Your custom message')}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="lastName"
              value="Last Name"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="lastName"
            type="text"
            placeholder="Last Name"
            data-id="user"
            required={true}
            onChange={handleChange}
            value={userData["user"]["lastName"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="jobTitle"
              value="Job Title"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="jobTitle"
            type="text"
            placeholder="Job Title"
            data-id="user"
            required={true}
            onChange={handleChange}
            value={userData["user"]["jobTitle"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="businessName"
              value="Business Name"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="business_name"
            type="text"
            placeholder="Business Name"
            data-id="account"
            required={true}
            onChange={handleChange}
            value={userData["account"]["business_name"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="website"
              value="Website"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="website"
            type="text"
            placeholder="Website"
            data-id="account"
            // required={true}
            onChange={handleChange}
            value={userData["account"]["website"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="phone"
              value="Phone"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="phone"
            type="text"
            placeholder="Phone"
            data-id="user"
            required={true}
            onChange={handleChange}
            value={userData["user"]["phone"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label htmlFor="fax" value="Fax" className="text-xs font-normal" />
          </div>
          <TextInput
            id="fax"
            type="text"
            placeholder="Fax"
            data-id="account"
            // required={true}
            onChange={handleChange}
            value={userData["account"]["fax"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="address_l1"
              value="Street Address"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="address_l1"
            type="text"
            placeholder="Street Address"
            data-id="account"
            required={true}
            onChange={handleChange}
            value={userData["account"]["address_l1"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="address_l2"
              value="Address Line 2"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="address_l2"
            type="text"
            placeholder="Address Line 2"
            data-id="account"
            // required={true}
            onChange={handleChange}
            value={userData["account"]["address_l2"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="city"
              value="City"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="city"
            type="text"
            placeholder="City"
            data-id="account"
            required={true}
            onChange={handleChange}
            value={userData["account"]["city"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label htmlFor="zip" value="Zip" className="text-xs font-normal" />
          </div>
          <TextInput
            id="zip"
            type="text"
            placeholder="Zip"
            data-id="account"
            required={true}
            onChange={handleChange}
            value={userData["account"]["zip"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="state"
              value="State"
              className="text-xs font-normal"
            />
          </div>
          <Select
            options={stateNamesWithInfo}
            id="State"
            required={true}
            placeholder="State"
            data-id="account"
            className="custom-select"
            onChange={handleChangeSelect}
            value={
              userData.account.state
                ? {
                    label: userData.account.state,
                    value: userData.account.state,
                  }
                : stateNames.find(
                    (option) => option.value === userData.account.stateNames
                  )
            }
          />
          <div className="text-xs font-normal text-[#787E8B] pt-2">
            * Required
          </div>
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="email"
              value="Email/Username"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="email"
            type="email"
            placeholder="Email/Username"
            data-id="user"
            required={true}
            shadow={true}
            onChange={handleChange}
            value={userData["user"]["email"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="password"
              value="Password"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="password"
            type="password"
            placeholder="Password"
            data-id="user"
            required={true}
            pattern="[a-z0-9]{1,8}"
            shadow={true}
            onChange={handleChange}
            value={userData["user"]["password"]}
            className="custom-input"
            helperText={
              <span className="text-xs">
                * Required, must contain 8 characters with, 1 uppercase, 1
                lowercase and 1 number
              </span>
            }
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="confirm_password"
              value="Confirm Password"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="confirm_password"
            type="password"
            placeholder="Confirm Password"
            data-id="user"
            required={true}
            shadow={true}
            onChange={handleChange}
            value={userData["user"]["confirm_password"]}
            className="custom-input"
            helperText={<span className="text-xs">* Required</span>}
          />
        </div>
        {/* <div className="flex justify-center col-start-2">
          <span className="text-[10px]">
            *Password must contain 8 characters with, 1 uppercase, 1 lowercase
            and 1 number
          </span>
        </div> */}
      </form>
    </div>
  );
}
