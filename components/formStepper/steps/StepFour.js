import { useStepperContext } from "../../../contexts/StepperContext";
import {
  Label,
  TextInput,
  Checkbox,
  Button,
  Accordion,
  Radio,
  Modal
} from "flowbite-react";
import React, { useState, useEffect } from "react";
import Select from "react-select";
import { AiFillPlusCircle } from "react-icons/ai";
import { data } from "autoprefixer";
import TermsOfService from '../../TermsOfService'; // Import your new component


export default function StepFour({ }) {
  const { userData, setUserData } = useStepperContext();
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { dataset, id, value } = e.target;

    if (id == 'accept'){
      setUserData({
        ...userData,
        [dataset.id]: { ...userData[dataset.id], [id]: (e.target.checked ? 1: 0) },
      });
    }else{
      setUserData({
        ...userData,
        [dataset.id]: { ...userData[dataset.id], [id]: value },
      });
    }
  };

  const [privatePractice, setPrivatePractice] = useState({
    yes: false,
    no: false,
  });

  const [dryEye, setDryEye] = useState({
    yes: false,
    no: false,
  });

  const [punctalCheck, setPunctalCheck] = useState({
    yes: false,
    no: false,
  });

  const [openModal, setOpenModal] = useState(false);


  const handleCheckboxInput = (e) => {
    const { id, checked, dataset } = e.target;
    console.log(dataset.id);
    if (id == "private_practice_yes") {
      setPrivatePractice({ yes: checked, no: false });
    } else if (id == "private_practice_no") {
      setPrivatePractice({ yes: false, no: checked });
    }
    console.log("Checked", checked)
    setUserData({
      ...userData,
      [dataset.id]: {
        ...userData[dataset.id],
        ["private_practice"]: Number(checked),
      },
    });
  };

  const handleCheckboxInputDryEye = (e) => {
    const { id, checked, dataset } = e.target;
    console.log(dataset.id);
    if (id == "treat_dry_eye_yes") {
      setDryEye({ yes: checked, no: false });
    } else if (id == "treat_dry_eye_no") {
      setDryEye({ yes: false, no: checked });
    }
    setUserData({
      ...userData,
      [dataset.id]: { ...userData[dataset.id], ["treat_dry_eye"]: checked * 1 },
    });
  };

  const handleCheckboxInputPunctal = (e) => {
    const { id, checked, dataset } = e.target;
    console.log(dataset.id);
    if (id == "punctal_plugs_yes") {
      setPunctalCheck({ yes: checked, no: false });
    } else if (id == "punctal_plugs_no") {
      setPunctalCheck({ yes: false, no: checked });
    }
    setUserData({
      ...userData,
      [dataset.id]: { ...userData[dataset.id], ["punctal_plugs"]: checked * 1 },
    });
  };

  useEffect(() => {
    setUserData({
      ...userData,
      account: {
        ...userData.account,
        accept: false,
      }
    })
  }, []);

  console.log(userData);

  return (
    <div className="flex justify-center">
      <form className="grid grid-cols-3  gap-4 w-3/5 mx-auto">
        <div className="flex gap-2 flex-col">
          <div className="mb-0.5 block">
            <Label htmlFor="private_practice" className="text-xs font-normal">
              Private Practice?
            </Label>
          </div>
          <div className="flex gap-2 flex-row">
            <Radio
              id="private_practice_yes"
              checked={privatePractice.yes}
              data-id="account"
              onClick={handleCheckboxInput}
              className="custom-radio"
            />
            <span className="text-xs font-normal mr-3">Yes</span>
            <Radio
              id="private_practice_no"
              checked={privatePractice.no}
              data-id="account"
              onClick={handleCheckboxInput}
              className="custom-radio"
            />
            <span className="text-xs font-normal">No</span>
          </div>
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="num_locations"
              value="Number of Locations"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="num_locations"
            type="text"
            placeholder="Number of Locations"
            required={true}
            data-id="account"
            onChange={handleChange}
            value={userData["account"]["num_locations"]}
            className={
              !errors.num_locations ? "custom-input" : "custom-input-failure"
            }
            helperText={<div className="text-xs">* Required</div>}
          />
        </div>
        <div className="col-start-1">
          <div className="mb-0.5 block">
            <Label
              htmlFor="primary_specialty"
              value="What is your care teams primary specialty?"
              className="text-xs font-normal"
            />
          </div>
          {/* <Select  
            id="primary_specialty" 
            required={true}
            placeholder="Primary Specialty"
            data-id="account" 
            className="custom-select"
            /> */}
          <TextInput
            id="primary_specialty"
            type="text"
            placeholder="Primary Specialty"
            required={true}
            data-id="account"
            onChange={handleChange}
            value={userData["account"]["primary_specialty"]}
            className={
              !errors.primary_specialty ? "custom-input" : "custom-input-failure"
            }
            helperText={<div className="text-xs">* Required</div>}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="secondary_specialty"
              value="What is your care teams secondary specialty?"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="secondary_specialty"
            type="text"
            placeholder="Secondary Specialty"
            required={true}
            data-id="account"
            onChange={handleChange}
            value={userData["account"]["secondary_specialty"]}
            className="custom-input"
          />
        </div>
        <div className="col-start-1">
          <div className="mb-0.5 block">
            <Label
              htmlFor="manager_firstName"
              value="Office Manager First Name"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="manager_firstName"
            type="text"
            placeholder="Manager First Name"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["manager_firstName"]}
            className="custom-input"
          />
        </div>

        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="manager_lastName"
              value="Office Manager Last Name"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="manager_lastName"
            type="text"
            placeholder="Manager Last Name"
            data-id="contacts"
            // required={true}
            onChange={handleChange}
            value={userData["contacts"]["manager_lastName"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="manager_email"
              value="Office Manager Email"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="manager_email"
            type="text"
            placeholder="Manager Email"
            data-id="contacts"
            // required={true}
            onChange={handleChange}
            value={userData["contacts"]["manager_email"]}
            className="custom-input"
          />
        </div>
        <div className="col-span-3">
          <Accordion
            alwaysOpen={true}
            arrowIcon={AiFillPlusCircle}
            className="border-0 divide-y-0"
          >
            <Accordion.Panel>
              <Accordion.Title className="text-primary bg-white hover:bg-inherit focus:ring-0 flex flex-row-reverse gap-2 [justify-content:start] px-0 py-2 text-xs">
                Add Optometrist
              </Accordion.Title>
              <Accordion.Content className="border-y-0 acc-content grid-cols-3 gap-4 px-0 py-3">
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="optometrist_firstName"
                      value="First Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="optometrist_firstName"
                    type="text"
                    placeholder="First Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["optometrist_firstName"]}
                    className="custom-input"
                  />
                </div>

                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="optometrist_lastName"
                      value="Last Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="optometrist_lastName"
                    type="text"
                    placeholder="Last Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["optometrist_lastName"]}
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="optometrist_email"
                      value="Email"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="optometrist_email"
                    type="text"
                    placeholder="Email"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["optometrist_email"]}
                    className="custom-input"
                  />
                </div>
              </Accordion.Content>
            </Accordion.Panel>
            <Accordion.Panel>
              <Accordion.Title className="text-primary bg-white hover:bg-inherit focus:ring-0 flex flex-row-reverse gap-2 [justify-content:start] px-0 py-2 text-xs">
                Add Ophthalmologists
              </Accordion.Title>
              <Accordion.Content className="border-y-0 acc-content grid-cols-3 gap-4 px-0 py-3">
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="ophtha_firstName"
                      value="First Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="ophtha_firstName"
                    type="text"
                    placeholder="First Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["ophtha_firstName"]}
                    className="custom-input"
                  />
                </div>

                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="ophtha_lastName"
                      value="Last Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="ophtha_lastName"
                    type="text"
                    placeholder="Last Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["ophtha_lastName"]}
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="ophtha_email"
                      value="Email"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="ophtha_email"
                    type="text"
                    placeholder="Email"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["ophtha_email"]}
                    className="custom-input"
                  />
                </div>
              </Accordion.Content>
            </Accordion.Panel>
            <Accordion.Panel>
              <Accordion.Title className="text-primary bg-white hover:bg-inherit focus:ring-0 flex flex-row-reverse gap-2 [justify-content:start] px-0 py-2 text-xs">
                Lead Surgical Tech
              </Accordion.Title>
              <Accordion.Content className="border-y-0 acc-content grid-cols-3 gap-4 px-0 py-3">
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="lead_tech_firstName"
                      value="First Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="lead_tech_firstName"
                    type="text"
                    placeholder="First Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["lead_tech_firstName"]}
                    className="custom-input"
                  />
                </div>

                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="lead_tech_lastName"
                      value="Last Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="lead_tech_lastName"
                    type="text"
                    placeholder="Last Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["lead_tech_lastName"]}
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="lead_tech_email"
                      value="Email"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="lead_tech_email"
                    type="text"
                    placeholder="Email"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["lead_tech_email"]}
                    className="custom-input"
                  />
                </div>
              </Accordion.Content>
            </Accordion.Panel>
            <Accordion.Panel>
              <Accordion.Title className="text-primary bg-white hover:bg-inherit focus:ring-0 flex flex-row-reverse gap-2 [justify-content:start] px-0 py-2 text-xs">
                Front Office/Clinic Coordinators
              </Accordion.Title>
              <Accordion.Content className="border-y-0 acc-content grid-cols-3 gap-4 px-0 py-3">
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="front_office_firstName"
                      value="First Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="front_office_firstName"
                    type="text"
                    placeholder="First Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["front_office_firstName"]}
                    className="custom-input"
                  />
                </div>

                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="front_office_lastName"
                      value="Last Name"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="front_office_lastName"
                    type="text"
                    placeholder="Last Name"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["front_office_lastName"]}
                    className="custom-input"
                  />
                </div>
                <div>
                  <div className="mb-0.5 block">
                    <Label
                      htmlFor="front_office_email"
                      value="Email"
                      className="text-xs font-normal"
                    />
                  </div>
                  <TextInput
                    id="front_office_email"
                    type="text"
                    placeholder="Email"
                    data-id="contacts"
                    // required={true}
                    onChange={handleChange}
                    value={userData["contacts"]["front_office_email"]}
                    className="custom-input"
                  />
                </div>
              </Accordion.Content>
            </Accordion.Panel>
          </Accordion>
        </div>

        <div className="flex gap-2 flex-col col-start-1">
          <div className="mb-0.5 block">
            <Label htmlFor="treat_dry_eye" className="text-xs font-normal">
              Does your practice treat dry eye?
            </Label>
          </div>
          <div className="flex gap-2 flex-row">
            <Radio
              id="treat_dry_eye_yes"
              checked={dryEye.yes}
              data-id="account"
              onClick={handleCheckboxInputDryEye}
              className="custom-radio"
            />
            <span className="text-xs font-normal mr-3">Yes</span>
            <Radio
              id="treat_dry_eye_no"
              checked={dryEye.no}
              data-id="account"
              onClick={handleCheckboxInputDryEye}
              className="custom-radio"
              
            />
            <span className="text-xs font-normal">No</span>
          </div>
        </div>
        <div className="flex gap-2 flex-col col-span-2">
          <div className="mb-0.5 block">
            <Label htmlFor="punctal_plugs" className="text-xs font-normal">
              If yes are puntal plugs a treatment modality?
            </Label>
          </div>
          <div className="flex gap-2 flex-row">
            <Radio
              id="punctal_plugs_yes"
              name="punctal_plugs"
              data-id="account"
              checked={punctalCheck.yes}
              onClick={handleCheckboxInputPunctal}
              className="custom-radio"
            />
            <Label htmlFor="punctal_plugs_yes">Yes</Label>
            <Radio
              id="punctal_plugs_no"
              name="punctal_plugs"
              data-id="account"
              checked={punctalCheck.no}
              onClick={handleCheckboxInputPunctal}
              className="custom-radio"
            />
            <Label htmlFor="punctal_plugs_no">No</Label>
            {/* <Checkbox
        id="punctal_plugs_yes"
        defaultChecked={false}
        data-id="account"
        /><span className="text-xs font-normal mr-3">Yes</span>
        <Checkbox
        id="punctal_plugs_no"
        defaultChecked={false}
        data-id="account"
        /><span className="text-xs font-normal">No</span> */}
          </div>
        </div>

        <div className="flex col-start-2  items-center gap-2">
          <Checkbox id="accept" isChecked={userData.account.accept} data-id="account" onChange={handleChange} className="custom-radio"/>
          <Label htmlFor="accept" className="text-xs font-normal">
            I accept the{" "}
            <a
              href="#"
              onClick={() => setOpenModal(true)}
              className="text-blue-600 hover:underline dark:text-blue-500"
            >
              Terms of Service
            </a>
          </Label>
        </div>
      </form>
      <Modal dismissible show={openModal} size="6xl" onClose={() => setOpenModal(false)}>
        <Modal.Header>Terms of Service</Modal.Header>
        <Modal.Body>
          <TermsOfService />

        </Modal.Body>
      </Modal>
    </div>
  );
}
