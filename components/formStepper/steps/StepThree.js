import { useStepperContext } from "../../../contexts/StepperContext";
import { Label, TextInput, Checkbox, Button, FileInput } from "flowbite-react";
import Select from "react-select";
import { FiUpload } from "react-icons/fi";
import React, { useState, useEffect } from "react";

export default function StepThree({ partnerType }) {
  const { userData, setUserData } = useStepperContext();
  const [errors, setErrors] = useState({});
  const [shipAcctRequired, setShipAcctRequired] = useState(false);


  const handleChange = (e) => {
    const { value, info } = e;
    console.log(info.dataset.id, info.id, value);
    setUserData({
      ...userData,
      [info.dataset.id]: { ...userData[info.dataset.id], [info.id]: value },
    });
  };

  const handleChangeText = (e) => {
    const { dataset, id, value } = e.target;
    setUserData({
      ...userData,
      [dataset.id]: { ...userData[dataset.id], [id]: value },
    });
  };

  const handleChangeSelectPartner = (selectedOption) => {
    setUserData({
      ...userData,
      ["account"]: {
        ...userData["account"],
        partner_type: selectedOption.value,
        type: selectedOption.label,
        partner_type_code: selectedOption.code,
      },
    });
    console.log(userData);
  };

  const handleChangeFileInput = (e) => {
    const fileUploaded = e.target.files[0];
    setUserData({
      ...userData,
      account: {
        ...userData.account,
        uploadedFile: fileUploaded,
      },
    });
  };

  useEffect(() => {}, []);

  const partner_type_value = { dataset: { id: "account" }, id: "partner_type" };

  const invoice_preferred_by_value = {
    dataset: { id: "account" },
    id: "invoice_preferred_by",
  };
  const tax_exempt_value = {
    dataset: { id: "account" },
    id: "tax_exempt_or_resale",
  };

  const pref_freight_delivery_value = {
    dataset: { id: "account" },
    id: "pref_freight_delivery",
  };
  const freight_company_value = {
    dataset: { id: "account" },
    id: "freight_company",
  };

  const invoice_preferred_by = [
    { value: "email", info: invoice_preferred_by_value, label: "Email" },
    { value: "mail", info: invoice_preferred_by_value, label: "US Mail" },
  ];

  const freight_company = [
    { value: "fedex", info: freight_company_value, label: "FedEx" },
    { value: "ups", info: freight_company_value, label: "UPS" },
  ];

  const pref_freight_delivery = [
    { value: "ground", info: pref_freight_delivery_value, label: "Ground" },
    { value: "two_day", info: pref_freight_delivery_value, label: "2-day" },
    { value: "three_day", info: pref_freight_delivery_value, label: "3-day" },
  ];

  const exempt_resale_cert = [
    { value: 1, info: tax_exempt_value, label: "Yes" },
    { value: 0, info: tax_exempt_value, label: "No" },
  ];

  console.log("partnerType", partnerType);
  return (
    <div className="flex justify-center">
      <form className="grid grid-cols-3  gap-4 w-3/5 mx-auto">
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="partner_type"
              value="Partner Type"
              className="text-xs font-normal"
            />
          </div>
          <Select
            options={partnerType}
            id="partner_type"
            required={true}
            placeholder="Choose One"
            data-id="account"
            className={
              !errors.partner_type ? "custom-select" : "custom-select-failure"
            }
            onChange={handleChangeSelectPartner}
            value={partnerType.find(
              (option) => option.value === userData.account.partner_type
            )}
          />
          <div className="text-xs font-normal text-[#787E8B] pt-2">
            * Required
          </div>
        </div>
        <div className="col-start-1">
          <div className="mb-0.5 block">
            <Label
              htmlFor="invoice_preferred_by"
              value="Invoice Preferred by"
              className="text-xs font-normal"
            />
          </div>
          <Select
            options={invoice_preferred_by}
            id="invoice_preferred_by"
            required={true}
            placeholder="Choose One"
            data-id="account"
            className="custom-select"
            onChange={handleChange}
            value={invoice_preferred_by.find(
              (option) => option.value === userData.account.invoice_preferred_by
            )} // Display the label based on the userData value
          />
        </div>
        <div>
          <div className="mb-0.5 block col-span-2 items-center">
            <Label
              htmlFor="tax_exempt_or_resale"
              value="Does your office have a tax exempt or resale certificate?"
              className="text-xs font-normal"
            />
          </div>
          <Select
            options={exempt_resale_cert}
            id="tax_exempt_or_resale"
            required={true}
            placeholder="Choose One"
            data-id="account"
            className="custom-select mt-6"
            onChange={handleChange}
            value={exempt_resale_cert.find(
              (option) => option.value === userData.account.tax_exempt_or_resale
            )}
          />
        </div>
        <div id="fileUpload" className="flex flex-col items-start justify-end">
        {
            userData["account"]["uploadedFile"] ? (
          <span className="text-xs mb-2 text-primary-light">{userData["account"]["uploadedFile"]["name"]}</span>
            ) : null }
          <div className="mb-2 flex ">
            <span>
              {" "}
              <FiUpload className="text-s text-primary" />{" "}
            </span>
            <Label
              htmlFor="exempt_resale_cert"
              value="Upload Tax Certificate"
              className="text-xs text-primary ml-2"
            />
            <FileInput
              id="exempt_resale_cert"
              data-id="account"
              className="opacity-0 h-0 w-0 fixed"
              onChange={handleChangeFileInput}
            />
          </div>
        </div>
        <div className="flex justify-center col-start-2 col-span-2">
          <p className="text-[10px]">
            *If yes please upload tax exempt/ resale documents using the link
            above. Call customer service with
            questions 844-820-8940
          </p>
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="pref_freight_delivery"
              value="Preferred Freight delivery"
              className="text-xs font-normal"
            />
          </div>
          <Select
            options={pref_freight_delivery}
            id="pref_freight_delivery"
            required={true}
            placeholder="Choose One"
            data-id="account"
            className="custom-select"
            onChange={handleChange}
            value={pref_freight_delivery.find(
              (option) =>
                option.value === userData.account.pref_freight_delivery
            )}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="freight_company"
              value="Freight Company"
              className="text-xs font-normal"
            />
          </div>
          <Select
            options={freight_company}
            id="freight_company"
            required={true}
            placeholder="Choose One"
            data-id="account"
            className="custom-select"
            onChange={handleChange}
            value={freight_company.find(
              (option) => option.value === userData.account.freight_company
            )}
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="shipping_acct_num"
              value="Shipper Account Number"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="shipping_acct_num"
            type="text"
            placeholder="Shipper Account Number"
            data-id="account"
            // required={true}
            onChange={handleChangeText}
            value={userData["account"]["shipping_acct_num"]}
            className="custom-input"
          />
          {
            userData.account.freight_company === "ups" ? (
              <div className="text-xs font-normal text-[#787E8B] pt-2">
                * Required for UPS - Please enter a valid UPS shipper account number.  Final shipping costs will be determined by UPS and charged to this UPS account.
              </div>
            ) : null
          }

        </div>
      </form>
    </div>
  );
}
