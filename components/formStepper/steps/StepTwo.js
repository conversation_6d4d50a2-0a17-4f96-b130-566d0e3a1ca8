import { useStepperContext } from "../../../contexts/StepperContext";
import { Label, TextInput, Checkbox, Button } from "flowbite-react";

export default function StepTwo() {
  const { userData, setUserData } = useStepperContext();

  const handleChange = (e) => {
    const { dataset, id, value } = e.target;
    console.log(e.target.checked);
    setUserData({
      ...userData,
      [dataset.id]: { ...userData[dataset.id], [id]: value },
    });
  };

  const handleChangeInput = (e) => {
    const { dataset, id, checked } = e.target;
    setUserData({
      ...userData,
      [dataset.id]: { ...userData[dataset.id], [id]: (checked ? 1 : 0) },
    });
  };
  return (
    <div className="flex justify-center">
      <form className="grid grid-cols-3  gap-4 w-3/5 mx-auto">
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="hear_of_oasis"
              value="How did you hear of oasis Products?"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="hear_of_oasis"
            type="text"
            placeholder="Answer Here"
            data-id="account"
            required={true}
            onChange={handleChange}
            value={userData["account"]["hear_of_oasis"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="purchasing_firstName"
              value="Primary Purchasing Contact:"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="purchasing_firstName"
            type="text"
            placeholder="First Name"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["purchasing_firstName"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="purchasing_lastName"
              value="Primary Purchasing Contact:"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="purchasing_lastName"
            type="text"
            placeholder="Last Name"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["purchasing_lastName"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="purchasing_phone"
              value="Phone Number"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="purchasing_phone"
            type="text"
            placeholder="(123)123-4567"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["purchasing_phone"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="purchasing_email"
              value="Email"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="purchasing_email"
            type="text"
            placeholder="<EMAIL>"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["purchasing_email"]}
            className="custom-input"
          />
        </div>

        <div className="col-start-1">
          <div className="mb-0.5 block col-start-1">
            <Label
              htmlFor="accounting_firstName"
              value="Primary Accounting Contact:"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="accounting_firstName"
            type="text"
            placeholder="First Name"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["accounting_firstName"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="accounting_lastName"
              value="Primary Accounting Contact:"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="accounting_lastName"
            type="text"
            placeholder="Last Name"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["accounting_lastName"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="accounting_phone"
              value="Phone Number"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="accounting_phone"
            type="text"
            placeholder="(123)123-4567"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["accounting_phone"]}
            className="custom-input"
          />
        </div>
        <div>
          <div className="mb-0.5 block">
            <Label
              htmlFor="accounting_email"
              value="Email"
              className="text-xs font-normal"
            />
          </div>
          <TextInput
            id="accounting_email"
            type="text"
            placeholder="<EMAIL>"
            data-id="contacts"
            required={true}
            onChange={handleChange}
            value={userData["contacts"]["accounting_email"]}
            className="custom-input"
          />
        </div>

        <div className="flex col-start-1 col-span-2 items-center gap-2">
          <Checkbox
            id="email_marketing_on"
            defaultChecked={false}
            data-id="account"
            className="custom-radio"
            onChange={handleChangeInput}
            value={userData["account"]["email_marketing_on"]}
          />
          <Label htmlFor="email_marketing_on" className="text-xs font-normal">
            Sign me up for important Oasis email announcements.
          </Label>
        </div>
      </form>
    </div>
  );
}
