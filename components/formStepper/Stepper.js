import React, { useState, useEffect, useRef } from "react";

const Stepper = ({ steps, currentStep, onStepClick }) => {
  const [newStep, setNewStep] = useState([]);
  const stepsRef = useRef();

  const updateStep = (stepNumber, steps) => {
    const newSteps = [...steps];
    let count = 0;
    while (count < newSteps.length) {
      //current step
      if (count === stepNumber) {
        newSteps[count] = {
          ...newSteps[count],
          highlighted: true,
          selected: true,
          completed: false,
        };
        count++;
      }

      //step completed
      else if (count < stepNumber) {
        newSteps[count] = {
          ...newSteps[count],
          highlighted: false,
          selected: true,
          completed: true,
        };
        count++;
      }
      //step pending
      else {
        newSteps[count] = {
          ...newSteps[count],
          highlighted: false,
          selected: false,
          completed: false,
        };
        count++;
      }
    }

    return newSteps;
  };

  useEffect(() => {
    const stepsState = steps.map((step, index) =>
      Object.assign(
        {},
        {
          description: step,
          completed: false,
          highlighted: index === 0 ? true : false,
          selected: index === 0 ? true : false,
        }
      )
    );

    stepsRef.current = stepsState;
    const current = updateStep(currentStep - 1, stepsRef.current);
    setNewStep(current);
  }, [steps, currentStep]);

  const stepsDisplay = newStep.map((step, index) => {
    const isClickable = index < currentStep - 1;
    const stepStyles = {
      cursor: isClickable ? "pointer" : "default",
    };
    return (
      <div
        key={index}
        className={
          index !== newStep.length - 1
            ? "w-full flex items-center justify-items-start flex-row"
            : "w-full flex items-center justify-items-start flex-row"
        }
      >
        <div
          className={`flex-1 border-t-2 transition duration-500 ease-in-out  ${
            step.selected ? "border-primary" : "border-[#EFF1F0] "
          }  `}
        ></div>
        <div className="relative flex flex-col items-center text-neutral-800">
          <div
            className={`rounded-full transition duration-500 ease-in-out h-12 w-12 flex items-center justify-center py-3  ${
              step.selected ? "bg-primary text-white" : "bg-[#EFF1F0]"
            }`}
            style={stepStyles}
            onClick={() => isClickable && onStepClick(index)}
          >
            {index + 1}
          </div>
          <div
            className={`absolute top-0  text-center mt-14 w-32 text-xs font-sans font-light ${
              step.selected ? "text-primary" : "text-[#C5CBC2]"
            }`}
          >
            {step.description}
          </div>
        </div>
        <div
          className={`flex-1 border-t-2 transition duration-500 ease-in-out  ${
            step.completed ? "border-primary" : "border-[#EFF1F0] "
          }  `}
        ></div>
      </div>
    );
  });

  return (
    <div className="flex justify-between items-center">{stepsDisplay}</div>
  );
};
export default Stepper;
