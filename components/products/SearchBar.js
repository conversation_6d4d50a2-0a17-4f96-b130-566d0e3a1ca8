"use client";

const SearchBar = ({ query, didChangeQuery }) => {
    const change = (e) => {
      e.preventDefault();
      e.stopPropagation()

      console.log(e.target.value);
      didChangeQuery(e);
    };

    const submitHandler = (e) => {
      e.preventDefault();
      e.stopPropagation();
    };
  
    return (
      <div className="pt-3 pr-[30px]">
        <form onSubmit={submitHandler}>
          <div className="relative float-right">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <svg
                aria-hidden="true"
                className="w-5 h-5 text-gray-500 dark:text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                ></path>
              </svg>
            </div>
            <input
              type="search"
              id="search"
              className="block p-4 pl-10 h-[34px] w-[300px] text-sm border-none rounded-lg bg-[#F8F8F8] text-gray-900"
              placeholder="Search Products"
              // value={query}
              onChange={change}
            />
          </div>
        </form>
      </div>
    );
  }

  export default SearchBar;