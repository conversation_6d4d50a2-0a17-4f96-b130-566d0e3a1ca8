"use client";
import { useState, useEffect } from "react";

import { useRouter } from "next/router";

import axios from "axios";

import { Sidebar } from "flowbite-react";

const SideBar = (props) => {
  const router = useRouter();
  const data = router.query;

  function didClickTop() {
    const id = data.parentid;

    props.didClickTop(id);
  }

  function didClickCat(id, item_id) {
    props.didClick(id, item_id);
  }

  function didClickSigleCat(id, noDrop) {
    props.didClickSingle(id, noDrop);
  }

  function didClickMain(id) {
    const fromSidebar = true;
    props.didClickMain(id, fromSidebar);
  }

  return (
    <div className="h-full w-full flex">
      <Sidebar
        aria-label="Sidebar"
        className="w-full product-sidebar hover: cursor-pointer"
      >
        <Sidebar.Items>
          <Sidebar.ItemGroup>
            <Sidebar.Item
              className="text-[14px] font-medium whitespace-normal"
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                didClickTop();
              }}
            >
              {data.category}
            </Sidebar.Item>
            {props.mainCategories.map((item) => {
              if (item.sub.length > 0) {
                return (
                  <Sidebar.Collapse
                    label={item.name}
                    key={item.id}
                    className={`text-[12px] font-bold custom-wrap ${
                      item.id === props.isOpenRef.current ? "bg-gray-200" : ""
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      didClickMain(item.id);
                    }}
                    open={item.id === props.isOpenRef.current ? true : false}
                  >
                    {item.sub.map((sub) => {
                      return (
                        <Sidebar.Item
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                            didClickCat(sub.id, item.id);
                          }}
                          key={sub.id}
                          className={`text-[12px] custom-wrap ${
                            sub.id === props.isOpenRef.sub ? "bg-gray-200" : ""
                          }`}
                        >
                          {sub.name}
                        </Sidebar.Item>
                      );
                    })}
                  </Sidebar.Collapse>
                );
              } else {
                return (
                  <Sidebar.Item
                    className={`text-[12px] font-bold custom-wrap ${
                      item.id === props.isOpenRef.current ? "bg-gray-200" : ""
                    }`}
                    key={item.id}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      const noDrop = true;
                      didClickSigleCat(item.id, noDrop);
                    }}
                  >
                    {item.name}
                  </Sidebar.Item>
                );
              }
            })}
          </Sidebar.ItemGroup>
        </Sidebar.Items>
      </Sidebar>
    </div>
  );
};

export default SideBar;
