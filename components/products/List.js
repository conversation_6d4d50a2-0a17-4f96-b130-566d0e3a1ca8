"use client";
import { useState, useEffect, useContext } from "react";
import { useSession, signOut } from "next-auth/react";

import Link from "next/link";
import Image from "next/image";

import axios from "axios";
import {
  Breadcrumb,
  Sidebar,
  Dropdown,
  <PERSON><PERSON>,
  <PERSON>ert,
  Badge,
} from "flowbite-react";
import { useCart } from "../../contexts/CartContext";
import { usePromotionsContext } from '../../contexts/PromotionsContext'; // Import the promotion context


const List = ({ data, filters, alert, setAlert }) => {
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [parentFilters, setParentFilters] = useState({});
  const [parentFiltersName, setParentFiltersName] = useState({});
  const [filteredData, setFilteredData] = useState([]);
  const { data: session, status } = useSession();
  const { updateCart } = useCart();
  const { promotions } = usePromotionsContext(); // Access promotions from context

  if (data.length < 1)
    return (
      <h1 className="text-center py-40 text-[#929791]">
        Please make a selection from the left column to see available products.
      </h1>
    );

  function setFilter(e, options, parent) {
    const value = e.target.value;
    const valueName = options.name;
    const parentFilter = options.filter_id;
    const parentName = parent.name;
    let curr = selectedFilters;
    let par = parentFilters;
    let name_array = parentFiltersName;
    // check if already in array, if so remove it.
    removeNumberFromArray(
      curr,
      Number(value),
      par,
      Number(parentFilter),
      valueName,
      parentName,
      name_array
    );
    let filteredProducts = "";
    if (Object.keys(parentFilters).length <= 1) {
      console.log("curr", curr);
      filteredProducts = data.filter((product) =>
        product.Product?.ProductAttribute?.some((attribute) =>
          curr.includes(attribute.attribute_id)
        )
      );
    } else {
      filteredProducts = data.filter((product) =>
        Object.keys(par).every((key) =>
          product.Product?.ProductAttribute?.some((attribute) =>
            par[key].includes(attribute.attribute_id)
          )
        )
      );

      // Object.keys(par).every(
      //   (key) =>
      //     (filteredProducts = data.filter((product) =>
      //       product.Product?.ProductAttribute?.some((attribute) =>
      //         par[key].includes(attribute.attribute_id)
      //       )
      //     ))
      // );

      const allKeysHaveMatch = Object.keys(par).every((key) => {
        return par[key].some((value) =>
          data.some((product) =>
            product.Product?.ProductAttribute?.some(
              (attribute) => value === attribute.attribute_id
            )
          )
        );
      });

      if (allKeysHaveMatch) {
        // Your code when all keys have at least one match
        console.log("All keys have at least one match");
      } else {
        // Your code when at least one key doesn't have a match
        console.log("At least one key does not have a match");
      }
    }

    setFilteredData(filteredProducts);
  }

  function removeNumberFromArray(
    array,
    number,
    parent_object,
    parent_number,
    number_name,
    parent_name,
    name_object
  ) {
    // console.log("number", number);
    if (array.includes(number)) {
      console.log("filter removed");

      const index = array.indexOf(number);
      array.splice(index, 1);

      const parentArray = parent_object[parent_number];
      const parentIndex = parentArray.indexOf(number);
      if (parentIndex !== -1) {
        parentArray.splice(parentIndex, 1);
        if (name_object.hasOwnProperty(parent_name)) {
          const nameArray = name_object[parent_name];
          const nameIndex = nameArray.indexOf(number_name);
          if (nameIndex !== -1) {
            nameArray.splice(nameIndex, 1);

            // If the name array becomes empty, delete the property
            if (nameArray.length === 0) {
              delete name_object[parent_name];
            }
          }
        }
        if (parentArray.length === 0) {
          delete parent_object[parent_number];
        }
      }
    } else {
      console.log("filter added");
      array.push(number);

      if (!parent_object.hasOwnProperty(parent_number)) {
        parent_object[parent_number] = [number];
        name_object[parent_name] = [number_name];
      } else {
        parent_object[parent_number].push(number);

        // Check if the name array exists, if not, create it
        if (!name_object.hasOwnProperty(parent_name)) {
          name_object[parent_name] = [];
        }

        // Add the new number_name to the array
        name_object[parent_name].push(number_name);
      }

      // console.log("array", array);

      setSelectedFilters(array);
      setParentFilters(parent_object);
      setParentFiltersName(name_object);
    }
  }

  function truncateProductName(str) {
    if (str.length > 50) {
      str = str.slice(0, 50).trim();
      str += "...";
    }

    return str;
  }

  const successAddNotification = (name) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: (
        <span>
          <span className="font-bold">{truncateProductName(name)}</span> added
          to cart.
        </span>
      ),
      show: true,
    };
    setAlert(newNotification);
  };

  const failureAddNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    setAlert(newNotification);
  };

  async function addToCart(product_id, item, promotions) {
    const { account_id, id } = session.user;


    try {
      // Update stock before adding to cart
      const updateResponse = await axios.post("/api/products/updateStockPrice", {
        id: product_id,
        account_jde: session.user.Account.jde_id,
      });
  
      // Check if the product is in stock
      if (updateResponse.data.stock > 0) {
        // Proceed to add the product to the cart
        await axios.post("api/cart", {
          id: product_id,
          user_id: id,
          account_id: account_id,
          qty: 1,
          price: updateResponse.data.price,
          promotions:promotions
        });
  
        // Update the cart state using the context
        const cartResponse = await axios.get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        );
        updateCart(cartResponse.data.items);
  
        successAddNotification(item.Product? item.Product.name : item.name);
      } else {
        // Display an error notification or take appropriate action
        console.error("Product is out of stock");
        failureAddNotification("Product is out of stock. Refresh Page");
        // You might want to show a notification or update the UI to indicate the product is out of stock
      }
    } catch (error) {
      // Handle errors
      console.error("Error adding to cart:", error);
      failureAddNotification("Failed To Add To Cart, Please Try Again.");
    }

  }

  const filterColors = [
    "info",
    "success",
    "warning",
    "indigo",
    "pink",
    "purple",
  ];

  function getRandomColorIndex(colorArray) {
    if (!Array.isArray(colorArray) || colorArray.length === 0) {
      // Return an error value or handle the case where the array is empty
      return null;
    }

    const randomIndex = Math.floor(Math.random() * colorArray.length);
    return randomIndex;
  }

  console.log("filteredData", filteredData);
  console.log("selectedFilters", selectedFilters);
  console.log("data", data);

  return (
    <div className="w-full flex flex-col ">
      <div className="flex flex-row flex-nowrap gap-4 py-4 overflow-x-auto ">
        {filters.map((item) => {
          return (
            <div className="flex-[0_0_auto] max-w-full min-w-min">
              <Dropdown
                label={item.Filter.name}
                size={"sm"}
                color={"#F8F8F8"}
                key={item.id}
              >
                {item.Filter.FilterAttribute.map((options) => {
                  return (
                    <div key={options.id}>
                      <Dropdown.Item>
                        <input
                          type="checkbox"
                          id={options.id}
                          name={options.name}
                          value={options.id}
                          className="mx-2"
                          onClick={(e) => setFilter(e, options, item.Filter)}
                          checked={selectedFilters.includes(options.id)}
                        />
                        <label htmlFor={options.name}>{options.name}</label>
                      </Dropdown.Item>
                    </div>
                  );
                })}
              </Dropdown>
            </div>
          );
        })}
      </div>
      <div className="flex flex-row h-full overflow-y-auto">
        <table className="table-auto w-full border-separate border-spacing-4">
          <tbody>
            {selectedFilters.length > 0 ? (
              <tr className="flex flex-row gap-4" key="0">
                <td>{filteredData.length} results</td>
                <td className="flex gap-2">
                  {Object.entries(parentFiltersName).map(
                    ([parentName, filterName]) => (
                      <Badge
                        color="blue"
                        key={parentName}
                        className={
                          Object.keys(parentFiltersName).length < 5
                            ? "text-[12px] font-bold"
                            : Object.keys(parentFiltersName).length < 7
                            ? "text-[11px] font-bold"
                            : Object.keys(parentFiltersName).length < 9
                            ? "text-[10px] font-bold"
                            : "text-[9px] font-bold"
                        }
                      >
                        {parentName}: {filterName.join(", ")}
                      </Badge>
                    )
                  )}
                </td>
                {/* <Button size='xs' color={'#08447C'} onClick={() => setFilteredData([])}>Clear</Button> */}
              </tr>
            ) : (
              <tr>
                <td>{data.length} results</td>
                {/* <td>
                  Filters:
                  {Object.entries(parentFilters).map(
                    ([parentNumber, parentArray]) => (
                      <>
                        {parentNumber}: {JSON.stringify(parentArray)}
                      </>
                    )
                  )}
                </td> */}
              </tr>
            )}

            {selectedFilters.length > 0
              ? filteredData.map((item) => {
                  return (
                    <tr
                      key={
                        item.Product
                          ? item.Product.sku + "+" + Math.random()
                          : item.name + "+" + Math.random()
                      }
                    >
                      <td>
                        <div className="flex gap-7">
                          <div key={Math.random()}>
                            <Link
                              href={{
                                pathname: "/product",
                                query: {
                                  name: item.Product
                                    ? item.Product.name
                                    : item.name,
                                  sku: item.Product
                                    ? item.Product.sku
                                    : item.sku,
                                  id: item.Product ? item.Product.id : item.id,
                                },
                              }}
                            >
                              <Image
                                className="border border-gray-300"
                                src={`${
                                  process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                                  "oasis-product-images/"
                                }${item.Product.ProductImage[0].path}_l.${
                                  item.Product.ProductImage[0].extension
                                }`}
                                alt={item.Product.name}
                                width={50}
                                height={50}
                              />
                            </Link>
                          </div>
                          <div className="grow">
                            <div className="pl-4">
                              <Link
                                href={{
                                  pathname: "/product",
                                  query: {
                                    name: item.Product
                                      ? item.Product.name
                                      : item.name,
                                    sku: item.Product
                                      ? item.Product.sku
                                      : item.sku,
                                    id: item.Product
                                      ? item.Product.id
                                      : item.id,
                                  },
                                }}
                              >
                                <p className="text-base">{item.Product.name}</p>{" "}
                              </Link>
                              <p className="text-[#929791]">
                                {item.Product.sku}
                              </p>
                            </div>
                          </div>

                          <div className="my-2">
                            <Button
                              color="light"
                              pill={true}
                              width={50}
                              height={28}
                              disabled={
                                item
                                  ? item.Product.stock == 0
                                    ? true
                                    : false
                                  : item.stock == 0 ? true : false
                              }
                              onClick={() =>
                                addToCart(
                                  item.Product ? item.Product.id : item.id,
                                  item,
                                  promotions
                                )
                              }
                            >
                              <Image
                                src="/incart.png"
                                alt=""
                                width={16}
                                height={16}
                              />
                            </Button>
                          </div>

                          <div className="my-4">
                            <Link
                              className="bg-[#08447C] text-white rounded-full whitespace-nowrap py-2 px-8"
                              href={{
                                pathname: "/product",
                                query: {
                                  name: item.Product
                                    ? item.Product.name
                                    : item.name,
                                  sku: item.Product
                                    ? item.Product.sku
                                    : item.sku,
                                  id: item.Product ? item.Product.id : item.id,
                                },
                              }}
                              replace
                            >
                              View Details
                            </Link>
                          </div>
                        </div>
                      </td>
                    </tr>
                  );
                })
              : data.map((item) => (
                  <tr
                    key={
                      item.Product
                        ? item.Product.sku + "+" + Math.random()
                        : item.name + "+" + Math.random()
                    }
                  >
                    <td>
                      <div className="flex gap-7">
                        <div className="">
                          {item.Product ? (
                            item.Product.ProductImage &&
                            item.Product.ProductImage.length > 0 ? (
                              <Link
                                href={{
                                  pathname: "/product",
                                  query: {
                                    name: item.Product
                                      ? item.Product.name
                                      : item.name,
                                    sku: item.Product
                                      ? item.Product.sku
                                      : item.sku,
                                    id: item.Product
                                      ? item.Product.id
                                      : item.id,
                                  },
                                }}
                              >
                                <Image
                                  className="border border-gray-300"
                                  src={`${
                                    process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                                    "oasis-product-images/"
                                  }${item.Product.ProductImage[0].path}_l.${
                                    item.Product.ProductImage[0].extension
                                  }`}
                                  alt={
                                    item.Product ? item.Product.name : item.name
                                  }
                                  width={50}
                                  height={50}
                                />{" "}
                              </Link>
                            ) : (
                              <Link
                                href={{
                                  pathname: "/product",
                                  query: {
                                    name: item.Product
                                      ? item.Product.name
                                      : item.name,
                                    sku: item.Product
                                      ? item.Product.sku
                                      : item.sku,
                                    id: item.Product
                                      ? item.Product.id
                                      : item.id,
                                  },
                                }}
                              >
                                <Image
                                  className="border border-gray-300"
                                  src="/no_image.jpeg"
                                  alt={
                                    item.Product ? item.Product.name : item.name
                                  }
                                  width={50}
                                  height={50}
                                />{" "}
                              </Link>
                            )
                          ) : item.ProductImage &&
                            item.ProductImage.length > 0 ? (
                            <Link
                              href={{
                                pathname: "/product",
                                query: {
                                  name: item.Product
                                    ? item.Product.name
                                    : item.name,
                                  sku: item.Product
                                    ? item.Product.sku
                                    : item.sku,
                                  id: item.Product ? item.Product.id : item.id,
                                },
                              }}
                            >
                              {" "}
                              <Image
                                className="border border-gray-300"
                                src={`${
                                  process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                                  "oasis-product-images/"
                                }${item.ProductImage[0].path}_l.${
                                  item.ProductImage[0].extension
                                }`}
                                alt={
                                  item.Product ? item.Product.name : item.name
                                }
                                width={50}
                                height={50}
                              />{" "}
                            </Link>
                          ) : (
                            <Link
                              href={{
                                pathname: "/product",
                                query: {
                                  name: item.Product
                                    ? item.Product.name
                                    : item.name,
                                  sku: item.Product
                                    ? item.Product.sku
                                    : item.sku,
                                  id: item.Product ? item.Product.id : item.id,
                                },
                              }}
                            >
                              <Image
                                className="border border-gray-300"
                                src="/no_image.jpeg"
                                alt={
                                  item.Product ? item.Product.name : item.name
                                }
                                width={50}
                                height={50}
                              />
                            </Link>
                          )}
                        </div>
                        <div className="grow">
                          <div className="pl-4">
                            <p className="text-base">
                              <Link
                                href={{
                                  pathname: "/product",
                                  query: {
                                    name: item.Product
                                      ? item.Product.name
                                      : item.name,
                                    sku: item.Product
                                      ? item.Product.sku
                                      : item.sku,
                                    id: item.Product
                                      ? item.Product.id
                                      : item.id,
                                  },
                                }}
                              >
                                {item.Product ? item.Product.name : item.name}
                              </Link>
                            </p>
                            <p className="text-[#929791]">
                              {item.Product ? item.Product.sku : item.sku}
                            </p>
                          </div>
                        </div>

                        <div className="my-2">
                          <Button
                            color="light"
                            pill={true}
                            width={50}
                            height={28}
                            disabled={
                              item.Product
                                ? item.Product.stock == 0
                                  ? true
                                  : false
                                : item.stock == 0 ? true : false
                            }
                            onClick={() =>
                              addToCart(
                                item.Product ? item.Product.id : item.id,
                                item,
                                promotions
                              )
                            }
                          >
                            <Image
                              src="/incart.png"
                              alt=""
                              width={16}
                              height={16}
                            />
                          </Button>
                        </div>

                        <div className="my-4">
                          <Link
                            className="bg-[#08447C] text-white rounded-full whitespace-nowrap py-2 px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
                            href={{
                              pathname: "/product",
                              query: {
                                name: item.Product
                                  ? item.Product.name
                                  : item.name,
                                sku: item.Product ? item.Product.sku : item.sku,
                                id: item.Product ? item.Product.id : item.id,
                              },
                            }}
                            replace
                          >
                            View Details
                          </Link>
                        </div>
                      </div>
                    </td>
                  </tr>
                ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default List;
