import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "flowbite-react";
import Stepper from "./formStepper/Stepper";
import StepperControl from "./formStepper/StepperControl";
import {
  UseContextProvider,
  useStepperContext,
} from "../contexts/StepperContext";
import { useUserAcctContext } from "../contexts/UserAcctContext";
import axios from "axios";

import StepOne from "./formStepper/steps/StepOne";
import StepTwo from "./formStepper/steps/StepTwo";
import StepThree from "./formStepper/steps/StepThree.js";
import StepFour from "./formStepper/steps/StepFour";

import { useRouter } from "next/router";
import { signIn } from "next-auth/react";
import restoreAccount from "../pages/api/syncJDE/restoreAccount.js";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";



function RegisterForm() {
  const [currentStep, setCurrentStep] = useState(1);
  const { router, push } = useRouter();
  const [partnerType, setPartnerType] = useState([]);
  const [loading, setloading] = useState({
    registering: false,
    syncing: false,
    finalizing: false,
    validating: false,
  });
  const { userData, setUserData } = useStepperContext();
  const { userAcct, setUserAcct } = useUserAcctContext();
  const { notifications, addNotifications } = useGlobalAlertContext();


  // State to manage the modal visibility
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  // Function to open the modal
  const openModal = () => setIsModalOpen(true);

  // Function to close the modal
  const closeModal = () => {
    setIsModalOpen(false);
    window.location.reload();
  };

  const failureNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };

  const steps = ["General", "Contacts", "Account", "Practice"];

  const displayStep = (step) => {
    switch (step) {
      case 1:
        return <StepOne />;
      case 2:
        return <StepTwo />;
      case 3:
        return <StepThree partnerType={partnerType} />;
      case 4:
        return <StepFour />;
      default:
    }
  };

  const onStepClick = (stepIndex) => {
    // Ensure the stepIndex is within bounds
    if (stepIndex >= 0 && stepIndex < currentStep - 1) {
      setCurrentStep(stepIndex + 1);
    }
  };

  const handleClick = (direction) => {
    let newStep = currentStep;
    // console.log('firstName', document.getElementById('firstName').value,);

    console.log(newStep);
    // return;

    if (!validateStep(newStep)) {
      return;
    }

    if (newStep === 1) {
      setloading({ ...loading, validating: true });
      if (
        passwordCheck(
          userData.user.password,
          userData.user.confirm_password
        ) === 0
      ) {
        setloading({ ...loading, validating: false });
        return;
      }
      emailCheck(userData.user.email)
        .then((response) => {
          console.log("response", response);
          if (response == 1) {
            setloading({ ...loading, validating: false });
            console.log("Email is available. Continue with validation.");
            // direction === "next" ? newStep++ : newStep--;
            newStep++;
            // check if steps are within bounds
            console.log(newStep);
            newStep > 0 && newStep <= steps.length && setCurrentStep(newStep);
          } else if (response == 0) {
            setloading({ ...loading, validating: false });
            failureNotification("Email is already taken.");
            return;
          } else if (response == 2) {
            setloading({ ...loading, validating: false });
            failureNotification("Please enter an email address.");
            return;
          }
        })
        .catch((error) => {
          console.error("Error:", error);
        });
    } else {
      direction === "next" ? newStep++ : newStep--;
      // newStep++;
      // check if steps are within bounds
      console.log(newStep);
      newStep > 0 && newStep <= steps.length && setCurrentStep(newStep);
      setloading({ ...loading, syncing: false });
    }
  };

  function hasNumber(str) {
    return /\d/.test(str);
  }

  const emailCheck = async (value) => {
    console.log("emailcheck value", value);
    if (!value) {
      return 2;
    }
    console.log(value);
    try {
      const response = await fetch(`/api/user/verifyByEmail?email=${value}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const responseData = await response.json();

        if (responseData === 0) {
          console.log("user ID", responseData);
          return 1;
        } else {
          console.log("user ID", responseData);
          return 0;
        }
      }
    } catch (error) {
      // Handle any errors that occurred during the API call
      console.error("Error:", error);
    }
  };

  const passwordCheck = (password, confirmPassword) => {
    if (password !== confirmPassword) {
      failureNotification("Passwords do not match");
      return 0;
    } else if (
      password.length < 8 ||
      !/[A-Z]/.test(password) ||
      !hasNumber(password)
    ) {
      failureNotification(
        "Please enter a password at least 8 characters, 1 uppercase, 1 lowercase, and 1 number"
      );
      return 0;
    } else {
      return 1;
    }
  };

  const syncAccount = async (email) => {
    try {
      const res = await fetch("/api/syncJDE/syncAccount", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        console.log("Account Updated");
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      failureNotification(error.message);
      console.error(
        "An unexpected error while updating Account occurred:",
        error
      );
    }
  };

  const validateStep = (step) => {
    const validationData = {
      user: {
        firstName: userData.user.firstName,
        lastName: userData.user.lastName,
        email: userData.user.email,
        // password: userData.user.password,
        // confirm_password: userData.user.confirm_password,
      },
      account: {
        business_name: userData.account.business_name,
        address_l1: userData.account.address_l1,
        city: userData.account.city,
        state: userData.account.state,
        zip: userData.account.zip,
        num_locations: userData.account.num_locations,
        partner_type_code: userData.account.partner_type_code,
        shipping_acct_num: userData.account.shipping_acct_num,
        primary_specialty: userData.account.primary_specialty,
        // Add other fields as needed
      },
      contacts: {
        purchasing_email: userData.contacts.purchasing_email,
        accounting_email: userData.contacts.accounting_email,
        manager_email: userData.contacts.manager_email,
        front_office_email: userData.contacts.front_office_email,
        lead_tech_email: userData.contacts.lead_tech_email,
        ophtha_email: userData.contacts.ophtha_email,
        optometrist_email: userData.contacts.optometrist_email,
      },
    };

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const zipPattern = /^[0-9-]+$/;

    const validationRules = {
      1: [
        {
          field: "user.firstName",
          condition: (value) => !!value,
          errorMessage: "Please enter first name",
          inputElement: document.getElementById("firstName"),
        },
        {
          field: "user.lastName",
          condition: (value) => !!value,
          errorMessage: "Please enter last name",
        },
        {
          field: "user.email",
          condition: (value) => emailPattern.test(value), 
          errorMessage: "Please enter a valid email",
        },
        // {
        //   field: "user.password",
        //   condition: (value) => value === userData.user.confirm_password,
        //   errorMessage: "Passwords do not match",
        // },
        // {
        //   field: "user.password",
        //   condition: (value) =>
        //     value &&
        //     value.length >= 8 &&
        //     /[A-Z]/.test(value) &&
        //     hasNumber(value),
        //   errorMessage:
        //     "Please enter a password at least 8 characters, 1 uppercase, 1 lowercase, and 1 number",
        // },
        {
          field: "account.business_name",
          condition: (value) => !!value,
          errorMessage: "Please enter business name",
        },
        {
          field: "account.address_l1",
          condition: (value) => !!value,
          errorMessage: "Please enter street address",
        },
        {
          field: "account.city",
          condition: (value) => !!value,
          errorMessage: "Please enter city",
        },
        {
          field: "account.zip",
          condition: (value) => zipPattern.test(value),
          errorMessage: "Please enter a valid zip",
        },
        {
          field: "account.state",
          condition: (value) => !!value,
          errorMessage: "Please enter state",
        },
      ],
      2: [
        {
          field: "contacts.purchasing_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid purchasing email",
        },
        {
          field: "contacts.accounting_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid accounting email",
        },
      ],
      3: [
        {
          field: "account.partner_type_code",
          condition: (value) => !!value,
          errorMessage: "Please enter Partner Type",
        },
        {
          field: "account.shipping_acct_num",
          condition: (value) => {
            // Check if freight_company is "ups" and shipping_acct_num is not empty
            return (
              userData.account.freight_company === "ups" 
              ? (value !== undefined && value.trim() !== "" && value.length == 6)
              : true // Return true if freight_company is not "ups"
            );
          },
          errorMessage: "Please enter a valid UPS shipper account number.",
        },
      ],
      4: [
        {
          field: "account.num_locations",
          condition: (value) => !!value,
          errorMessage: "Please enter Number of Locations",
        },
        {
          field: "account.primary_specialty",
          condition: (value) => !!value,
          errorMessage: "Please enter Primary care specialty",
        },
        {
          field: "contacts.manager_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid manager email",
        },
        {
          field: "contacts.optometrist_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid optometrist email",
        },
        {
          field: "contacts.ophtha_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid ophthalmologist email",
        },
        {
          field: "contacts.lead_tech_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid lead tech email",
        },
        {
          field: "contacts.front_office_email",
          condition: (value) => !value || emailPattern.test(value),
          errorMessage: "Please enter a valid front office email",
        },
      ],
    };

    const errorMessages = [];

    if (validationRules[step]) {
      validationRules[step].forEach((rule) => {
        const fieldPath = rule.field.split(".");
        let fieldValue = validationData;
        for (const path of fieldPath) {
          fieldValue = fieldValue[path];

          if (fieldValue === undefined) {
            break;
          }
        }

        if (!rule.condition(fieldValue)) {
          errorMessages.push(rule.errorMessage);
          // rule.inputElement.setCustomValidity(rule.message);
          // console.log(rule.inputElement, rule.inputElement.validity);
        }
      });
    } else {
      console.error(
        `Validation rules for scenario ${selectedScenario} not found.`
      );
    }

    if (errorMessages.length > 0) {
      console.log(errorMessages);
      errorMessages.forEach((error) => {
        failureNotification(error); // Pass the error message to failureNotification
      });
      // failureNotification(errorMessages);
      return false;
    }

    return true;
  };

  const setDetails = async (email) => {
    try {
      const res = await fetch("/api/user/setDetails", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: email }),
      });
      if (res.status === 200) {
        const data = await res.json();
        const tc = data.Account.territory_code == "" ? false : true;
        setUserAcct({
          ...userAcct,
          user: {
            id: data.id,
            first_name: data.first_name,
            last_name: data.last_name,
            email: data.email,
            location_id: data.location_id,
            account_id: data.account_id,
            primary_user: data.primary_user,
            group_id: data.group_id,
            phone: data.phone,
            position: data.position,
            address: data.address,
            address_l2: data.address_2,
            city: data.city,
            state: data.state,
            zip: data.zip,
          },
          account: {
            id: data.Account.id,
            jde_id: data.Account.jde_id,
            business_name: data.Account.business_name,
            approved: data.Account.approved,
            territory_code: tc ? data.Account.territory_code : "",
            oasis_rep_id: tc ? data.Account.oasis_rep_id : "",
            oasis_rep_name: tc ? data.Account.OasisRep.name : "",
            oasis_rep_email: tc ? data.Account.OasisRep.email : "",
            oasis_rep_phone: tc ? data.Account.OasisRep.phone : "",
          },
        });
      } else {
        throw new Error(await res.text());
      }
    } catch (error) {
      failureNotification(error.message);
      console.error(
        "An unexpected error while updating Account occurred:",
        error
      );
    }
  };

  const registerUser = async (userData) => {
    event.preventDefault();
    if (!validateStep(4)) {
      return;
    }
    if (userData.account.accept === false) {
      failureNotification("Please accept Terms of Use");
      return;
    }
    const email = userData.user.email;
    const password = userData.user.password;
    console.log("User Data", userData.user.email);
    setloading({ ...loading, registering: true });

    console.log("register:");
    // return;
    try {
      const res = await fetch("/api/register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(userData),
      });
      if (res.status === 200) {
        const reg_data = await res.json();
        const account_id = reg_data.account_id;
        const source = reg_data.source;

        console.log("account_id", account_id);
        console.log("source", source);
        console.log("reg_data", reg_data);

        if(userData.account.uploadedFile) {
          const formData = new FormData();
          formData.append("account_id", account_id);
          formData.append("uploadedFile", userData.account.uploadedFile);
          const resCert = await fetch("/api/account/uploadCert", {
            method: "POST",
            headers: {},
            body: formData,
          });
          if (resCert.status === 200) {
            console.log("Upload successful!");
          } else {
              console.error("Upload failed:", resCert.status);
              throw new Error(await resCert.text());

          }
        }

        if (source === 0) {
          setloading({ ...loading, registering: false, syncing: true });
          const res2 = await fetch("/api/syncJDE/addAccount", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(account_id),
          });
          if (res2.status === 200) {
            const data = await res2.json();
            console.log("res2.data", data);
            const res3 = await fetch("/api/syncJDE/addContactsLocation", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify(data),
            });
            if (res3.status === 200) {
              setloading({ ...loading, syncing: false, finalizing: true });
              // syncAccount(email).then(function (result) {

              // add welcome email function here
              fetch("/api/account/welcomeEmail", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ account_id: account_id }),
              }).then(async (welcomeEmailResponse) => {
                if (welcomeEmailResponse.status === 200) {
                  console.log("Welcome email sent");
                } else {
                  console.log(
                    "An email error occurred:",
                    welcomeEmailResponse.text()
                  );
                }
              });

              // Open the modal here
              openModal();

              // signIn("credentials", {
              //   email,
              //   password,
              //   callbackUrl: `${window.location.origin}/dashboard`,
              //   redirect: false,
              // })
              //   .then(function (result) {
              //     setDetails(email).then(() => {
              //       push("/dashboard");
              //       setloading({ ...loading, finalizing: false });
              //     });
              //   })
              //   .catch((err) => {
              //     setloading({ ...loading, finalizing: false });
              //     alert("Failed to register: " + err.toString());
              //   });
              // });
            } else {
              setloading({
                ...loading,
                registering: false,
                syncing: false,
                finalizing: false,
              });

              throw new Error(await res3.text());
            }
          } else {
            setloading({
              ...loading,
              registering: false,
              syncing: false,
              finalizing: false,
            });

            throw new Error(await res2.text());
          }
        } else {
          setloading({ ...loading, registering: false, syncing: true });
          const res3 = await fetch("/api/syncJDE/addContactsLocation", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(reg_data),
          });
          if (res3.status === 200) {
            setloading({ ...loading, syncing: false, finalizing: true });
              // add restored email function here
              fetch("/api/account/restoredEmail", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ account_id: account_id }),
              }).then(async (restoredEmailResponse) => {
                if (restoredEmailResponse.status === 200) {
                  console.log("Restored account email notification sent");
                } else {
                  console.log(
                    "An email error occurred:",
                    restoredEmailResponse.text()
                  );
                }
              });
              setIsRestoring(true);
              await fetch("/api/syncJDE/restoreOrderHistory", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ account_id: account_id }),
              }).then(async (restoreOrdersResponse) => {      
                if (restoreOrdersResponse.status === 200) {
                  setIsRestoring(false);
                  console.log("Orders restored successfully");
                  signIn("credentials", {
                    email,
                    password,
                    callbackUrl: `${window.location.origin}/dashboard`,
                    redirect: false,
                  })
                    .then(function (result) {
                      // setDetails(email);
                      push("/dashboard");
                      setloading({ ...loading, finalizing: false });
                    })
                    .catch((err) => {
                      failureNotification("Failed to register: " + err.toString());
                    });
                } else {
                  setIsRestoring(false);
                  failureNotification(await restoreOrdersResponse.text());
             
                  signIn("credentials", {
                    email,
                    password,
                    callbackUrl: `${window.location.origin}/dashboard`,
                    redirect: false,
                  })
                  .then(function (result) {
                    // setDetails(email);
                    push("/dashboard");
                    setloading({ ...loading, finalizing: false });
                  })
                  .catch((err) => {
                    failureNotification("Failed to register: " + err.toString());
                  });
                }
              });
          } else {
            setloading({
              ...loading,
              registering: false,
              syncing: false,
              finalizing: false,
            });
            throw new Error(await res3.text());
          }
        }
      } else {
        setloading({
          ...loading,
          registering: false,
          syncing: false,
          finalizing: false,
        });
        throw new Error(await res.text());
      }
    } catch (error) {
      setloading({
        ...loading,
        registering: false,
        syncing: false,
        finalizing: false,
      });
      failureNotification(error.message);
      console.error("An unexpected error occurred:", error);
    }

    // push("/dashboard");
  };

  useEffect(() => {
    axios.get("/api/partnerType/get").then((response) => {
      setPartnerType(response.data.formattedItems);
    });
  }, []);

  return (
    <div className="mx-auto justify-center">
      {/* Stepper */}
      {/* <UseContextProvider> */}
      <div className="horizontal mt-3 -mx-3">
        <Stepper
          steps={steps}
          currentStep={currentStep}
          onStepClick={onStepClick}
        />

        <div className="mt-16 mb-7 min-h-[500px]">
          {displayStep(currentStep)}
        </div>
      </div>

      {/* navigation button */}
      {currentStep <= steps.length && (
        <StepperControl
          handleClick={handleClick}
          currentStep={currentStep}
          steps={steps}
          loading={loading}
          registerUser={registerUser}
        />
      )}
      {/* </UseContextProvider> */}
      <Modal
          show={isModalOpen}
          onClose={closeModal}
        >
        <Modal.Header>Registration Success</Modal.Header>
        <Modal.Body>
          <div className="flex flex-col justify-center ">
              <div className="pb-8 w-11/12 m-auto">
                <div className="flex flex-col text-center pb-4 font-normal">
                  <p className="font-bold">Thank you for submitting your registration to MyOasis360.com</p>
                  <br />
                  <p>Your account must be activated before a purchase online can be made. Account activation may take up to 24 hours, or our next business day. Our business hours are Monday through Friday 6:30AM PST to 4:30 PM PST. If your order is urgent, please call Customer Service at (844) 820-8940 during business hours.</p>
                </div>
                <Button
                  className="w-2/4 mx-auto mt-4 bg-primary px-8 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent"
                  onClick={closeModal}
                >
                  Return to Login Page
                </Button>
              </div>
          </div>
        </Modal.Body>
      </Modal>
      <Modal
          show={isRestoring}
        >
        <Modal.Body>
          <div className="flex flex-col justify-center ">
              <div className="pb-8 w-11/12 m-auto">
                <div className="flex flex-col text-center pb-4 font-normal">
                  <p className="text-xl font-medium text-gray-900">Loading Order History</p> <br />
                  <p>Your account's order history is now being retrieved. This may take a minute or two. Thank you for your patience.</p>
                </div>
              
              </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
}

export default RegisterForm;
