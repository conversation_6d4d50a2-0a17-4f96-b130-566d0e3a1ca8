import { Table, Checkbox, Tabs } from "flowbite-react";
import { AiFillCheckCircle } from "react-icons/ai";
import { <PERSON>s<PERSON>rinter } from "react-icons/bs";
import { FaArrowsRotate } from "react-icons/fa6";
import {
  FaCheckCircle,
  FaRegCircle,
  FaChevronDown,
  FaDesktop,
  FaPhoneAlt,
} from "react-icons/fa";
import { IoDownloadOutline } from "react-icons/io5";

import { But<PERSON>, Modal, Spinner } from "flowbite-react";
import { useSession, signOut } from "next-auth/react";
import { useState, useEffect } from "react";
import { useDate } from "../contexts/DateContext";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import axios from "axios";

function InvoiceTable({ data }) {
  const { data: session, status } = useSession();
  const { startDate, endDate, handleStartDateChange, handleEndDateChange } =
    useDate();

  const [openInvoice, setOpenInvoice] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [orderSubtotal, setOrderSubtotal] = useState(0);

  const handleOpenModal = (item) => {
    setSelectedItem(item);
    setOpenInvoice(true);
  };
  const handleSubtotalChange = (subtotal) => {
    setOrderSubtotal(subtotal);
  };

  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  if (session) {
    return (
      <>
        <table className="w-full text-sm text-center text-gray-500 dark:text-gray-400">
          <caption className="p-8 text-2xl font-semibold text-left text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <div className="flex justify-between">
              <div className="">Invoices</div>
              <div className="flex justify-center text-3xl">
                {/* <Button color="light">
                  <MdAddCircle className="mr-2 h-5 w-5" />
                  Add New
                </Button> */}
                <span className="mx-3 my-3 text-gray-500 text-sm">From</span>
                <DatePicker
                  dateFormat="MMMM d, yyyy"
                  placeholderText="Start Date"
                  selected={startDate}
                  onChange={handleStartDateChange}
                  selectsStart
                  startDate={startDate}
                  endDate={endDate}
                  className="text-center bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                />
                <span className="mx-4 my-3 text-gray-500 text-sm">To</span>
                <DatePicker
                  dateFormat="MMMM d, yyyy"
                  placeholderText="End Date"
                  selected={endDate}
                  onChange={handleEndDateChange}
                  selectsEnd
                  startDate={startDate}
                  endDate={endDate}
                  minDate={startDate}
                  className="text-center bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                />
              </div>
              <div className="">
                {/* <Button color="light">
                  <MdAddCircle className="mr-2 h-5 w-5" />
                  Add New
                </Button> */}
              </div>
{/* 
              <div className="text-center">
                <Button
                  color="light"
                  onClick={refreshPOP}
                  className="text-center m-auto"
                >
                  {fetchingPOP ? (
                    <Spinner className="mr-2 h-5 w-5" />
                  ) : (
                    <FaArrowsRotate className="mr-2 h-5 w-5" />
                  )}
                  {fetchingPOP ? "Running..." : "Run Post Order Process"}
                </Button>
                <div className="text-xs text-gray-400 pt-2">
                  {lastFetched &&
                    "Last Ran: " +
                      new Date(lastFetched).toLocaleString("en-US", {
                        month: "numeric",
                        day: "2-digit",
                        year: "2-digit",
                        hour: "2-digit",
                        minute: "2-digit",
                        hour12: true, // Use 24-hour format
                      })}
                </div>
              </div> */}
            </div>
          </caption>
          <Table>
            <Table.Head className="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400 text-center">
              {/* <Table.HeadCell className="px-6 py-3 flex items-center justify-center">
                <IoDownloadOutline size={20} />
              </Table.HeadCell> */}
              <Table.HeadCell scope="col" className="px-6 py-3">
                Date
              </Table.HeadCell>
              <Table.HeadCell scope="col" className="px-6 py-3">
                Customer
              </Table.HeadCell>
              <Table.HeadCell scope="col" className="px-6 py-3">
                Invoice Number
              </Table.HeadCell>
              <Table.HeadCell scope="col" className="px-6 py-3">
                Order Number
              </Table.HeadCell>
              <Table.HeadCell scope="col" className="px-6 py-3">
                Type
              </Table.HeadCell>
              {/* <Table.HeadCell scope="col" className="px-6 py-3">
                Terms
              </Table.HeadCell> */}
              <Table.HeadCell scope="col" className="px-6 py-3">
                Amount
              </Table.HeadCell>
              <Table.HeadCell scope="col" className="px-6 py-3">
                Status
              </Table.HeadCell>
              <Table.HeadCell scope="col" className="px-6 py-3">
                View
              </Table.HeadCell>
            </Table.Head>
            <tbody>
              {/* {invoices.length < 1 ? (
            <div>No available invoices.</div>
          ) : ( */}
              {data.map((item) => {
                return (
                  <Row
                    data={item}
                    key={item.id}
                    handleOpenModal={handleOpenModal}
                  />
                );
              })}
              {/* )} */}
            </tbody>
          </Table>
        </table>

        <Modal
          show={openInvoice}
          size="3xl"
          dismissible
          onClose={() => setOpenInvoice(false)}
          className="modal-opacity modal-content"
        >
          {/* Check if a selectedItem is available before displaying item details */}
          {selectedItem && (
            <>
              <Modal.Header>
                Invoice Details - Order #{selectedItem.Order.jde_id}
              </Modal.Header>
              <Modal.Body>
                {/* Display item details using selectedItem */}
                <div className="grid grid-cols-3 gap-4 pb-6">
                  <div>
                    <p className="text-[#929791]">Shipping Address</p>
                    <p>{selectedItem.Order.Locations.practice}</p>
                    <p>
                      {selectedItem.Order.Locations.address}{" "}
                      {selectedItem.Order.Locations.address_2}
                    </p>
                    <p>
                      {selectedItem.Order.Locations.city},{" "}
                      {selectedItem.Order.Locations.state}{" "}
                      {selectedItem.Order.Locations.zip}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#929791]">Billing Address</p>
                    <p>{session.user.Account.business_name}</p>
                    <p>
                      {session.user.Account.address}{" "}
                      {session.user.Account.address_2}
                    </p>
                    <p>
                      {session.user.Account.city}, {session.user.Account.state}{" "}
                      {session.user.Account.zip.substring(0,5)}
                    </p>
                  </div>

                  <div>
                    <p className="text-[#929791]">Invoice Number</p>
                    <p>{selectedItem.invoice_num}</p>
                  </div>
                </div>
                <hr className="h-px bg-gray-100 border-0 -mx-6" />
                <div className="grid grid-cols-3 gap-4 py-6 font-light">
                  <div>
                    <p className="text-[#929791]">Date</p>
                    <p>
                      {selectedItem.Order.date
                        ? new Date(selectedItem.Order.date).toLocaleDateString(
                            "en-US",
                            {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                            }
                          )
                        : ""}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#929791]">Status</p>
                    {selectedItem.status === 1 && (
                      <p className="text-green-500">Paid</p>
                    )}
                    {selectedItem.status === 2 && (
                      <div>
                      <p className="text-yellow-500">Due</p>
                      {selectedItem.amount_due && (<p className="text-yellow-500"> {formatPrice.format(selectedItem.amount_due)} due by</p>)}
                      <p className="text-yellow-500"> {selectedItem.due_date
                        ? new Date(selectedItem.due_date).toLocaleDateString("en-US", {
                          year: "2-digit",
                          month: "2-digit",
                          day: "2-digit",
                          timeZone: "UTC", // Specify UTC timezone to prevent conversion
                        })
                      : ""}</p>
                      </div>
                    )}
                    {selectedItem.status === 3 && (
                      <div>
                      <p className=" text-red-500">Past Due</p>
                      {selectedItem.amount_due && (<p className="text-red-500"> {formatPrice.format(selectedItem.amount_due)} due by</p>)}
                      <p className="text-red-500"> {selectedItem.due_date
                        ? new Date(selectedItem.due_date).toLocaleDateString("en-US", {
                          year: "2-digit",
                          month: "2-digit",
                          day: "2-digit",
                          timeZone: "UTC", // Specify UTC timezone to prevent conversion
                        })
                      : ""}</p>
                      </div>
                    )}
                    {selectedItem.status === 4 && (
                      <p className=" text-blue-500">Pending</p>
                    )}
                  </div>
                  {selectedItem.Order.payment_id && (
                  <div>
                    
                    <p className="text-[#929791]">Payment Method</p>
                    <div className="flex flex-row items-center space-x-4">
                      <div>
                        <img
                          src="/visa.png"
                          className="w-[48.81px] h-[32.03px] mx-auto"
                        ></img>
                      </div>
                      <div className="my-[20px]">
                        <p className="text-[14px] ">
                          xxxx-xxxx-xxxx-{selectedItem.Order.Payment?.last_four}
                        </p>
                      </div>
                    </div>
                  </div>
                  )}
                </div>
                <hr className="h-px bg-gray-100 border-0 -mx-6" />

                <div className="pt-6">
                  <p className="text-2xl font-semibold">Order Summary</p>
                  <OrderSummary
                    selectedItem={selectedItem.Order}
                    onSubtotalChange={handleSubtotalChange}
                  />
                  <hr className="h-px bg-gray-100 border-0 -mx-6" />
                  <div className="grid grid-cols-9 gap-x-4 gap-y-2 py-6 font-light">
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Subtotal</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(orderSubtotal)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Shipping</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.Order.shipping)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Tax</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.Order.tax)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Discount</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        -{formatPrice.format(selectedItem.Order.discount)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3 font-semibold">
                      <p className="text-sm">Total</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.Order.total)}
                      </p>
                    </div>
                  </div>
                  {selectedItem.status === 3 ? (
                    <>
                      <hr className="h-px bg-gray-100 border-0 -mx-6" />
                      <div className="bg-white rounded-xl dark:bg-gray-800 py-4">
                        <div className="pt-3 -mb-3 flex gap-2 items-center text-center font-light text-sm">
                          <p>
                            {" "}
                            If paying by check please mail checks to:{" "}
                            <span className="font-semibold">
                              2035 E. Financial Way, Glendora CA 91741.
                            </span>{" "}
                            Make Checks Payable To{" "}
                            <span className="font-semibold">OASIS Billing</span>
                            , and put your{" "}
                            <span className="font-semibold">
                              Invoice and Account Number
                            </span>{" "}
                            in the Memo
                          </p>
                        </div>
                      </div>
                    </>
                  ) : (
                    ""
                  )}
                </div>
              </Modal.Body>
              <Modal.Footer>
                <BsPrinter
                  onClick={() => {
                    // Add the "modal-print" class to the modal element
                    const modal = document.querySelector(".modal-content");
                    console.log(modal);
                    modal.classList.add("modal-print");

                    // Trigger the print action
                    window.print();
                    console.log(modal);

                    // Remove the "modal-print" class to revert to normal display
                    modal.classList.remove("modal-print");
                  }}
                  className="cursor-pointer"
                />
              </Modal.Footer>
            </>
          )}
        </Modal>
      </>
    );
  }
}

export default InvoiceTable;

function Row({ data, handleOpenModal }) {
  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  const inputDate = new Date(data.Order.date);
  const options = { year: "2-digit", month: "numeric", day: "numeric" };
  const formattedDate = inputDate.toLocaleDateString("en-US", options);
  return (
    <Table.Row
      className="bg-white border-b font-light text-gray-900 dark:bg-gray-800 dark:border-gray-700 text-center"
      key={data.id}
    >
      {/* <Table.Cell className="px-6 py-4">
        <Checkbox className="" />
      </Table.Cell> */}
      <Table.Cell className="px-6 py-4 font-light text-gray-900 whitespace-nowrap dark:text-white">
        {formattedDate}
      </Table.Cell>
      <Table.Cell className="px-6 py-4">
        <p className="font-bold">
          {data.Order.User.first_name} {data.Order.User.last_name}
        </p>
        <p className="font-light text-gray-500">
          {" "}
          {data.Order.Locations.practice}
        </p>
      </Table.Cell>
      <Table.Cell className="px-6 py-4">{data.invoice_num}</Table.Cell>
      <Table.Cell className="px-6 py-4">{data.Order.jde_id}</Table.Cell>
      <Table.Cell className="px-6 py-4">
        <div className="flex items-center justify-center">
        {data.type === "web" && <FaDesktop size="20px" />}
        {data.type === "phone" && <FaPhoneAlt size="20px" />}
        </div>
      </Table.Cell>
      {/* <Table.Cell className="px-6 py-4">
      <div className="flex items-center justify-center">
        <FaCheckCircle color="#27AE60" size="20px"/>
        </div>
      </Table.Cell> */}
      <Table.Cell className="px-6 py-4">
        {formatPrice.format(data.Order.total)}
      </Table.Cell>
      <Table.Cell className="px-6 py-4">
        {data.status === 1 && (
          <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md">
            Paid
          </p>
        )}
        {data.status === 2 && (
          <p className="text-center font-semibold p-2 text-yellow-500 bg-yellow-100 rounded-md">
            Due
          </p>
        )}
        {data.status === 3 && (
          <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
            Past Due
          </p>
        )}
        {data.status === 4 && (
          <p className="text-center font-semibold p-2 text-blue-500 bg-blue-100 rounded-md">
            Pending
          </p>
        )}
      </Table.Cell>
      <Table.Cell className="px-2 py-4 text-center">
        <Button
          onClick={() => handleOpenModal(data)}
          color="transparent"
          className="text-[#08447C] hover:text-blue-500 block mx-auto"
        >
          View
        </Button>
      </Table.Cell>
    </Table.Row>
  );
}

function OrderSummary({ selectedItem, onSubtotalChange }) {
  // Use the selectedItem prop to access the data
  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });
  // Calculate subtotal
  const subtotal = selectedItem.OrderedProduct.reduce((total, product) => {
    return total + product.price * product.qty;
  }, 0);

  useEffect(() => {
    onSubtotalChange(subtotal);
  }, [subtotal, onSubtotalChange]);

  return (
    <div className="font-light">
      {selectedItem &&
        selectedItem.OrderedProduct.map((product) => (
          <div className="grid grid-cols-9 gap-4 py-3" key={product.product_id}>
            <div className="flex-shrink-0 col-span-1">
              <img
                className="w-11 h-11 sqaure"
                src={product.Product.ProductImage && product.Product.ProductImage.length < 1
                  ? "/no_image.jpeg"
                  : `${
                  process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                  "oasis-product-images/"
                }${product.Product.ProductImage[0].path}_s.${
                  product.Product.ProductImage[0].extension
                }`}
                alt=""
              />
            </div>
            <div className="text-black font-semibold text-sm mb-1.5 col-span-4">
              {product.Product.name}
              <div className="text-xs font-normal text-gray-400 mt-2">
                {product.Product.sku}
              </div>
            </div>
            <div className="justify-self-center col-span-2">
              <p className="text-sm">Qty. {product.qty}</p>
            </div>
            <div className="justify-self-end col-span-2">
              <p className="text-sm">
                {formatPrice.format(product.price)}
              </p>
            </div>
          </div>
        ))}
    </div>
  );
}
