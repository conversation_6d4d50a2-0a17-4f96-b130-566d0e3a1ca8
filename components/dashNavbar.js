import { use, useEffect, useState } from "react";
import Link from "next/link";
import { useRouter } from "next/router";
import { useSession, signOut } from "next-auth/react";
import {
  Sidebar,
  Navbar,
  TextInput,
  Checkbox,
  Button,
  Dropdown,
  Avatar,
  Badge,
  Modal,
  Alert,
  Table,
  Accordion,
} from "flowbite-react";
import { HiOutlineSearch } from "react-icons/hi";
import { FaStore, FaBell, FaUserFriends } from "react-icons/fa";
import axios from "axios";
import { useCart } from "../contexts/CartContext";
import { useUserAcctContext } from "../contexts/UserAcctContext";

import {
  MdNotifications,
  MdLocalGroceryStore,
  MdQuestionAnswer,
  MdOutlineLogin,
  MdVideoLibrary,
  MdGroup,
  MdOutlineSettings,
  MdOutlineGarage,
} from "react-icons/md";
import Cart from "./dashNav/cart";
import SearchModal from "./dashNav/searchModal";
// import Cart from "./dashNav/cart";

export default function DashNavbar() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const { userAcct, setUserAcct } = useUserAcctContext();
  const { cart: cartContext, updateCart, setCart: setCartContext } = useCart();
  const [locations, setLocations] = useState([]);
  const [searchResults, setSearchResults] = useState({
    products: [],
    orders: [],
    invoices: [],
    filtered: false,
  });
  const [searchModal, setSearchModal] = useState(false);
  // const [showNotifications, setShowNotifications] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const [gotLocations, setGotLocations] = useState(0);
  const [openCart, setOpenCart] = useState(false);
  const [cartAlert, setCartAlert] = useState({
    show: false,
    message: "",
    color: "",
    icon: "",
  });

  function logoutHandler() {
    signOut({ callbackUrl: "/" });
  }

  const updateUserAcct = () => {
    if (session) {
      const encodedEmail = encodeURIComponent(session.user.email);

      axios
        .get(`/api/user/getUserInfo?email=${encodedEmail}`)
        .then((response) => {
          const data = response.data;
          const tc =
            data.Account.territory_code && data.Account.oasis_rep_id
              ? true
              : false;
          setUserAcct({
            ...userAcct,
            user: {
              id: data.id,
              first_name: data.first_name,
              last_name: data.last_name,
              email: data.email,
              location_id: data.location_id,
              account_id: data.account_id,
              primary_user: data.primary_user,
              group_id: data.group_id,
              phone: data.phone,
              position: data.position,
              address: data.address,
              address_l2: data.address_2,
              city: data.city,
              state: data.state,
              zip: data.zip,
            },
            account: {
              id: data.Account.id,
              jde_id: data.Account.jde_id,
              business_name: data.Account.business_name,
              approved: data.Account.approved,
              territory_code: tc ? data.Account.territory_code : "",
              oasis_rep_id: tc ? data.Account.oasis_rep_id : "",
              oasis_rep_name: tc ? data.Account.OasisRep.name : "",
              oasis_rep_email: tc ? data.Account.OasisRep.email : "",
              oasis_rep_phone: tc ? data.Account.OasisRep.phone : "",
            },
          });
          // console.log(userAcct);
        })
        .catch((error) => {
          // Handle error
          console.error("Error updating user account:", error);
        });
    }
  };

  const getCart = () => {
    if (session) {
      axios
        .get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        )
        .then((response) => {
          const updatedCart = response.data.items;
          updateCart(updatedCart);
        });
    }
  };

  const getLocations = () => {
    const account_id = session.user.account_id;
    const user_id = session.user.id;
    axios
      .get("/api/location/get", {
        params: {
          user_id: user_id,
          account_id: account_id,
          location_id: session["user"]["location_id"],
          user_group: session["user"]["group_id"],
        },
      })
      .then((response) => {
        setLocations(response.data);
        // console.log(userAcct.user.group_id);

        setGotLocations(1);
      });
    // console.log("set location", locations);
  };

  const updateQty = (e) => {
    // console.log("updateCartQty Running...");
    if (session) {
      axios
        .get(
          `/api/getCart?account=${session.user.account_id}&user_id=${session.user.id}`
        )
        .then((response) => {
          const updatedCart = response.data.items;
          updateCart(updatedCart);
        });
    }
  };

  const handleGlobalSearch = (e) => {
    e.preventDefault();
    // console.log(e.target[0].value);

    const query = e.target[0].value;

    axios
      .get("/api/globalSearch/getGlobalSearchResults", {
        params: {
          query: query,
          user_id: session.user.id,
          group_id: session.user.group_id,
          location_id: session.user.location_id,
          account_id: session.user.account_id,
        },
      })
      .then((response) => {
        const results = response.data;
        console.log(results);
        setSearchResults({
          products: results.products ? results.products : [],
          orders: results.orders ? results.orders : [],
          invoices: results.invoices ? results.invoices : [],
        });
        setSearchModal(true);
      });
  };

  function clearInputField() {
    // Assuming your input field has an ID, replace 'yourInputFieldId' with the actual ID of your input field
    var inputField = document.getElementById("modal-search");
    var topBarField = document.getElementById("topbar-search");

    // Check if the input field exists
    if (inputField) {
      // Clear the input field value
      inputField.value = "";
    }
    if (topBarField) {
      // Clear the input field value
      topBarField.value = "";
    }
  }

  const extractTextFromHTML = (htmlContent) => {
    const doc = new DOMParser().parseFromString(htmlContent, "text/html");
  
    const extractText = (node) => {
      let text = "";
  
      if (node.nodeType === Node.TEXT_NODE) {
        text += node.nodeValue;
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        for (let childNode of node.childNodes) {
          text += extractText(childNode);
        }
  
        if (node.tagName === "P" || node.tagName === "DIV" || node.tagName === "BR") {
          text += "\n";
        } else if (node.tagName === "LI" || node.tagName === "TD") {
          text += " ";
        }
      }
  
      return text;
    };
  
    let finalText = "";
    for (let node of doc.body.childNodes) {
      finalText += extractText(node);
    }
  
    return finalText.trim();
  };

  const fetchNotifications = async () => {
    try {
      const user_id = session.user.id;
        const response = await axios.get(`/api/userNotifications/getUnopened?id=${user_id}`);
      setNotifications(response.data);
    } catch (error) {
      console.error("Error fetching notifications:", error);
    }
  };

  useEffect(() => {
    updateUserAcct();
    getLocations();
    getCart();
    fetchNotifications();


    console.log("search results", searchResults);
  }, [session]);

  // console.log("cart", cart);
  // console.log("nav cart", cartContext);

  if (session && userAcct.user && gotLocations) {
    return (
      <header className="row-span-1">
        <script
          src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.6.5/flowbite.min.js"
          defer
        ></script>
        <nav className="bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
          <div className="flex flex-wrap justify-between items-center">
            <div className="flex justify-start items-center">
              <div className="space-y-1 dark:text-white">
                <div className="font-medium  text-gray-500 dark:text-gray-400">
                  Hi {userAcct.user && userAcct.user.first_name},
                </div>
                <div>
                  Acct#
                  {/* {(userAcct.user && userAcct.user.group_id == 2) ||
                  (userAcct.user && userAcct.user.group_id == 4)
                    ? userAcct.account.jde_id
                    : locations.item.jde_id} */}
                  {(session.user.group_id == 2 || session.user.group_id == 4) &&
                  session.user.location_id
                    ? session.user.Locations.jde_id
                    : session.user.Account.jde_id}
                </div>
              </div>
            </div>
            <div className="flex lg:order-2">
              <form
                // action="#"
                // method="GET"
                className="hidden lg:block pl-2 mr-8"
                onSubmit={handleGlobalSearch}
              >
                <label htmlFor="topbar-search" className="sr-only">
                  Search
                </label>
                <div className="relative mt-1 lg:w-96">
                  <div className="flex absolute inset-y-0 left-0 items-center pl-3 pointer-events-none">
                    <svg
                      className="w-5 h-5 text-gray-400 dark:text-gray-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                        clipRule="evenodd"
                      ></path>
                    </svg>
                  </div>
                  <input
                    type="text"
                    name="query"
                    id="topbar-search"
                    className="bg-neutral-200 text-gray-400 border-0 sm:text-sm rounded-lg block w-full pl-10 p-3.5 dark:bg-gray-700 "
                    placeholder="Search"
                  />
                </div>
              </form>
              <Modal
                size="6xl"
                dismissible
                show={searchModal}
                onClose={() => {
                  setSearchModal(false);
                  setSearchResults({
                    products: [],
                    orders: [],
                    invoices: [],
                    filtered: false,
                  });
                  clearInputField();
                }}
              >
                <SearchModal
                  searchResults={searchResults}
                  setSearchResults={setSearchResults}
                  setSearchModal={setSearchModal}
                  clearInputField={clearInputField}
                />
              </Modal>

              <Dropdown
                label={
                  <Avatar
                    alt="User settings"
                    placeholderInitials={
                      userAcct.user.first_name &&
                      userAcct.user.first_name[0] + userAcct.user.last_name[0]
                    }
                    rounded={true}
                  >
                    <div className="space-y-0.5 mr-8 font-medium dark:text-white flex flex-col items-start">
                      <div>
                        {userAcct.user && userAcct.user.first_name}{" "}
                        {userAcct.user && userAcct.user.last_name}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {userAcct.user &&
                        (userAcct.user.group_id == 1 ||
                          userAcct.user.location_id == null)
                          ? userAcct.account.business_name
                          : locations.item.practice}
                        {/* locations.item.find(
                              (location) =>
                                location.id === userAcct.user.location_id
                            )?.practice} */}
                        {/* {session.user && session.user.Account.business_name} */}
                      </div>
                    </div>
                  </Avatar>
                }
                inline={true}
                className="mr-3 rounded-xl"
              >
                <Dropdown.Header>
                  <div className="flex items-center">
                    <Avatar
                      alt="User settings"
                      placeholderInitials={
                        userAcct.user.first_name &&
                        userAcct.user.first_name[0] + userAcct.user.last_name[0]
                      }
                      rounded={true}
                    >
                      <div className="space-y-0.5 font-semibold dark:text-white">
                        <div>
                          {userAcct.user && userAcct.user.first_name}{" "}
                          {userAcct.user && userAcct.user.last_name}
                        </div>
                        {/* <div className="text-xs text-gray-500 dark:text-gray-400">
                          {userAcct.user &&
                          (userAcct.user.group_id == 1 ||
                            userAcct.user.location_id == null)
                            ? userAcct.account.business_name
                            : locations.item.find(
                                (location) =>
                                  location.id === userAcct.user.location_id
                              )?.practice}
                        </div> */}
                      </div>
                    </Avatar>
                  </div>
                </Dropdown.Header>
                <Dropdown.Item className="text-left">
                  {userAcct.user && userAcct.user.position}
                  <br />
                  {userAcct.user && userAcct.user.address && (
                    <>
                      {userAcct.user.address}{" "}
                      {userAcct.user.address_l2 &&
                        `${userAcct.user.address_l2}, `}
                      {userAcct.user.city && `${userAcct.user.city}, `}
                      {userAcct.user.state && `${userAcct.user.state} `}
                      {userAcct.user.zip && userAcct.user.zip}
                    </>
                  )}
                  <br />
                  {userAcct.user && userAcct.user.phone && (
                    <>
                      {`(${userAcct.user.phone.slice(0, 3)}) `}
                      {`${userAcct.user.phone.slice(3, 6)}-`}
                      {userAcct.user.phone.slice(6)}
                    </>
                  )}
                </Dropdown.Item>
                <Dropdown.Divider />
                <Dropdown.Item onClick={logoutHandler}>Log Out</Dropdown.Item>
              </Dropdown>

              {/* <button
                type="button"
                className="p-2 mr-3 ml-3 text-gray-500 text-center rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
              >
                <span className="sr-only">View chat</span>
                <MdQuestionAnswer className="h-5 w-5" />
              </button> */}

              <Dropdown
                label={
                  <button
                  type="button"
                  className="p-2 mx-3 h-full text-gray-500 text-center rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
                >
                  <span className="sr-only">View notifications</span>
                    <div className="relative">
                    <MdNotifications className="h-5 w-5" />
                    {notifications && notifications.length > 0 ? (
                      <Badge className="bg-red-500 text-white text-[10px] rounded-full p-1 justify-center h-4 w-4 absolute -top-2.5 -right-1.5">
                        {notifications && notifications.length}
                      </Badge>
                    ) : (
                      <></>
                    )}
                  </div>
                </button>
                }
                arrowIcon = {false}
                inline={true}
                className="max-w-md"
              >
              <Dropdown.Header>
                <div className="flex items-start">
                <MdNotifications className="h-5 w-5 text-gray-500" />
                  <span className="font-semibold ml-2">Notifications</span>
                </div>
              </Dropdown.Header>
              
                {notifications.length > 0 ? (
                  notifications.map((notification) => (
                    <>
                    <Dropdown.Item className="px-3 py-2" onClick={() => {
                      // Redirect to the notification URL when the item is clicked
                      router.push(`/inbox?id=${notification.id}`);
                    }}>
                    <div key={notification.id} className="flex items-start space-x-4 grow ">
                      <Avatar className="shrink-0" alt="Notification" img={process.env.NEXT_PUBLIC_STAGING_BASE_URL + notification.Notifications.image_path} size="lg">
                      </Avatar>
                      <div className="text-start flex flex-col grow h-full">
                        <p className="font-bold">{notification.Notifications.headline}</p>
                        <p className="line-clamp-2 leading-tight text-sm mt-1 ">
                        {extractTextFromHTML(
                          notification.Notifications.body
                        )}
                        </p>
                        <Link href={`/inbox?id=${notification.id}`}
                        className="text-blue-500 text-md font-normal float-right mt-1 self-end justify-self-end">
                          Open
                        </Link>
                      </div>
                    </div>
                    </Dropdown.Item>
                    <Dropdown.Divider />
                    </>
                  ))
                ) : (
                  <>
                  <Dropdown.Item>
                    <p className="text-sm">No new notifications</p>
                  </Dropdown.Item>
                  <Dropdown.Divider />
                  </>
                )}
                <Dropdown.Item className="text-[#08447C] text-md font-medium justify-center" onClick={() => {
                  // Redirect to the Inbox URL when "Go to Inbox" is clicked
                  router.push('/inbox');
                }}
                >Go to Inbox</Dropdown.Item>
            </Dropdown>

              <button
                type="button"
                data-dropdown-toggle="cart-dropdown"
                onClick={() => {
                  getCart();
                  setOpenCart(true);
                }}
                className="p-2 text-gray-500 rounded-lg hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-700 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600"
              >
                <span className="sr-only">Open Mini Cart</span>
                <div className="relative">
                  <MdLocalGroceryStore className="h-5 w-5" />
                  {cartContext && cartContext.length > 0 ? (
                    <Badge className="bg-red-500 text-white text-[10px] rounded-full p-1 justify-center h-4 w-4 absolute -top-2.5 -right-1.5">
                      {cartContext && cartContext.length}
                    </Badge>
                  ) : (
                    <></>
                  )}
                </div>
              </button>

              <Modal
                show={openCart}
                position={"top-right"}
                onClose={() => setOpenCart(false)}
                size={"4xl"}
                dismissible
              >
                <Modal.Header>Shopping Cart</Modal.Header>
                <Modal.Body>
                  <div
                    className=" z-50 text-base list-none bg-white"
                    id="cart-dropdown"
                  >
                    <Badge
                      color={cartAlert.color}
                      size="xs"
                      className="float-right"
                    >
                      {cartAlert.message}
                    </Badge>

                    <div className="block pb-6  text-[#929791] text-left font-[14px]">
                      Your Shopping Cart Items
                    </div>
                    <hr className="h-px bg-gray-200 border-0 -mx-6" />
                    <div>
                      {cartContext.length > 0 ? (
                        cartContext.map((value, index) => (
                          <Cart
                            data={value}
                            key={index}
                            index={index}
                            cart={cartContext}
                            onUpdate={updateQty}
                            cartAlert={cartAlert}
                            setCartAlert={setCartAlert}
                          />
                        ))
                      ) : (
                        <h1>Your cart is empty</h1>
                      )}
                    </div>
                  </div>
                </Modal.Body>
                <Modal.Footer>
                  <div className="w-full">
                    <Link
                      href="/cart"
                      className="bg-primary text-white py-3 px-12 rounded-full float-right hover:bg-primary-dark focus:ring-0 focus:ring-transparent"
                    >
                      Check Out
                    </Link>
                  </div>
                </Modal.Footer>
              </Modal>
            </div>
          </div>
        </nav>
      </header>
    );
  }
}
