function pastOrdersModal(
  selectedItem,
  handleSubtotal<PERSON>hange,
  isExpanded,
  toggleAccordion
) {
  <>
    <Modal.Header>Past Order Details</Modal.Header>
    <Modal.Body>
      {/* Display item details using selectedItem */}
      <div className="grid grid-cols-4 gap-4 pb-6">
        <div>
          <p className="text-[#929791]">Date</p>
          <p>
            {selectedItem.date
              ? new Date(selectedItem.date).toLocaleDateString("en-US", {
                  year: "2-digit",
                  month: "numeric",
                  day: "numeric",
                })
              : ""}
          </p>
        </div>
        <div>
          <p className="text-[#929791]">Order Number</p>
          <p>{selectedItem.jde_id}</p>
        </div>
        <div>
          <p className="text-[#929791]">Tracking</p>
          <p>
            {selectedItem.tracking ? (
              <Link
                href={`https://www.fedex.com/fedextrack/no-results-found?trknbr=${selectedItem.tracking}`}
                target="_blank"
              >
                {selectedItem.tracking}
              </Link>
            ) : null}
          </p>
        </div>
        <div>
          <p className="text-[#929791]">Status</p>
          {selectedItem.status === 1 && <p>Submitted</p>}
          {selectedItem.status === 2 && <p>In Progress</p>}
          {selectedItem.status === 3 && <p>Shipped</p>}
          {selectedItem.status === 4 && <p>Delivered</p>}
          {selectedItem.status === 9 && <p>Cancelled</p>}
        </div>
      </div>
      <hr className="h-px bg-gray-100 border-0 -mx-6" />
      <div className="grid grid-cols-3 gap-4 py-6 font-light">
        <div>
          <p className="text-[#929791]">Shipping Address</p>
          <p>{selectedItem.Locations.practice}</p>
          <p>
            {selectedItem.Locations.address} {selectedItem.Locations.address_2}
          </p>
          <p>
            {selectedItem.Locations.city}, {selectedItem.Locations.state}{" "}
            {selectedItem.Locations.zip}
          </p>
        </div>
        <div>
          <p className="text-[#929791]">Billing Address</p>
          <p>{session.user.Account.business_name}</p>
          <p>
            {session.user.Account.address} {session.user.Account.address_2}
          </p>
          <p>
            {session.user.Account.city}, {session.user.Account.state}{" "}
            {session.user.Account.postal_code}
          </p>
        </div>
        {selectedItem.Payment ? (
          <div>
            <p className="text-[#929791]">Payment Method</p>
            <div className="flex flex-row items-center space-x-4">
              <div>
                <img
                  src="/visa.png"
                  className="w-[48.81px] h-[32.03px] mx-auto"
                ></img>
              </div>
              <div className="my-[20px]">
                <p className="text-[14px] ">
                  xxxx-xxxx-xxxx-{selectedItem.Payment.last_four}
                </p>
              </div>
            </div>
          </div>
        ) : (
          ""
        )}
      </div>
      <hr className="h-px bg-gray-100 border-0 -mx-6" />

      <div className="pt-6">
        <p className="text-2xl font-semibold">Order Summary</p>
        <OrderSummary
          selectedItem={selectedItem}
          onSubtotalChange={handleSubtotalChange}
        />
        <hr className="h-px bg-gray-100 border-0 -mx-6" />
        <div className="grid grid-cols-9 gap-x-4 gap-y-2 py-6 font-light">
          <div className="justify-self-start col-start-5 col-span-3">
            <p className="text-sm">Subtotal</p>
          </div>
          <div className="justify-self-end cols-start-7 col-span-2">
            <p className="text-sm">{formatPrice.format(orderSubtotal)}</p>
          </div>
          <div className="justify-self-start col-start-5 col-span-3">
            <p className="text-sm">Discount</p>
          </div>
          <div className="justify-self-end cols-start-7 col-span-2">
            <p className="text-sm">
              -{formatPrice.format(selectedItem.discount)}
            </p>
          </div>
          <div className="justify-self-start col-start-5 col-span-3">
            <p className="text-sm">Tax</p>
          </div>
          <div className="justify-self-end cols-start-7 col-span-2">
            <p className="text-sm">{formatPrice.format(selectedItem.tax)}</p>
          </div>
          <div className="justify-self-start col-start-5 col-span-3">
            <p className="text-sm">Shipping</p>
          </div>
          <div className="justify-self-end cols-start-7 col-span-2">
            <p className="text-sm">
              {formatPrice.format(selectedItem.shipping)}
            </p>
          </div>

          <div className="justify-self-start col-start-5 col-span-3 font-semibold">
            <p className="text-sm">Total</p>
          </div>
          <div className="justify-self-end cols-start-7 col-span-2">
            <p className="text-sm">{formatPrice.format(selectedItem.total)}</p>
          </div>
        </div>
        <hr className="h-px bg-gray-100 border-0 -mx-6" />
        <div className="bg-white rounded-xl dark:bg-gray-800 ">
          <div
            className="pt-3 -mb-3 flex gap-2 items-center"
            onClick={toggleAccordion}
          >
            <h1 className="text-[#353535] font-semibold text-[20px]">
              Auto Delivery
            </h1>
            <FaChevronDown color="#929791" size="15px" />
          </div>
          {isExpanded && (
            <div>
              <div className="flex flex-row gap-4 justify-start mt-7">
                <div>
                  <Button
                    color={`${
                      parseInt(selectedItem.auto_freq) === 0 ? "" : "light"
                    }`}
                    className={`w-[156.25px] h-11  ${
                      parseInt(selectedItem.auto_freq) === 0
                        ? "bg-[#4BB5EA] text-white"
                        : ""
                    }`}
                    style={{ pointerEvents: "none" }}
                  >
                    None
                  </Button>
                </div>
                <div>
                  <Button
                    color={`${
                      parseInt(selectedItem.auto_freq) === 1 ? "" : "light"
                    }`}
                    className={`w-[156.25px] h-11  ${
                      parseInt(selectedItem.auto_freq) === 1
                        ? "bg-[#4BB5EA] text-white"
                        : ""
                    }`}
                    style={{ pointerEvents: "none" }}
                  >
                    Every Month
                  </Button>
                </div>
                <div>
                  <Button
                    color={`${
                      parseInt(selectedItem.auto_freq) === 2 ? "" : "light"
                    }`}
                    className={`w-[156.25px] h-11  ${
                      parseInt(selectedItem.auto_freq) === 2
                        ? "bg-[#4BB5EA] text-white"
                        : ""
                    }`}
                    style={{ pointerEvents: "none" }}
                  >
                    Every 2 Months
                  </Button>
                </div>
                <div>
                  <Button
                    color={`${
                      parseInt(selectedItem.auto_freq) === 3 ? "" : "light"
                    }`}
                    className={`w-[156.25px] h-11  ${
                      parseInt(selectedItem.auto_freq) === 3
                        ? "bg-[#4BB5EA] text-white"
                        : ""
                    }`}
                    style={{ pointerEvents: "none" }}
                  >
                    Every 3 Months
                  </Button>
                </div>
              </div>
              {selectedItem.auto_delivery === 1 && (
                <div className="flex flex-row justify-start gap-x-6 pt-6 font-light">
                  <div className="">
                    <p className="text-[#929791]">Delivery Start Date</p>
                    <p>
                      {selectedItem.date
                        ? new Date(selectedItem.date).toLocaleDateString(
                            "en-US",
                            {
                              year: "2-digit",
                              month: "numeric",
                              day: "numeric",
                            }
                          )
                        : ""}
                    </p>
                  </div>
                  <div className="">
                    <p className="text-[#929791]">Next Scheduled Delivery</p>
                    <p>
                      {selectedItem.date
                        ? new Date(selectedItem.date).toLocaleDateString(
                            "en-US",
                            {
                              year: "2-digit",
                              month: "numeric",
                              day: "numeric",
                            }
                          )
                        : ""}
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </Modal.Body>
    <Modal.Footer>
      <BsPrinter
        onClick={() => {
          // Add the "modal-print" class to the modal element
          const modal = document.querySelector(".modal-content");
          console.log(modal);
          modal.classList.add("modal-print");

          // Trigger the print action
          window.print();
          console.log(modal);

          // Remove the "modal-print" class to revert to normal display
          modal.classList.remove("modal-print");
        }}
        className="cursor-pointer"
      />
      {/* <Button color="gray" onClick={() => setOpenOrder(false)}>
                Decline
              </Button> */}
    </Modal.Footer>
  </>;
}

export default pastOrdersModal;
