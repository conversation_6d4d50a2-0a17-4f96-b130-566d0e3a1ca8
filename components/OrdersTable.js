import { MdAddCircle } from "react-icons/md";
import { FaCheckCircle, FaRegCircle, FaChevronDown } from "react-icons/fa";
import { FaArrowsRotate } from "react-icons/fa6";
import { Bs<PERSON>rinter } from "react-icons/bs";
import {
  Button,
  Modal,
  Dropdown,
  Badge,
  Spinner,
  Accordion,
  Table,
  Tooltip
} from "flowbite-react";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { useState, useEffect } from "react";
import { useDate } from "../contexts/DateContext";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import pastOrdersModal from "./orders/pastOrdersModal";
import recurringOrdersModal from "./orders/recurringOrdersModal";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import axios from "axios";

<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.7.0/datepicker.min.js"></script>;

function OrdersTable({
  data,
  refreshOrders,
  fetchingOrders,
  lastFetched,
  recurring,
}) {
  const { startDate, endDate, handleStartDateChange, handleEndDateChange } =
    useDate();
  const { data: session, status } = useSession();
  const { notifications, addNotifications } = useGlobalAlertContext();
  const [openOrder, setOpenOrder] = useState(false);
  const [openRecurringOrder, setOpenRecurringOrder] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [orderSubtotal, setOrderSubtotal] = useState(0);
  const [cancelProccess, setCancelProccess] = useState(false);
  const [skipProccess, setSkipProccess] = useState(false);


  const trackingURLs = {
    FedEx: ['C1F', 'C2A', 'C2D', 'C3D', 'CFP', 'CFS', 'CGN', 'CIE', 'CIP', 'CSA', 'CTN', 'F14', 'FDX', 'FGN', 'FGR', 'FX3', 'FXG', 'N1A', 'N1P', 'N2A', 'N2B', 'N2D', 'N3D', 'NFS', 'NGN', 'NST', 'P1P', 'P1S', 'P2A', 'P2D', 'P3D', 'PFG', 'PFS', 'PI3', 'PIP', 'PST', 'R10', 'R11', 'R15', 'R17', 'R18', 'R20', 'R21', 'R26', 'R27', 'R2D', 'R3D', 'R3Y', 'R95', 'RG7', 'WF2', 'WFG', 'WFR'],
    UPS: ['C8A', 'CU1', 'CU2', 'CU3', 'CUG', 'CUI', 'CUN', 'NU3', 'NUG', 'P1A', 'PU2', 'PU3', 'PUA', 'PUG', 'PUI', 'PUN', 'WGP', 'WU2', 'WUG', 'WUP']
  };

  const getShippingProvider= (shippingMethod) => {
      const provider = Object.keys(trackingURLs).find(provider => trackingURLs[provider].includes(shippingMethod));
      return provider || 'neither';
  };

  // Helper function to get shipping method
  const getShippingMethod = (shippingMethod) => {
    const provider = getShippingProvider(shippingMethod);

    if (provider === 'FedEx') {
      // Handle specific FedEx codes like FXG for FedEx Ground
      if (shippingMethod === 'FXG') {
        return 'FedEx Ground';
      } else if (shippingMethod === 'P2D'){
        return 'FedEx 2Day';
      } else if (shippingMethod === 'P3D'){
        return 'FedEx Express Saver';
      }
      return 'FedEx';
    } else if (provider === 'UPS') {
      // Handle specific UPS codes if needed (example given for PUA as 'UPS Air')
      if (shippingMethod === 'CUG') {
        return 'UPS Ground';
      } else if (shippingMethod === 'CU2'){
        return 'UPS 2nd Day Air';
      } else if (shippingMethod === 'CU3'){
        return 'UPS 3 Day Select';
      }
      return 'UPS';
    }
    return 'Unknown Shipping Method';
  };

  const handleOpenModal = (item) => {
    setSelectedItem(item);
    setOpenOrder(true);
    console.log("item", item);
  };
  const handleOpenRecurringModal = (item) => {
    setSelectedItem(item);
    setOpenRecurringOrder(true);
    console.log("item", item);
  };

  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });

  const handleSubtotalChange = (subtotal) => {
    setOrderSubtotal(subtotal);
  };

  const [isExpanded, setIsExpanded] = useState(false);

  const toggleAccordion = () => {
    setIsExpanded((prevIsExpanded) => !prevIsExpanded);
  };


  const formatDate = (date) => {
    const inputDate = new Date(date);
    const options = {
      year: "2-digit",
      month: "numeric",
      day: "2-digit",
      timeZone: "UTC",
    };
    return inputDate.toLocaleDateString("en-US", options);
  };

  const calculateScheduledDate = (item) => {
    const orderDate = new Date(item.date);
  
    let inputDate;

    if (item.auto_start_date) {
      inputDate = new Date(item.auto_start_date);
    } else if (item.auto_freq >= 1 && item.auto_freq <= 3) {
      inputDate = new Date(orderDate); // Avoid modifying the original date
      inputDate.setMonth(inputDate.getMonth() + item.auto_freq);

      // Handle month overflow due to date shifting
      if (inputDate.getMonth() !== (orderDate.getMonth() + item.auto_freq) % 12) {
        inputDate.setFullYear(inputDate.getFullYear() + 1);
      }
    } else {
      inputDate = orderDate;
    }

    // Format the date
    const options = {
      year: "2-digit",
      month: "numeric",
      day: "2-digit",
    };

    return inputDate.toLocaleDateString("en-US", options);
  };

  const successNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: message,
      show: true,
    };
    addNotifications(newNotification);
  };

  const failureNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };

  async function cancelUpcoming(id) {
    try {
      // Call the API to skip the next order
      setSkipProccess(true);
      await axios.post("/api/order/skipRecurring", {
        id,
      });
  
      // On success, show notification
      setOpenRecurringOrder(false)
      setSkipProccess(false);
      successNotification('Successfully cancelled upcoming order.');
      handleEndDateChange(endDate);
    } catch (error) {
      // Handle errors and show failure notification
      setSkipProccess(false);
      console.error("Error skipping the next order:", error);
      failureNotification(
        error.response?.data?.message || "Failed to skip the order. Please try again."
      );
    }

  }

  async function cancelRecurring(id) {
    try {
      // Call the API to skip the next order
      setCancelProccess(true);
      await axios.post("/api/order/cancelRecurring", {
        id,
      });
  
      // On success, show notification
      setOpenRecurringOrder(false)
      setCancelProccess(false);
      successNotification('Successfully cancelled future recurring orders.');
      handleEndDateChange(endDate);
    } catch (error) {
      // Handle errors and show failure notification
      setCancelProccess(false);
      console.error("Error skipping the next order:", error);
      failureNotification(
        error.response?.data?.message || "Failed to cancel auto delivery. Please try again."
      );
    }

  }

  async function resumeUpcoming(id) {
    try {
      // Call the API to skip the next order
      setSkipProccess(true);
      await axios.post("/api/order/resumeRecurring", {
        id,
      });
  
      // On success, show notification
      setOpenRecurringOrder(false)
      setSkipProccess(false);
      successNotification('Successfully resumed upcoming orders.');
      handleEndDateChange(endDate);
    } catch (error) {
      // Handle errors and show failure notification
      setSkipProccess(false);
      console.error("Error resuming auto delivery:", error);
      failureNotification(
        error.response?.data?.message || "Failed to resume auto delivery. Please try again."
      );
    }

  }

  async function restartRecurring(id) {
    try {
      // Call the API to skip the next order
      setCancelProccess(true);
      await axios.post("/api/order/restartRecurring", {
        id,
      });
  
      // On success, show notification
      setOpenRecurringOrder(false)
      setCancelProccess(false);
      successNotification('Successfully restarted auto delivery.');
      handleEndDateChange(endDate);
    } catch (error) {
      // Handle errors and show failure notification
      setCancelProccess(false);
      console.error("Error restarting auto delivery:", error);
      failureNotification(
        error.response?.data?.message || "Failed to restart auto delivery. Please try again."
      );
    }

  }
  

  const cardImage = (type) => {
    switch (type) {
      case "VISA":
        return "/visa.png";
      case "MC":
        return "/mc.png";
      case "AMEX":
        return "/amex.png";
      case "DISC":
        return "/discover.png";

      default:
        return "/visa.png";
    }
  };

  if (session) {
    return (
      <>
        <table className="w-full text-sm text-center text-gray-500 dark:text-gray-400">
          <caption className="p-8 text-2xl font-semibold text-left text-gray-900 bg-white dark:text-white dark:bg-gray-800">
            <div className="flex justify-between">
              <div className="">Orders</div>
              
                <div className="flex justify-center text-3xl">
                  <span className="mx-3 my-3 text-gray-500 text-sm">From</span>
                  <DatePicker
                    dateFormat="MMMM d, yyyy"
                    placeholderText="Start Date"
                    selected={startDate}
                    onChange={handleStartDateChange}
                    selectsStart
                    startDate={startDate}
                    endDate={endDate}
                    className="text-center bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  />
                  <span className="mx-4 my-3 text-gray-500 text-sm">To</span>
                  <DatePicker
                    dateFormat="MMMM d, yyyy"
                    placeholderText="End Date"
                    selected={endDate}
                    onChange={handleEndDateChange}
                    selectsEnd
                    startDate={startDate}
                    endDate={endDate}
                    minDate={startDate}
                    className="text-center bg-gray-50 border border-gray-300 text-gray-900 rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5  dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                  />
                </div>
              


                <div className="text-center">
                  <Button
                    color="light"
                    onClick={refreshOrders}
                    className="text-center m-auto"
                  >
                    {fetchingOrders ? (
                      <Spinner className="mr-2 h-5 w-5" />
                    ) : (
                      <FaArrowsRotate className="mr-2 h-5 w-5" />
                    )}
                    {fetchingOrders ? "Refreshing..." : "Refresh Status"}
                  </Button>
                  <div className="text-xs text-gray-400 pt-2">
                    {lastFetched &&
                      "Refreshed: " +
                        new Date(lastFetched).toLocaleString("en-US", {
                          month: "numeric",
                          day: "2-digit",
                          year: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                          hour12: true, // Use 24-hour format
                        })}
                  </div>
                </div>
             
            </div>
          </caption>

          
            <Accordion alwaysOpen>
              
              <Accordion.Panel>
                <Accordion.Title>My Orders</Accordion.Title>
                <Accordion.Content className="table-auto">
                  <Table>
                    <Table.Head className="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400 text-center">
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Order Date
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Customer
                      </Table.HeadCell>
                      {/* <Table.HeadCell scope="col" className="px-6 py-3">
                        Auto Delivery
                      </Table.HeadCell> */}
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Order ID
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Tracking
                      </Table.HeadCell>
                      
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Total
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Status
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        View
                      </Table.HeadCell>
                    </Table.Head>
                    {data.length > 0 ? (
                    <Table.Body>
                    
                      {data.map((item) => {
                        // console.log(item);
                        const inputDate = new Date(item.date);
                        const options = {
                          year: "2-digit",
                          month: "numeric",
                          day: "numeric",
                        };
                        const formattedDate = inputDate.toLocaleDateString(
                          "en-US",
                          options
                        );

                        return (
                          <Table.Row
                            className="bg-white border-b font-light text-gray-900 dark:bg-gray-800 dark:border-gray-700 text-center"
                            key={item.id}
                          >
                            <Table.Cell
                              scope="row"
                              className="px-6 py-4 font-light text-gray-900 whitespace-nowrap dark:text-white"
                            >
                              {formattedDate}
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4">
                              <p className="font-bold">
                                {item.User.first_name} {item.User.last_name}
                              </p>
                              <p className="font-light text-gray-500">
                                {" "}
                                {item.Locations?.practice}
                              </p>
                            </Table.Cell>
                            {/* <Table.Cell className="px-6 py-4 text-center">
                              <div className="flex justify-center">
                                {item.auto_delivery === 1 ? (
                                  <FaCheckCircle color="#08447C" size="25px" />
                                ) : (
                                  <FaRegCircle color="#08447C" size="25px" />
                                )}
                              </div>
                            </Table.Cell> */}
                            <Table.Cell className="px-6 py-4">
                              {item.jde_id}
                            </Table.Cell>
                            <Table.Cell className="px-4 py-4 text-blue-600">
                              {item.tracking  && ( 
                                <>
                              {(() => {
                                const provider = getShippingProvider(item.shipping_method);
                                if (provider === 'FedEx') {
                                  return (
                                    <Link
                                      href={`https://www.fedex.com/fedextrack/no-results-found?trknbr=${item.tracking}`}
                                      target="_blank"
                                    >
                                      Track Order
                                    </Link>
                                  );
                                } else if (provider === 'UPS') {
                                  return (
                                    <Link
                                      href={`https://www.ups.com/track?tracknum=${item.tracking}`}
                                      target="_blank"
                                    >
                                      Track Order
                                    </Link>
                                  );
                                } else {
                                  return item.tracking;
                                }
                              })()}
                              </>
                              )}
                            </Table.Cell>
                            
                            <Table.Cell className="px-6 py-4">
                              {formatPrice.format(item.total)}
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4">
                              {item.status === 1 && (
                                <p className="text-center font-semibold p-2 text-blue-500 bg-blue-100 rounded-md">
                                  Submitted
                                </p>
                              )}
                              {item.status === 2 && (
                                <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                  In Progress
                                </p>
                              )}
                              {item.status === 3 && (
                                <p className="text-center font-semibold p-2 text-yellow-500 bg-yellow-100 rounded-md">
                                  Shipped
                                </p>
                              )}
                              {item.status === 4 && (
                                <p className="text-center font-semibold p-2 text-green-500 bg-green-100 rounded-md">
                                  Delivered
                                </p>
                              )}
                              {item.status === 9 && (
                                <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                  Cancelled
                                </p>
                              )}
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4 text-center">
                              <Button
                                onClick={() => handleOpenModal(item)} // Pass the item to the handler
                                color="transparent"
                                className="text-[#08447C] hover:text-blue-500 block mx-auto"
                              >
                                View
                              </Button>
                            </Table.Cell>
                          </Table.Row>
                        );
                      })} 
                     
                    </Table.Body>
                     ) : (
                      <Table.Body >
                        <Table.Row> <Table.Cell colspan="8" className="pt-4 text-center text-gray-600">No Available Orders</Table.Cell></Table.Row>
                        </Table.Body>
                    )}
                  </Table>
                </Accordion.Content>
              </Accordion.Panel>
              <Accordion.Panel>
                <Accordion.Title>Auto Deliveries</Accordion.Title>
                <Accordion.Content>
                  <Table>
                    <Table.Head className="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400 text-center">
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Created Date
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Customer
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Auto Delivery ID
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Next Delivery Date
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Total
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        Status
                      </Table.HeadCell>
                      <Table.HeadCell scope="col" className="px-6 py-3">
                        View
                      </Table.HeadCell>
                    </Table.Head>
                    {recurring.length > 0 ? (
                    <Table.Body>
                      {recurring.map((item) => {
                          const inputDate = new Date(item.date);
                          const options = {
                            year: "2-digit",
                            month: "numeric",
                            day: "numeric",
                          };
                          const formattedDate = inputDate.toLocaleDateString(
                            "en-US",
                            options
                          );

                        return (
                          <Table.Row
                            className="bg-white border-b font-light text-gray-900 dark:bg-gray-800 dark:border-gray-700 text-center"
                            key={item.id}
                          >
                            <Table.Cell
                              scope="row"
                              className="px-6 py-4 font-light text-gray-900 whitespace-nowrap dark:text-white"
                            >
                              {formattedDate}
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4">
                              <p className="font-bold">
                                {item.User.first_name} {item.User.last_name}
                              </p>
                              <p className="font-light text-gray-500">
                                {" "}
                                {item.Locations?.practice}
                              </p>
                            </Table.Cell>
                            <Table.Cell className="px-4 py-4">
                              {item.parent_jde_id}
                            </Table.Cell>
                            <Table.Cell
                              scope="row"
                              className="px-6 py-4 font-light text-gray-900 whitespace-nowrap dark:text-white"
                            >
                              {calculateScheduledDate(item)}
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4">
                              {formatPrice.format(item.total)}
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4">
                              {item.cancel_auto === 1 ? (
                                <p className="text-center font-semibold p-2 text-red-500 bg-red-100 rounded-md">
                                Cancelled
                              </p> ) :
                              ( item.pause_auto === 1 ? (
                                  <p className="text-center font-semibold p-2 text-yellow-500 bg-yellow-100 rounded-md">
                                  Paused
                                  </p>
                                ) : (
                                  <p className="text-center font-semibold p-2 text-purple-500 bg-purple-100 rounded-md">
                                    Upcoming
                                  </p>
                                )
                                ) 
                                }
                              
                            </Table.Cell>
                            <Table.Cell className="px-6 py-4 text-center">
                              <Button
                                onClick={() => handleOpenRecurringModal(item)} // Pass the item to the handler
                                color="transparent"
                                className="text-[#08447C] hover:text-blue-500 block mx-auto"
                              >
                                View
                              </Button>
                            </Table.Cell>
                          </Table.Row>
                        );
                      })}
                    </Table.Body>
                    ) : (
                      <Table.Body >
                        <Table.Row> <Table.Cell colspan="8" className="pt-4 text-center text-gray-600">No Upcoming Orders</Table.Cell></Table.Row>
                        </Table.Body>
                    )}
                  </Table>
                </Accordion.Content>
              </Accordion.Panel>
            </Accordion>
          
        </table>

        <Modal
          show={openOrder}
          size="3xl"
          dismissible
          onClose={() => setOpenOrder(false)}
          className="modal-opacity modal-content"
        >
          {selectedItem && (
            <>
              <Modal.Header>Order #{selectedItem.jde_id}</Modal.Header>
              <Modal.Body>
                {/* Display item details using selectedItem */}
                <div className="grid grid-cols-4 gap-4 pb-6">
                  <div>
                    <p className="text-[#929791]">Date</p>
                    <p>
                      {selectedItem.date
                        ? new Date(selectedItem.date).toLocaleDateString(
                            "en-US",
                            {
                              year: "2-digit",
                              month: "numeric",
                              day: "numeric",
                            }
                          )
                        : ""}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#929791]">Status</p>
                    {selectedItem.status === 1 && <p>Submitted</p>}
                    {selectedItem.status === 2 && <p>In Progress</p>}
                    {selectedItem.status === 3 && <p>Shipped</p>}
                    {selectedItem.status === 4 && <p>Delivered</p>}
                    {selectedItem.status === 9 && <p>Cancelled</p>}
                  </div>
                  <div>
                    <p className="text-[#929791]">Tracking</p>
                      {selectedItem.tracking && (
                        <p>
                        {(() => {
                          const provider = getShippingProvider(selectedItem.shipping_method);
                          if (provider === 'FedEx') {
                            return (
                              <Link
                                href={`https://www.fedex.com/fedextrack/no-results-found?trknbr=${selectedItem.tracking}`}
                                target="_blank"
                              >
                                {selectedItem.tracking}
                              </Link>
                            );
                          } else if (provider === 'UPS') {
                            return (
                              <Link
                                href={`https://www.ups.com/track?tracknum=${selectedItem.tracking}`}
                                target="_blank"
                              >
                                {selectedItem.tracking}
                              </Link>
                            );
                          } else {
                            return selectedItem.tracking;
                          }
                        })()}
                      </p>
                      ) }
                  </div>
       
                  <div>
                    <p  className="text-[#929791]">Shipping Method</p>
                    <p>{getShippingMethod(selectedItem.shipping_method)}</p>
                    </div>
                </div>
                <hr className="h-px bg-gray-100 border-0 -mx-6" />
                <div className="grid grid-cols-3 gap-4 py-6 font-light">
                  <div>
                    <p className="text-[#929791]">Shipping Address</p>
                    <p>{selectedItem.Locations.practice}</p>
                    <p>
                      {selectedItem.Locations.address}{" "}
                      {selectedItem.Locations.address_2}
                    </p>
                    <p>
                      {selectedItem.Locations.city},{" "}
                      {selectedItem.Locations.state}{" "}
                      {selectedItem.Locations.zip}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#929791]">Billing Address</p>
                    <p>{session.user.Account.business_name}</p>
                    <p>
                      {session.user.Account.address}{" "}
                      {session.user.Account.address_2}
                    </p>
                    <p>
                      {session.user.Account.city}, {session.user.Account.state}{" "}
                      {session.user.Account.zip.substring(0, 5)}
                    </p>
                  </div>
                  {selectedItem.Payment ? (
                    <div>
                      <p className="text-[#929791]">Payment Method</p>
                      <div className="flex flex-row items-center space-x-4">
                        <div>
                          <img
                            src={cardImage(selectedItem.Payment.type)}
                            className="w-[48.81px] h-[32.03px] mx-auto"
                          ></img>
                        </div>
                        <div className="my-[20px]">
                          <p className="text-[14px] ">
                            xxxx-xxxx-xxxx-{selectedItem.Payment.last_four}
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <hr className="h-px bg-gray-100 border-0 -mx-6" />

                <div className="pt-6">
                  <p className="text-2xl font-semibold">Order Summary</p>
                  <OrderSummary
                    selectedItem={selectedItem}
                    onSubtotalChange={handleSubtotalChange}
                  />
                  <hr className="h-px bg-gray-100 border-0 -mx-6" />
                  <div className="grid grid-cols-9 gap-x-4 gap-y-2 py-6 font-light">
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Subtotal</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(orderSubtotal)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Discount</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        -{formatPrice.format(selectedItem.discount)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Tax</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.tax)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Shipping</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.shipping)}
                      </p>
                    </div>

                    <div className="justify-self-start col-start-5 col-span-3 font-semibold">
                      <p className="text-sm">Total</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.total)}
                      </p>
                    </div>
                  </div>
                  <hr className="h-px bg-gray-100 border-0 -mx-6" />
                  <div className="bg-white rounded-xl dark:bg-gray-800 ">
                    <div
                      className="pt-3 -mb-3 flex gap-2 items-center"
                      onClick={toggleAccordion}
                    >
                      <h1 className="text-[#353535] font-semibold text-[20px]">
                        Auto Delivery
                      </h1>
                      <FaChevronDown color="#929791" size="15px" />
                    </div>
                    {isExpanded && (
                      <div>
                        <div className="flex flex-row gap-4 justify-start mt-7">
                          <div>
                            <Button
                              color={`${
                                parseInt(selectedItem.auto_freq) === 0
                                  ? ""
                                  : "light"
                              }`}
                              className={`w-[156.25px] h-11  ${
                                parseInt(selectedItem.auto_freq) === 0
                                  ? "bg-primary text-white"
                                  : ""
                              }`}
                              style={{ pointerEvents: "none" }}
                            >
                              None
                            </Button>
                          </div>
                          <div>
                            <Button
                              color={`${
                                parseInt(selectedItem.auto_freq) === 1
                                  ? ""
                                  : "light"
                              }`}
                              className={`w-[156.25px] h-11  ${
                                parseInt(selectedItem.auto_freq) === 1
                                  ? "bg-primary text-white"
                                  : ""
                              }`}
                              style={{ pointerEvents: "none" }}
                            >
                              Every Month
                            </Button>
                          </div>
                          <div>
                            <Button
                              color={`${
                                parseInt(selectedItem.auto_freq) === 2
                                  ? ""
                                  : "light"
                              }`}
                              className={`w-[156.25px] h-11  ${
                                parseInt(selectedItem.auto_freq) === 2
                                  ? "bg-primary text-white"
                                  : ""
                              }`}
                              style={{ pointerEvents: "none" }}
                            >
                              Every 2 Months
                            </Button>
                          </div>
                          <div>
                            <Button
                              color={`${
                                parseInt(selectedItem.auto_freq) === 3
                                  ? ""
                                  : "light"
                              }`}
                              className={`w-[156.25px] h-11  ${
                                parseInt(selectedItem.auto_freq) === 3
                                  ? "bg-primary text-white"
                                  : ""
                              }`}
                              style={{ pointerEvents: "none" }}
                            >
                              Every 3 Months
                            </Button>
                          </div>
                        </div>
                        {selectedItem.auto_delivery === 1 && (
                          <div className="flex flex-row justify-start gap-x-6 pt-6 font-light">
                            <div className="">
                              <p className="text-[#929791]">
                                Delivery Start Date
                              </p>
                              <p>
                                {selectedItem.date
                                  ? new Date(
                                      selectedItem.date
                                    ).toLocaleDateString("en-US", {
                                      year: "2-digit",
                                      month: "numeric",
                                      day: "numeric",
                                    })
                                  : ""}
                              </p>
                            </div>
                            <div className="">
                              <p className="text-[#929791]">
                                Next Scheduled Delivery
                              </p>
                              <p>
                                {selectedItem.auto_start_date
                                  ? new Date(
                                      selectedItem.auto_start_date
                                    ).toLocaleDateString("en-US", {
                                      year: "2-digit",
                                      month: "numeric",
                                      day: "numeric",
                                    })
                                  : ""}
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </Modal.Body>
              <Modal.Footer>
                <BsPrinter
                  onClick={() => {
                    // Add the "modal-print" class to the modal element
                    const modal = document.querySelector(".modal-content");
                    modal.classList.add("modal-print");

                    // Trigger the print action
                    window.print();

                    // Remove the "modal-print" class to revert to normal display
                    modal.classList.remove("modal-print");
                  }}
                  className="cursor-pointer"
                />
                {/* <Button color="gray" onClick={() => setOpenOrder(false)}>
                Decline
              </Button> */}
              </Modal.Footer>
            </>
          )}
          
        </Modal>

        <Modal
          show={openRecurringOrder}
          size="3xl"
          dismissible
          onClose={() => setOpenRecurringOrder(false)}
          className="modal-opacity modal-content"
        >
          {selectedItem && (
            <>
              <Modal.Header>
              {selectedItem.cancel_auto === 1 ? 
                (<p>Cancelled Order Details</p>) 
                : (selectedItem.pause_auto === 1 ? (
                      <p>Paused Order Details</p>
                    ) : (
                      <p>Upcoming Order Details</p>
                    )
                  )}
                    </Modal.Header>
              <Modal.Body>
                <div className="grid grid-cols-3 gap-4 pb-4 font-light">
                  <div>
                    <p className="text-[#929791]">Shipping Address</p>
                    <p>{selectedItem.Locations.practice}</p>
                    <p>
                      {selectedItem.Locations.address}{" "}
                      {selectedItem.Locations.address_2}
                    </p>
                    <p>
                      {selectedItem.Locations.city},{" "}
                      {selectedItem.Locations.state}{" "}
                      {selectedItem.Locations.zip}
                    </p>
                  </div>
                  <div>
                    <p className="text-[#929791]">Billing Address</p>
                    <p>{session.user.Account.business_name}</p>
                    <p>
                      {session.user.Account.address}{" "}
                      {session.user.Account.address_2}
                    </p>
                    <p>
                      {session.user.Account.city}, {session.user.Account.state}{" "}
                      {session.user.Account.postal_code}
                    </p>
                  </div>
                  {selectedItem.Payment ? (
                    <div>
                      <p className="text-[#929791]">Payment Method</p>
                      <div className="flex flex-row items-center space-x-4">
                        <div>
                          <img
                            src={cardImage(selectedItem.Payment.type)}
                            className="w-[48.81px] h-[32.03px] mx-auto"
                          ></img>
                        </div>
                        <div className="my-[20px]">
                          <p className="text-[14px] ">
                            xxxx-xxxx-xxxx-{selectedItem.Payment.last_four}
                          </p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    ""
                  )}
                </div>
                <hr className="h-px bg-gray-100 border-0 -mx-6" />
                <div className="grid grid-cols-3 gap-4 py-4 font-light">
                  <div>
                    <p className="text-[#929791]">Order Frequency</p>
                    {selectedItem.auto_freq === 1 ? (
                      <p>Every Month</p>
                    ) : selectedItem.auto_freq === 2 ? (
                      <p>Every 2 Months</p>
                    ) : (
                      <p>Every 3 Months</p>
                    )}
                  </div>
                  <div>
                    <p className="text-[#929791]">Start Date</p>
                    <p>{formatDate(selectedItem.date)}</p>
                  </div>
                  <div>
                    <p className="text-[#929791]">Scheduled Delivery</p>
                    <p>{calculateScheduledDate(selectedItem)}</p>       
                  </div>
                </div>

                <hr className="h-px bg-gray-100 border-0 -mx-6" />

                <div className="pt-6">
                  <p className="text-2xl font-semibold">Order Summary</p>
                  <OrderSummary
                    selectedItem={selectedItem}
                    onSubtotalChange={handleSubtotalChange}
                  />
                  <hr className="h-px bg-gray-100 border-0 -mx-6" />
                  <div className="grid grid-cols-9 gap-x-4 gap-y-2 py-6 font-light">
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Subtotal</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(orderSubtotal)}
                      </p>
                    </div>

                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Shipping</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.shipping)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Taxes</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.tax)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3">
                      <p className="text-sm">Recurring Order Discount</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        -{formatPrice.format(selectedItem.discount)}
                      </p>
                    </div>
                    <div className="justify-self-start col-start-5 col-span-3 font-semibold">
                      <p className="text-sm">Total</p>
                    </div>
                    <div className="justify-self-end cols-start-7 col-span-2">
                      <p className="text-sm">
                        {formatPrice.format(selectedItem.total)}
                      </p>
                    </div>
                  </div>
                  <hr className="h-px bg-gray-100 border-0 -mx-6" />
                  {/* <div className="pt-4 text-center text-xs font-semibold">
                    <p>
                      **New Items added to the auto delivery cart will be
                      included for all future deliveries**
                    </p>
                  </div> */}
                </div>
              </Modal.Body>
              <Modal.Footer className="justify-between">
                <div>
                  <BsPrinter
                    onClick={() => {
                      // Add the "modal-print" class to the modal element
                      const modal = document.querySelector(".modal-content");
                      console.log(modal);
                      modal.classList.add("modal-print");

                      // Trigger the print action
                      window.print();
                      console.log(modal);

                      // Remove the "modal-print" class to revert to normal display
                      modal.classList.remove("modal-print");
                    }}
                    className="cursor-pointer"
                  />
                </div>
                {selectedItem.type === "phone" ? ("") : (
                <div className="flex flex-row gap-x-3" style={{ marginLeft: "auto" }}>
                  <div>
                    {selectedItem.cancel_auto === 1 ? (
                  <Button
                    pill={true}
                    className="theme-button"
                    isProcessing={cancelProccess}
                    onClick={() => restartRecurring(selectedItem.id)}
                  >
                    Restart Auto Delivery
                  </Button>
                    ) : (
                  <Tooltip
                      content={ "Cancel scheduled orders"}
                      placement="top"
                      >
                    <Button
                      pill={true}
                      color="red"
                      className="py-2 px-4"
                      isProcessing={cancelProccess}
                      onClick={() => cancelRecurring(selectedItem.id)}
                      // disabled={selectedItem.cancel_auto === 1}
                    >
                      Cancel Auto Delivery
                    </Button>
                  </Tooltip>
                    )}
                  </div>
                  <div>
                    { selectedItem.cancel_auto === 1 ? "" : (
                        selectedItem.pause_auto === 1 ? (
                          <Button
                            pill={true}
                            className="theme-button"
                            isProcessing={skipProccess}
                            onClick={() => resumeUpcoming(selectedItem.id)}
                          >
                            Resume Auto Delivery
                          </Button>
                        ) : (
                          <Tooltip
                          content={ "Skip next scheduled delivery"}
                          placement="top"
                          >
                            <Button
                              pill={true}
                              className="secondary-button"
                              isProcessing={skipProccess}
                              onClick={() => cancelUpcoming(selectedItem.id)}
                              // disabled={selectedItem.pause_auto === 1 || selectedItem.cancel_auto === 1}
                            >
                              
                                Pause Auto Delivery
                            </Button>
                          </Tooltip>
                        )
                      )
                    }
                   
                </div>
                
                </div>
                )}
              </Modal.Footer>
            </>
          )}
        </Modal>
      </>
    );
  }
}

export default OrdersTable;

function OrderSummary({ selectedItem, onSubtotalChange }) {
  // Use the selectedItem prop to access the data
  const formatPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  });
  // Calculate subtotal
  const subtotal = selectedItem.OrderedProduct.reduce((total, product) => {
    return total + product.price * product.qty;
  }, 0);
  console.log("products",selectedItem.OrderedProduct);

  useEffect(() => {
    onSubtotalChange(subtotal);
  }, [subtotal, onSubtotalChange]);

  return (
    <div className="font-light">
      {selectedItem &&
        selectedItem.OrderedProduct.map((product) => (
          <div className="grid grid-cols-9 gap-4 py-3" key={product.product_id}>
            <div className="flex-shrink-0 col-span-1">
              <img
                className="w-11 h-11 sqaure"
                src={product.Product.ProductImage && product.Product.ProductImage.length < 1
                  ? "/no_image.jpeg"
                  : `${
                  process.env.NEXT_PUBLIC_STAGING_BASE_URL +
                  "oasis-product-images/"
                }${product.Product.ProductImage[0].path}_s.${
                  product.Product.ProductImage[0].extension
                }`}
                alt=""
              />
            </div>
            <div className="text-black font-semibold text-sm mb-1.5 col-span-4">
              {product.Product.name}{" "}
              {product.last_status_code === "980" && (
                <span className="text-red-500"> - Item Cancelled</span>
              )}
              <div className="text-xs font-normal text-gray-400 mt-2">
                {product.Product.sku}
              </div>
            </div>
            <div className="justify-self-center col-span-2">
              <p className="text-sm">
                Qty.{" "}
                {product.last_status_code === "980" ? (
                  <span className="text-red-500">0</span>
                ) : (
                  product.qty
                )}
              </p>
            </div>
            <div className="justify-self-end col-span-2">
              <p className="text-sm">
                {formatPrice.format(product.price)}
              </p>
            </div>
          </div>
        ))}
    </div>
  );
}
