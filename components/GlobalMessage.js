import { <PERSON>ert } from "flowbite-react";
import { useState, useEffect } from "react";
import axios from "axios";
import { HiInformationCircle, HiX, Hi<PERSON><PERSON>, Hi<PERSON>he<PERSON> } from "react-icons/hi";
import { MdAnnouncement } from "react-icons/md";

function GlobalMessage() {
    const [message, setMessage] = useState("");
  
    useEffect(() => {
        axios.get("/api/globalMessage/get")
          .then((response) => {
            if (response.data) {
              setMessage(response.data);
            }
          })
          .catch((error) => {
            if (error.response && error.response.status === 404) {
              console.log("No global message found.");
            } else {
              console.error("Error fetching global message:", error);
            }
          });
      }, []);

    return (
        message && (
            <Alert className="mb-4 border-[#bae0f3] bg-[#bae0f3] text-[#0b447c] dark:bg-[#bae0f3] dark:text-[#0b447c]" additionalContent={<span
                className="html-notif" // Use the 'prose' class to style the HTML content similarly to the CKEditor
                dangerouslySetInnerHTML={{ __html: message.body }}
              ></span>}>
              
            </Alert>
          )
  );
}

export default GlobalMessage;
