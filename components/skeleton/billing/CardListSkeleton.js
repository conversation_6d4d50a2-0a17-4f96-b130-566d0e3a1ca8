const CardListSkeleton = () => {
    const numberOfCards = 3;
    const cards = Array.from({ length: numberOfCards }, (_, index) => (
      <div key={index} className="w-full font-medium text-left border-b border-gray-200 animate-pulse">
        <div className="flex flex-row justify-between h-[84px] gap-[10px]">
          <div className="grow-0">
            <div className="w-[48.81px] h-[32.03px] ml-[30px] mt-[24.99px] bg-gray-300 rounded-md"></div>
          </div>
          <div className="grow pl-6">
            <div className="my-[20px]">
              <div className="h-3 bg-gray-200 rounded-md w-3/5"></div>
            </div>
            <div className="grow">
              <div className="h-3 bg-gray-300 rounded-md w-4/5"></div>
            </div>
          </div>
          <div className="grow m-auto pl-4">
            <div className="h-3 bg-gray-300 rounded-md py-[5px] my-[31px] mr-[30px]"></div>
          </div>
        </div>
      </div>
    ));
  
    return <div>{cards}</div>;
};

export default CardListSkeleton;
