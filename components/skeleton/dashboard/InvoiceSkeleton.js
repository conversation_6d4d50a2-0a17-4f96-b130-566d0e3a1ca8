import Link from "next/link";

export default function InvoiceSkeleton() {
  return (
    <div className="h-full overflow-auto relative bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
    <p className="text-3xl font-semibold">Invoices</p>
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <tbody>
          <tr className="grid grid-cols-2 gap-4 mt-10 text-sm">
            <div>
              <p class="w-1/2 h-3 bg-gray-300 rounded-lg mb-2"></p>
              <p class="w-3/4 h-3 bg-gray-200 rounded-lg"></p>
            </div>
            <div>
              <p className="h-6 text-center font-semibold p-2 bg-gray-300 rounded-md w-14 float-right"></p>
            </div>
          </tr>
          <tr className="grid grid-cols-2 gap-4 mt-10 text-sm">
            <div>
              <p class="w-1/2 h-3 bg-gray-300 rounded-lg mb-2"></p>
              <p class="w-3/4 h-3 bg-gray-200 rounded-lg"></p>
            </div>
            <div>
              <p className="h-6 text-center font-semibold p-2 bg-gray-200 rounded-md w-14 float-right"></p>
            </div>
          </tr>
          <tr className="grid grid-cols-2 gap-4 mt-10 text-sm">
            <div>
              <p class="w-1/2 h-3 bg-gray-300 rounded-lg mb-2"></p>
              <p class="w-3/4 h-3 bg-gray-200 rounded-lg"></p>
            </div>
            <div>
              <p className=" h-6 text-center font-semibold p-2 bg-gray-300 rounded-md w-14 float-right"></p>
            </div>
          </tr>
        </tbody>
      </table>
      <div className="absolute inset-x-0 bottom-0 grid grid-cols-2 px-6 pb-8 place-items-end">
        <p className="text-xs text-gray-400 text-start my-auto mb-0 pb-0"></p>
      <p class="h-6 w-1/2 bg-gray-200 grid place-items-end my-auto"></p>
      </div>
    </div>
  );
}
