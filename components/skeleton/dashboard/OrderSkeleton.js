import { FaCheckCircle, FaRegCircle, FaCircle } from "react-icons/fa";
import Link from "next/link";

export default function OrderSkeleton() {
  const numberOfOrders = 2;
  const orders = Array.from({ length: numberOfOrders }, (_, index) => (
    <tr key={index} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
      <th
        scope="row"
        className="px-4 py-4 text-gray-900 whitespace-nowrap dark:text-white w-1/5"
      >
        <p className="h-4 bg-gray-300 rounded-md"></p>
      </th>
      <td className="px-4 py-4 flex justify-center">
        <FaCircle color="#D1D5DA" size="25px" />
      </td>
      <td className="px-4 py-4 text-center w-1/6">
        <p className="h-4 bg-gray-200 rounded-md"></p>
      </td>
      <td className="px-4 py-4 text-center w-1/4">
        <p className="h-4 bg-gray-300 rounded-md"></p>
      </td>
      <td className="px-4 py-4 w-1/3">
        <p className="h-4 bg-gray-200 rounded-md"></p>
      </td>
    </tr>
  ));
  const orders2 = Array.from({ length: numberOfOrders }, (_, index) => (
    <tr key={index} className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
      <th
        scope="row"
        className="px-4 py-4 text-gray-900 whitespace-nowrap dark:text-white w-1/5"
      >
        <p className="h-4 bg-gray-200 rounded-md"></p>
      </th>
      <td className="px-4 py-4 flex justify-center">
        <FaCircle color="#D1D5DA" size="25px" />
      </td>
      <td className="px-4 py-4 text-center w-1/6">
        <p className="h-4 bg-gray-300 rounded-md"></p>
      </td>
      <td className="px-4 py-4 text-center w-1/4">
        <p className="h-4 bg-gray-200 rounded-md"></p>
      </td>
      <td className="px-4 py-4 w-1/3">
        <p className="h-4 bg-gray-300 rounded-md"></p>
      </td>
    </tr>
  ));

  return (
    <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-md dark:bg-gray-800">
      <p className="text-3xl font-semibold">Orders</p>

      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="animate-pulse text-xs text-gray-700 text-center uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
          <tr>
            <th scope="col" className="px-4 py-3"></th>
            <th scope="col" className="px-4 py-3">
              <div className="h-4 bg-gray-300 rounded-md"></div>
            </th>
            <th scope="col" className="px-4 py-3">
              <div className="h-4 bg-gray-200 rounded-md"></div>
            </th>
            <th scope="col" className="px-4 py-3">
              <div className="h-4 bg-gray-300 rounded-md"></div>
            </th>
            <th scope="col" className="px-4 py-3">
              <div className="h-4 bg-gray-200 rounded-md"></div>
            </th>
          </tr>
        </thead>
        <tbody className="animate-pulse">
          {orders}
          {orders2}
          {/* <tr className="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
            <th
              scope="row"
              className="px-4 py-4 text-gray-900 whitespace-nowrap dark:text-white w-1/5"
            >
              <p className="h-4 bg-gray-300 rounded-md"></p>
            </th>
            <td className="px-4 py-4 flex justify-center">
              <FaCheckCircle color="gray-200" size="25px" />
            </td>
            <td className="px-4 py-4 text-center w-1/6">
              <p className="h-4 bg-gray-300 rounded-md"></p>
            </td>
            <td className="px-4 py-4 text-center w-1/4">
              <p className="h-4 bg-gray-300 rounded-md"></p>
            </td>
            <td className="px-4 py-4 w-1/3">
              <p className="h-4 bg-gray-300 rounded-md"></p>
            </td>
          </tr> */}
        </tbody>
      </table>
      <div className="pt-14 pb-8 animate-pulse">
        <div className="h-6 bg-gray-200 rounded-2xl w-1/5 float-right"></div>
      </div>
    </div>
  );
}
