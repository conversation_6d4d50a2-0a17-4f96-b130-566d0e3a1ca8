// LatestNotificationSkeleton.js
import React from "react";

const NotificationSkeleton = () => {
  return (
    <div className="h-full bg-white px-6 lg:px-8 py-6 rounded-xl dark:bg-gray-800">
        <p className="text-2xl font-semibold mb-2">Notifications</p>
    <div className="py-2">
      <div className=" -mx-6 lg:-mx-8">
        <div className="w-full h-48 bg-gray-300"></div>
      </div>
      <div className="py-2">
        <div className="font-bold w-2/3 h-6 bg-gray-300 mb-2"></div>
        <div className="w-full h-8 bg-gray-300"></div>
      </div>
      <div className="pt-14 pb-8 animate-pulse">
        <div className="h-6 bg-gray-200 rounded-2xl w-1/5 float-right"></div>
      </div>
    </div>
    </div>
  );
};

export default NotificationSkeleton;
