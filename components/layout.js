import { useSession, signOut } from "next-auth/react";
import SidebarMenu from "../components/sidebarMenu";
import DashNavbar from "../components/dashNavbar";
import GlobalMessage from "../components/GlobalMessage";
import AllAlerts from "../components/allAlerts";
import { useGlobalAlertContext } from "../contexts/GlobalAlertContext";
import { useEffect } from "react";
import { useRouter } from "next/router";


export default function Layout({ children }) {
  const { data: session, status } = useSession();
  const loading = status === "loading";
  const { notifications } = useGlobalAlertContext();
  const router = useRouter();
  


  function logoutHandler() {
    signOut();
  }

  if (loading) return <p>loading...</p>;

  // Session has expired or user is not logged in
  // console.log("status", status);
  // console.log("session check", session);
  if (status === 'unauthenticated' && !session) {
    // Redirect to login page
    router.push('/');
    return null; // Don't render anything else while redirecting
    // Redirect to login page
    // signOut({ callbackUrl: "/" });
  }

  // useEffect(() => {
  //   if (!session || status === 'unauthenticated' || status === '') {
  //     // Session expired or not defined, trigger page refresh
  //     router.push('/');
  //     return null; 
  //   }
  // }, [session, router]);
  // useEffect(() => {
  //   if (status === 'unauthenticated' && !session) {
  //     // Redirect to login page only if not already on the login page
  //     if (router.pathname !== '/') {
  //       router.push('/');
  //     }
  //   }
  // }, [status, session, router.pathname]);

  // console.log("layout", notifications);
  // console.log("layout notif length", notifications.length);

  if (session) {
    return (
      <div className="flex flex-row h-screen w-screen overflow-hidden bg-neutral-200">
        <div className="w-fit grow h-full">
          <SidebarMenu></SidebarMenu>
        </div>
        <div className="w-full h-full p-4 flex flex-col items-start overflow-hidden">
          <div className="w-full h-full grid grid-col-1 grid-rows-[auto_minmax(0px,_1fr)] gap-4">
            <DashNavbar></DashNavbar>
            
            {/* <GlobalMessage></GlobalMessage> */}

            <main className="overflow-y-auto h-full relative">
            <GlobalMessage></GlobalMessage>
              {children}
              </main>

            <div className="fixed flex flex-col gap-4 rounded-lg right-10 bottom-10 opacity-95 shadow-sm">
              {notifications.map((notification, index) => {
                // console.log("single notification", notification);

                return <AllAlerts id={index} notification={notification} />;
              })}
            </div>
          </div>
        </div> 
      </div>
    );
  }
}
