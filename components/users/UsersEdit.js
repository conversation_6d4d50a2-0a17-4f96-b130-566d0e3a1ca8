import { Label, TextInput, Modal, Button } from "flowbite-react";
import { useState, useEffect } from "react";
import Select from "react-select";
import axios from "axios";

function EditUser({
  userData,
  locations,
  setEditModal,
  session,
  onUpdate,
  alert,
  setAlert,
}) {
  //   console.log("userData");
  const [userForm, setUserForm] = useState({
    id: null,
    first: "",
    last: "",
    title: "",
    email: "",
    password: "",
    password_confirm: "",
    user_group: "",
    location: null,
    phone: "",
    phone_type: null,
    fax: "",
    jde_id: session.user.jde_id,
    account_id: session.user.account_id,
  });

  const [locationRequired, setLocationRequired] = useState(false);

  function handleChange(e) {
    const { name, value } = e.target;
    console.log(name);
    console.log(value);
    setUserForm({
      ...userForm,
      [name]: value,
    });
  }

  const handleChangeSelectLocation = (selectedOption) => {
    if (selectedOption) {
      setUserForm({
        ...userForm,
        location: selectedOption.value,
      });
    } else {
      setUserForm({
        ...userForm,
        location: null,
      });
    }
  };

  const handleChangeSelect = (selectedOption) => {
    const name = selectedOption.name;
    if (name === "user_group") {
      setUserForm({
        ...userForm,
        [name]: selectedOption.value,
      });
      
      if (selectedOption.value === 4) {
        // Check if the object is already in the array
        const allLocationsObject = { value: null, label: 'All Locations' };
        if (!locations.some(location => location.value === allLocationsObject.value)) {
          locations.unshift(allLocationsObject);
        }
      } else {
        // Remove the object if it's present in the array
        const allLocationsObjectIndex = locations.findIndex(location => location.value === null);
        if (allLocationsObjectIndex !== -1) {
          locations.splice(allLocationsObjectIndex, 1);
        }
      }

      // Check if "Admin" is selected and update the "Location" field requirement
      setLocationRequired(selectedOption.value === 2);
    } else {
      setUserForm({
        ...userForm,
        [name]: selectedOption.value,
      });
    }
  };

  const generateUserGroupsOptions = () => {
    const groupOptions = [
      { value: 1, label: "Super Admin", name: "user_group" },
      { value: 2, label: "Admin", name: "user_group" },
      { value: 3, label: "Accounting", name: "user_group" },
      { value: 4, label: "Standard", name: "user_group" },
    ];

    if (session.user.group_id === 1) {
      // Group ID is 1
      if (locations.length > 1) {
        // Locations > 1, show all options
        return groupOptions;
      } else {
        // Locations = 1, remove or disable Admin option
        return groupOptions.filter((option) => option.value !== 2);
      }
    } else if (session.user.group_id === 2) {
      // Group ID is 2, remove or disable Super Admin and Accounting options
      return groupOptions.filter(
        (option) => option.value !== 1 && option.value !== 3
      );
    }

    return groupOptions; // Default case
  };

  const user_groups = generateUserGroupsOptions();

  const phone_types = [
    { value: "Mobile", label: "Mobile", name: "phone_type" },
    { value: "Work", label: "Work", name: "phone_type" },
  ];

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setUserForm({
      ...userForm,
      phone: cleanedInput,
    });
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  //Function not used, need to adjust to account for current email for user (ie currently this check will fail since email does exist)
  const emailCheck = async (value) => {
    // console.log(value);
    try {
      const response = await fetch(`/api/user/verifyByEmail?email=${value}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const responseData = await response.json();

        if (responseData === 0) {
          console.log("user ID", responseData);
          return 1;
        } else {
          console.log("user ID", responseData);
          return 0;
        }
      }
    } catch (error) {
      // Handle any errors that occurred during the API call
      console.error("Error:", error);
    }
  };

  const successUserNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: "User Updated Successfully.",
      show: true,
    };

    console.log("New Notif", newNotification)

    setAlert(newNotification);
  };

  const failureUserNotification = () => {
    console.log("New Fail Notif")
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: "Failed To Update User, Please Try Again.",
      show: true,
    };

    setAlert(newNotification);
  };

  const editUser = (e) => {
    e.preventDefault();
    // setEditModal(false);
    axios.post("api/user/edit", userForm).then((response) => {
      if (response.status == 200) {
        //need to pass Contact data to this, to then add to JDE
        // console.log("response", response.data);
        // axios
        //   .post("api/syncJDE/editContact", response.data)
        //   .then((response) => {
        //     if (response.status == 200) {
        // setAlert({
        //   ...alert,
        //   iconColor: {
        //     bg: "bg-green-100",
        //     text: "text-green-500",
        //   },
        //   bgColor: "success",
        //   message: "User Updated Successfully.",
        //   show: true,
        // });
        successUserNotification();
        setEditModal(false);
        onUpdate();
        // }
        // });
      }else{
        failureUserNotification();
      }

    });
  };

  const dataProp = (user) => {
    setUserForm({
      id: user["id"],
      first: user["first_name"],
      last: user["last_name"],
      title: user["position"],
      email: user["email"],
      password: "",
      password_confirm: "",
      user_group: user["group_id"],
      location: user["location_id"],
      phone: user["phone"],
      phone_type: user["mobile_type"],
      fax: user["fax"],
    });
    let formattedPhone = "";
    for (let i = 0; i < user["phone"].length; i++) {
      if (i === 0) {
        formattedPhone = `(${user["phone"][i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${user["phone"][i]}`;
      } else if (i === 6) {
        formattedPhone += `-${user["phone"][i]}`;
      } else {
        formattedPhone += user["phone"][i];
      }
    }
    setFormatPhone(formattedPhone);

    // if(user["group_id"] === 4){
    //   console.log("STAND")
      
    //    // Check if the object is already in the array
    //    const allLocationsObject = { value: null, label: 'All Locations' };
    //    if (!locations.some(location => location.value === allLocationsObject.value)) {
    //      locations.unshift(allLocationsObject);
    //    }
    //  } else {
    //   console.log("NOT STAND")
    //    // Remove the object if it's present in the array
    //    const allLocationsObjectIndex = locations.findIndex(location => location.value === null);
    //    if (allLocationsObjectIndex !== -1) {
    //      locations.splice(allLocationsObjectIndex, 1);
    //    }
    //  }
  };
  


  useEffect(() => {
    dataProp(userData);
  }, []);
  // console.log(userForm);

  return (
    <form onSubmit={editUser}>
      <Modal.Header>Edit User</Modal.Header>
      <Modal.Body>
        {/* <div className="text-sm text-gray-500">* fields are required</div> */}
        <div className="grid gap-4 mb-4  sm:grid-cols-2">
          <div>
            <label
              htmlFor="first"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="first"
              className="custom-input"
              placeholder="* First Name"
              value={userForm.first}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="last"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="last"
              className="custom-input"
              placeholder="* Last Name"
              value={userForm.last}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="title"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="title"
              className="custom-input"
              placeholder="Contact Title"
              value={userForm.title}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor="email"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="email"
              className="custom-input"
              placeholder="Email"
              value={userForm.email}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="password"
              name="password"
              className="custom-input"
              placeholder="Password"
              value={userForm.password}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor="password_confirm"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="password"
              name="password_confirm"
              className="custom-input"
              placeholder="* Confirm Password"
              value={userForm.password_confirm}
              onChange={handleChange}
            />
          </div>
          {/* <div><p>Your password must be at least 8 characters long, contain at least one lower case letter, at least one uppercase letter, and at least one letter.</p></div> */}
          <div>
            <label
              htmlFor="user_group"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={user_groups}
              name="user_group"
              placeholder="* User Type"
              className="custom-select text-sm"
              value={
                userForm.user_group !== ""
                  ? user_groups.find(
                      (option) => option.value === userForm.user_group
                    )
                  : ""
              }
              onChange={handleChangeSelect}
            />
          </div>

          <div>
            <label
              htmlFor="location"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={locations}
              name="location"
              placeholder="* Location"
              isClearable={true}
              isSearchable={true}
              className="custom-select text-sm"
              value={
                userForm.location !== ""
                  ? locations.find(
                      (option) => option.value === userForm.location
                    )
                  : ""
              }
              onChange={handleChangeSelectLocation}
            />
            <p className="text-xs text-[#6A7280]">
              * Location is only required for admin level users.
            </p>
          </div>
        </div>
        <div className="grid gap-4 mb-4 sm:grid-cols-3">
          <div>
            <label
              htmlFor="phone"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="phone"
              className="custom-input"
              placeholder="Phone Number"
              maxLength={14}
              value={formatPhone}
              onChange={handlePhoneChange}
            />
          </div>
          <div>
            <label
              htmlFor="phone_type"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={phone_types}
              name="phone_type"
              className="custom-select text-sm"
              placeholder="Mobile or Work?"
              value={
                userForm.phone_type !== ""
                  ? phone_types.find(
                      (option) => option.value === userForm.phone_type
                    )
                  : ""
              }
              onChange={handleChangeSelect}
            />
          </div>

          <div>
            <label
              htmlFor="fax"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="fax"
              className="custom-input"
              placeholder="Fax"
              value={userForm.fax}
              onChange={handleChange}
            />
          </div>

          
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex flex-col justify-center mx-auto">
          <Button
            className=" theme-button w-48 hover:bg-primary-dark focus:ring-0 focus:ring-transparent"
            type="submit"
            pill={true}
          >
            Save Changes
          </Button>
        </div>
      </Modal.Footer>
    </form>
  );
}

export default EditUser;
