import { Label, TextInput, Modal, Button } from "flowbite-react";
import { useState, useEffect } from "react";
import Select from "react-select";
import axios from "axios";

function AddUser({ locations, setModal, session, onUpdate, alert, setAlert }) {
  console.log(locations);
  const [newUserForm, setNewUserForm] = useState({
    first: "",
    last: "",
    title: "",
    email: "",
    password: "",
    password_confirm: "",
    user_group: "",
    location: null,
    phone: "",
    phone_type: null,
    fax: "",
    jde_id: session.user.jde_id,
    account_id: session.user.account_id,
  });
  const [locationRequired, setLocationRequired] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [emailAlert, setEmailAlert] = useState({
    alertMessage: "",
    alertShow: "",
  });

  function handleChange(e) {
    const { name, value } = e.target;
    console.log(name);
    console.log(value);
    setNewUserForm({
      ...newUserForm,
      [name]: value,
    });
  }

  const handleChangeSelectLocation = (selectedOption) => {
    if (selectedOption) {
      setNewUserForm({
        ...newUserForm,
        location: selectedOption.value,
      });
    } else {
      setNewUserForm({
        ...newUserForm,
        location: null,
      });
    }
  };

  const handleChangeSelect = (selectedOption) => {
    const name = selectedOption.name;
    if (name === "user_group") {
      setNewUserForm({
        ...newUserForm,
        [name]: selectedOption.value,
      });

      if (selectedOption.value === 4) {
        // Check if the object is already in the array
        const allLocationsObject = { value: null, label: "All Locations" };
        if (
          !locations.some(
            (location) => location.value === allLocationsObject.value
          )
        ) {
          locations.unshift(allLocationsObject);
        }
      } else {
        // Remove the object if it's present in the array
        const allLocationsObjectIndex = locations.findIndex(
          (location) => location.value === null
        );
        if (allLocationsObjectIndex !== -1) {
          locations.splice(allLocationsObjectIndex, 1);
        }
      }

      // Check if "Admin" is selected and update the "Location" field requirement
      setLocationRequired(selectedOption.value === 2);
    } else {
      setNewUserForm({
        ...newUserForm,
        [name]: selectedOption.value,
      });
    }
  };

  const generateUserGroupsOptions = () => {
    const groupOptions = [
      { value: 1, label: "Super Admin", name: "user_group" },
      { value: 2, label: "Admin", name: "user_group" },
      { value: 3, label: "Accounting", name: "user_group" },
      { value: 4, label: "Standard", name: "user_group" },
    ];

    if (session.user.group_id === 1) {
      // Group ID is 1
      if (locations.length > 1) {
        // Locations > 1, show all options
        return groupOptions;
      } else {
        // Locations = 1, remove or disable Admin option
        return groupOptions.filter((option) => option.value !== 2);
      }
    } else if (session.user.group_id === 2) {
      // Group ID is 2, remove or disable Super Admin and Accounting options
      return groupOptions.filter(
        (option) => option.value !== 1 && option.value !== 3
      );
    }

    return groupOptions; // Default case
  };

  const user_groups = generateUserGroupsOptions();

  const phone_types = [
    { value: "Mobile", label: "Mobile", name: "phone_type" },
    { value: "Work", label: "Work", name: "phone_type" },
  ];

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setNewUserForm({
      ...newUserForm,
      phone: cleanedInput,
    });
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  const emailCheck = async (value) => {
    console.log(value);
    try {
      const response = await fetch(`/api/user/verifyByEmail?email=${value}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const responseData = await response.json();

        if (responseData === 0) {
          console.log("user ID", responseData);
          return 1;
        } else {
          console.log("user ID", responseData);
          return 0;
        }
      }
    } catch (error) {
      // Handle any errors that occurred during the API call
      console.error("Error:", error);
    }
  };

  const successUserNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: "User Created Successfully.",
      show: true,
    };


    setAlert(newNotification);
  };

  const failureUserNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    setAlert(newNotification);
  };


  const createUser = (e) => {
    e.preventDefault();
    setEmailAlert(false);
    setSyncing(true);
    const email = newUserForm.email;
    const password = newUserForm.password;
    const password_confirm = newUserForm.password_confirm;

    emailCheck(email).then((response) => {
      if (response == 1) {
        console.log("success", newUserForm);
        if (password !== password_confirm) {
          setSyncing(false);
          failureUserNotification("Passwords do not match.");

          return;
        } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/.test(password)) {
          setSyncing(false);
          failureUserNotification(
            "Password does not meet requirements (8 characters, 1 uppercase, 1 lowercase, 1 number)"
          );
          return;
        } else {
          if (newUserForm.location == "") {
            setNewUserForm({
              ...newUserForm,
              location: null,
            });
          }
          axios.post("api/user/create", newUserForm).then((response) => {
            if (response.status == 200) {
              //need to pass Contact data to this, to then add to JDE
              console.log("response", response.data);
              axios
                .post("api/syncJDE/addContact", response.data)
                .then((response) => {
                  if (response.status == 200) {
                    successUserNotification();
                    setModal(false);
                    setSyncing(false);
                    onUpdate();
                    dataProp();
                  } else {
                    failureUserNotification("Unable to create user. Please try again.");
                  }
                })
                .catch((error) => console.error(error));
            } else {
              setSyncing(false);
              failureUserNotification("Unable to create user. Please try again.");
            }
          });
        }
      } else {
        setSyncing(false);
        failureUserNotification("Email taken. Please try another.");
        return;
      }
    });
  };

  const dataProp = () => {
    setNewUserForm({
      first: "",
      last: "",
      title: "",
      email: "",
      password: "",
      password_confirm: "",
      user_group: "",
      location: null,
      phone: "",
      phone_type: null,
      fax: "",
      jde_id: session.user.jde_id,
      account_id: session.user.account_id,
    });
  };

  useEffect(() => {
    console.log(newUserForm);
  }, [newUserForm]);

  return (
    <form onSubmit={createUser}>
      <Modal.Header>New User</Modal.Header>
      <Modal.Body>
        <div className="text-sm text-gray-500">* fields are required</div>
        <div className="grid gap-4 mb-4  sm:grid-cols-2">
          <div>
            <label
              htmlFor="first"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="first"
              required={true}
              className="custom-input"
              placeholder="* First Name"
              value={newUserForm.first}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="last"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="last"
              required={true}
              className="custom-input"
              placeholder="* Last Name"
              value={newUserForm.last}
              onChange={handleChange}
            />
          </div>
          <div>
            <label
              htmlFor="title"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="title"
              className="custom-input"
              placeholder="Contact Title"
              value={newUserForm.title}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor="email"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="email"
              required={true}
              className="custom-input"
              placeholder="* Email"
              value={newUserForm.email}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="password"
              name="password"
              required={true}
              className="custom-input"
              placeholder="* Password"
              value={newUserForm.password}
              onChange={handleChange}
            />
          </div>

          <div>
            <label
              htmlFor="password_confirm"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="password"
              name="password_confirm"
              required={true}
              className="custom-input"
              placeholder="* Confirm Password"
              value={newUserForm.password_confirm}
              onChange={handleChange}
            />
          </div>
          {/* <div><p>Your password must be at least 8 characters long, contain at least one lower case letter, at least one uppercase letter, and at least one letter.</p></div> */}
          <div>
            <label
              htmlFor="user_group"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={user_groups}
              name="user_group"
              required={true}
              placeholder="* User Type"
              className="custom-select text-sm"
              value={
                newUserForm.user_group !== ""
                  ? user_groups.find(
                      (option) => option.value === newUserForm.user_group
                    )
                  : ""
              }
              onChange={handleChangeSelect}
            />
          </div>

          <div>
            <label
              htmlFor="location"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={locations}
              name="location"
              required={locationRequired}
              placeholder={locationRequired ? "* Location" : "Location"}
              isClearable={true}
              isSearchable={true}
              className="custom-select text-sm"
              value={
                newUserForm.location !== ""
                  ? locations.find(
                      (option) => option.value === newUserForm.location
                    )
                  : ""
              }
              onChange={handleChangeSelectLocation}
            />
            {locationRequired && (
              <p className="text-xs text-[#6A7280]">
                * Location is required for admin level users.
              </p>
            )}
          </div>
        </div>
        <div className="grid gap-4 mb-4 sm:grid-cols-3">
          <div>
            <label
              htmlFor="phone"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="phone"
              maxLength={14}
              className="custom-input"
              placeholder="Phone Number"
              value={formatPhone}
              onChange={handlePhoneChange}
            />
          </div>

          <div>
            <label
              htmlFor="phone_type"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <Select
              menuPosition={"fixed"}
              options={phone_types}
              name="phone_type"
              className="custom-select text-sm"
              placeholder="Mobile or Work?"
              value={
                newUserForm.phone_type !== ""
                  ? phone_types.find(
                      (option) => option.value === newUserForm.phone_type
                    )
                  : ""
              }
              onChange={handleChangeSelect}
            />
          </div>

          <div>
            <label
              htmlFor="fax"
              className="block mb-2 text-sm font-medium text-gray-900 dark:text-white"
            ></label>
            <TextInput
              type="text"
              name="fax"
              className="custom-input"
              placeholder="Fax"
              value={newUserForm.fax}
              onChange={handleChange}
            />
          </div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <div className="flex flex-col justify-center mx-auto">
          {emailAlert.alertShow && (
            <p className="m-auto mb-2 text-red-500">
              {emailAlert.alertMessage}
            </p>
          )}
          <Button
            className="m-auto theme-button w-52 hover:bg-primary-dark focus:ring-0 focus:ring-transparent"
            type="submit"
            pill={true}
            isProcessing={syncing}
          >
            Add New User
          </Button>
        </div>
      </Modal.Footer>
    </form>
  );
}

export default AddUser;
