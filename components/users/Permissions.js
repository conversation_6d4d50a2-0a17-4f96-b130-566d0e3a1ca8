import { useSession } from "next-auth/react";
import { But<PERSON>, Checkbox, Modal } from "flowbite-react";
import { useEffect, useState } from "react";
import axios from "axios";
import { useGlobalAlertContext } from "../../contexts/GlobalAlertContext";

const groups = [
  { id: 2, name: "Admin" },
  { id: 3, name: "Accounting" },
  { id: 4, name: "Standard" },
];

const groupPermissionsBySection = (permissions) => {
  return permissions.reduce((acc, perm) => {
    if (!acc[perm.section]) acc[perm.section] = [];
    acc[perm.section].push(perm);
    return acc;
  }, {});
};

const Permissions = () => {
  const [permissionsByGroup, setPermissionsByGroup] = useState({});
  const [allPermissions, setAllPermissions] = useState([]);
  const [initialPermissions, setInitialPermissions] = useState({});
  const [showResetModal, setShowResetModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const { notifications, addNotifications } = useGlobalAlertContext();

  const { data: session } = useSession();

  const successNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: message,
      show: true,
    };
    addNotifications(newNotification);
  };

  const failureNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: message,
      show: true,
    };

    addNotifications(newNotification);
  };

  useEffect(() => {
    const fetchPermissions = async () => {
      try {
        const [allPermsRes, effectiveRes] = await Promise.all([
          axios.get("/api/permissions/getAll"),
          axios.get(`/api/permissions/getEffective?id=${session.user.account_id}`),
        ]);

        setAllPermissions(allPermsRes.data);
        setPermissionsByGroup(effectiveRes.data);
        setInitialPermissions(effectiveRes.data);
      } catch (err) {
        console.error("Failed to fetch permissions:", err);
      }
    };

    fetchPermissions();
  }, [session.user.account_id]);

  const isChecked = (groupId, permId) =>
    permissionsByGroup[groupId]?.includes(permId);

  const togglePermission = (groupId, permId) => {
    setPermissionsByGroup((prev) => {
      const groupPerms = prev[groupId] || [];
      return {
        ...prev,
        [groupId]: groupPerms.includes(permId)
          ? groupPerms.filter((id) => id !== permId)
          : [...groupPerms, permId],
      };
    });
  };

  const permissionsBySection = groupPermissionsBySection(allPermissions);
  const hasChanges = JSON.stringify(permissionsByGroup) !== JSON.stringify(initialPermissions);

  const formatActionName = (action) => {
    return action
      .replace(/_/g, " ")
      .replace(/([a-z])([A-Z])/g, "$1 $2")
      .replace(/\b\w/g, (c) => c.toUpperCase());
  };

  const handleReset = async () => {
    try {
      const res = await axios.post("/api/permissions/reset", {
        accountId: session.user.account_id,
      });
      setPermissionsByGroup(res.data);
      setInitialPermissions(res.data);
      setShowResetModal(false);
      successNotification("Permissions reset to default successfully.");
    } catch (err) {
      console.error("Reset failed:", err);
      failureNotification("Failed to reset permissions.");
    }
  };

  const handleCancel = () => {
    setPermissionsByGroup(initialPermissions);
    setShowCancelModal(false);
    successNotification("Changes canceled.");
  };

  const handleSave = async () => {
    try {
      await axios.post("/api/permissions/save", {
        accountId: session.user.account_id,
        permissions: permissionsByGroup,
      });
      setInitialPermissions(permissionsByGroup);
      setShowSaveModal(false);
      successNotification("Permissions saved successfully.");
    } catch (err) {
      console.error("Save failed:", err);
      failureNotification("Failed to save permissions.");
    }
  };

  const isComingSoon = (section, action) => {
  return (
    (section === "My Orders" && action === "approve") ||
    (section === "Checkout" && action === "request_order")
  );
};

  return (
    <div className="w-full overflow-x-auto -mt-3">
      <div className="grid grid-cols-1 divide-y">
        {groups.map((group) => (
          <div key={group.id} className="px-6 pb-6 lg:px-8">
            <div className="py-4 font-semibold text-base">{group.name}</div>

            {Object.entries(permissionsBySection).map(([section, perms]) => (
              <div key={section} className="mb-5 ml-4">
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-y-3 gap-x-4">
                  <div className="text-xs font-bold text-gray-700">{section}</div>

                  {perms.map((perm) => {
                    const isView = perm.action === "view";
                    const hasView = perms.some((p) => p.action === "view");
                    const viewPerm = perms.find((p) => p.action === "view");
                    const isViewEnabled = hasView && isChecked(group.id, viewPerm?.id);

                    const isCheckoutSection = section.toLowerCase() === "checkout";
                    const isCheckedThis = isChecked(group.id, perm.id);

                    const isDisabled = hasView
                      ? !isView && !isViewEnabled
                      : false;

                    const handleChange = () => {
                      if (isCheckoutSection) {
                        setPermissionsByGroup((prev) => {
                          const groupPerms = prev[group.id] || [];
                          const checkoutPermIds = perms
                            .filter((p) => p.action === "place_order" || p.action === "request_order")
                            .map((p) => p.id);

                          if (groupPerms.includes(perm.id)) {
                            return {
                              ...prev,
                              [group.id]: groupPerms.filter((id) => id !== perm.id),
                            };
                          }

                          return {
                            ...prev,
                            [group.id]: [
                              ...groupPerms.filter((id) => !checkoutPermIds.includes(id)),
                              perm.id,
                            ],
                          };
                        });
                      } else {
                        togglePermission(group.id, perm.id);
                      }
                    };
                    const comingSoon = isComingSoon(section, perm.action);
                    const isDisabledFinal = comingSoon || isDisabled;
                    return (
                      <label
                        key={`${group.id}-${perm.id}`}
                        className={`flex items-center text-xs font-normal ${
                          isDisabledFinal ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                        title={comingSoon ? "Coming soon" : ""}
                      >
                        <Checkbox
                          id={`${group.id}-${perm.id}`}
                          className="checked:bg-primary bg-transparent"
                          checked={isCheckedThis}
                          onChange={handleChange}
                          disabled={isDisabledFinal}
                        />
                        <span className="ml-3 text-gray-800">
                          {formatActionName(perm.action)}
                          {comingSoon && <span className="ml-1 text-[10px] text-orange-500">(Coming soon)</span>}
                        </span>
                      </label>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        ))}

        <div className="px-6 pt-9 lg:px-8 flex justify-between">
          <Button
            pill
            className="secondary-button w-44"
            onClick={() => setShowResetModal(true)}
          >
            Reset to Default
          </Button>
          <div className="flex gap-6">
            <Button
              pill
              className="secondary-button w-44"
              onClick={() => setShowCancelModal(true)}
              disabled={!hasChanges}
            >
              Cancel
            </Button>
            <Button
              pill
              className="theme-button w-44"
              onClick={handleSave}
              disabled={!hasChanges}
            >
              Save Changes
            </Button>
          </div>
        </div>
      </div>

      {/* Confirmation Modals (same as before) */}
      <Modal show={showResetModal} onClose={() => setShowResetModal(false)}>
        <Modal.Header>Are you sure you want to Reset to Default?</Modal.Header>
        <Modal.Body>
          <p className="text-center">
            Resetting to Default will set all User Groups to their original settings.
          </p>
        </Modal.Body>
        <div className="flex justify-center gap-5 py-4">
          <Button onClick={() => setShowResetModal(false)} pill className="secondary-button w-52">
            No
          </Button>
          <Button pill className="theme-button w-52" onClick={handleReset}>
            Yes, Reset to Default
          </Button>
        </div>
      </Modal>

      <Modal show={showCancelModal} onClose={() => setShowCancelModal(false)}>
        <Modal.Header>Are you sure you want to cancel?</Modal.Header>
        <Modal.Body>
          <p className="text-center">Canceling will reset any changes you have made.</p>
        </Modal.Body>
        <div className="flex justify-center gap-5 py-4">
          <Button onClick={() => setShowCancelModal(false)} pill className="secondary-button w-52">
            No
          </Button>
          <Button pill className="theme-button w-52" onClick={handleCancel}>
            Yes, Cancel
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default Permissions;
