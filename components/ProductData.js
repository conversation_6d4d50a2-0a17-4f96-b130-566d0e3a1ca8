export function filterList(items, query) {
  query = query.toLowerCase();

  // return items.filter((item) =>
  //   item.sub.split(" ").some((word) => word.toLowerCase().startsWith(query))
  // );

  let filter = items.filter(
    (item) =>
      item.sku.toLowerCase().includes(query) ||
      item.name.toLowerCase().includes(query) ||
      item.description.toLowerCase().includes(query)
  );
  return filter;
}

// function pull() {
//     const { data, isLoading } = useSWR("/api/products", fetcher);
//     console.log(data)
// }

// pull()

// export const ProductData = [
//   {
//     sku: "test",
//     name: "name",
//     description: "testtest",
//   },
// ];
