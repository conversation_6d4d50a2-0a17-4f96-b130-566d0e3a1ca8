import { But<PERSON>, Label, TextInput, Alert, Modal, Badge } from "flowbite-react";
import { useState, useEffect } from "react";
import { useSession, signOut } from "next-auth/react";
import Select from "react-select";
import axios from "axios";
import { HiOutlineExclamationCircle } from "react-icons/hi";import { usePermissions } from "../../contexts/PermissionsContext";
import { createPermissionChecker } from "../utils";
import { BiSolidBookAlt } from "react-icons/bi";
import BillingAddressBook from "./BillingAddressBook";

export default function BillingView({
  name,
  current,
  stateNames,
  locations,
  alert,
  setAlert,
  onUpdate,
}) {
  const [modal, setModal] = useState(false);
  const [cardInfo, setCardInfo] = useState({});
  const [processing, setProcessing] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const { data: session, status } = useSession();
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setCardInfo({ ...cardInfo, [name]: value });
  };

  const handleChangeSelectState = (selectedOption) => {
    setCardInfo({
      ...cardInfo,
      state: selectedOption.value,
    });
  };

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setCardInfo({ ...cardInfo, phone: cleanedInput });
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  const dataProp = () => {
    setCardInfo({
      user_id: session.user.id,
      id: current.db.id,
      nickname: current.db.nickname,
      expiration_date: current.db.expiry,
      // cvc: current.cardpointe...,
      card_number: "",
      company_name: current.db.Billing.company,
      company_website: current.db.Billing.website,
      address: current.db.Billing.address,
      address_2: current.db.Billing.address_2,
      city: current.db.Billing.city,
      state: current.db.Billing.state,
      country: current.db.Billing.country,
      zipcode: current.db.Billing.postal_code,
      phone: current.db.Billing.phone,
      card_owner: current.cardpointe[0].name,
      token: current.db.token,
      profileid: current.db.profileid,
      accttype: current.cardpointe[0].accttype,
      account_id: session.user.account_id,
      default: current.db.default === 1 ? "Y" : "N",
    });
    let formattedPhone = "";
    for (let i = 0; i < current.db.Billing.phone.length; i++) {
      if (i === 0) {
        formattedPhone = `(${current.db.Billing.phone[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${current.db.Billing.phone[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${current.db.Billing.phone[i]}`;
      } else {
        formattedPhone += current.db.Billing.phone[i];
      }
    }
    setFormatPhone(formattedPhone);
  };

  const handleClickScroll = () => {
    const element = document.getElementById("header-section");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const successUpdateNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: (
        <span>
          {message}
        </span>
      ),
      show: true,
    };

    setAlert(newNotification);
  };

  const failureUpdateNotification = (message) => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: <span>{message}</span>,
      show: true,
    };

    setAlert(newNotification);
  };

  const save = (e) => {
    e.preventDefault();
    setProcessing(true);
    // console.log("func to update added card");
    // console.log(cardInfo);
    // console.log(session.user.location_id);
    const user_id = session.user.id;
    axios
      .put("/api/billing/update", {
        cardInfo,
        user: session.user.id,
        location: session.user.location_id,
      })
      .then((response) => {
        handleClickScroll();

        successUpdateNotification('Card:'+response.data.nickname+' updated successfully.');
        setCardInfo({});
        onUpdate(user_id);
        setProcessing(false);
      })
      .catch((error) => {
        failureUpdateNotification('Unable to update Card. Please try again.');
        setProcessing(false);

      });
  };

  const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const handleRemove = (e) => {
    e.preventDefault();
    setDeleting(true);

    const user_id = session.user.id;
    axios
      .post("/api/billing/delete", {
        id: current.db.id
      })
      .then((response) => {
        handleClickScroll();
        setOpenDeleteModal(false);
        successUpdateNotification("Card removed successfully");
        setCardInfo({});
        onUpdate(user_id);
        setDeleting(false);
      })
      .catch((error) => {
        failureUpdateNotification("Unable to remove card. Please try again.");
        setDeleting(false);
        setOpenDeleteModal(false);

      });
  };

  useEffect(() => {
    dataProp(current);
  }, [current]);

  // console.log(cardInfo);

  return (
    <div className="basis-3/4">
      <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
        <div className="py-[14px] pl-[30px] pr-[30px] border-b-2 border-[#EFF1F0]">
          <form onSubmit={save}>
            <h1 className="text-[#353535] text-[20px] font-semibold">
              Edit Card Info
            </h1>

            <div className="mb-2 block mt-[30px] px-[80px]">
              <Label htmlFor="firstName" value="Nickname For Card" />

              <TextInput
                type="text"
                placeholder="Nickname"
                required={true}
                name="nickname"
                className="custom-input"
                onChange={handleChange}
                value={cardInfo["nickname"]}
              />
            </div>

            <div className="mb-2 block mt-[30px] px-[80px]">
              <Label htmlFor="firstName" value="Name On Card" />

              <TextInput
                type="text"
                placeholder="First and Last Name"
                name="card_owner"
                required={true}
                className="custom-input"
                onChange={handleChange}
                value={cardInfo["card_owner"]}
              />
            </div>

            <div className="mb-2 block mt-[30px] px-[80px]">
              <Label htmlFor="firstName" value="Card Number" />

              <TextInput
                type="text"
                placeholder={"**** **** **** " + current.db.last_four}
                required={true}
                name="card_number"
                className="custom-input"
                onChange={handleChange}
                value={cardInfo["card_number"]}
              />
            </div>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="Expire Date" />

                <TextInput
                  type="text"
                  placeholder="MM/YY"
                  required={true}
                  className="custom-input"
                  name="expiration_date"
                  onChange={handleChange}
                  value={cardInfo["expiration_date"]}
                />
              </div>

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="cvc" value="CVC/CVV" />

                <TextInput
                  type="text"
                  placeholder="1234"
                  className="custom-input"
                  name="cvc"
                  onChange={handleChange}
                  value={cardInfo["cvc"]}
                />
              </div>
            </div>

            <h1 className="text-[#353535] text-[20px] font-semibold mt-[80px]">
              Billing Info
            </h1>

            <div
              className="flex flex-grid  px-[80px] items-center pt-2 cursor-pointer"
              onClick={() => {
                setModal(true);
              }}
            >
              <BiSolidBookAlt className="mr-2 h-6 w-6" />
              Address Book
            </div>

            <Modal
              show={modal}
              onClose={() => {
                setModal(false);
              }}
              size="3xl"
            >
              <BillingAddressBook
                locations={locations}
                setModal={setModal}
                cardInfo={cardInfo}
                setCardInfo={setCardInfo}
              />
            </Modal>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="company_name" value="Company Name" />

                <TextInput
                  type="text"
                  placeholder="Company"
                  required={true}
                  className="custom-input"
                  name="company_name"
                  onChange={handleChange}
                  value={cardInfo["company_name"]}
                />
              </div>

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="company_website" value="Company Website" />

                <TextInput
                  type="text"
                  placeholder="Company Website"
                  className="custom-input"
                  name="company_website"
                  onChange={handleChange}
                  value={cardInfo["company_website"]}
                />
              </div>
            </div>

            <div className="mb-2 block mt-[30px] px-[80px] w-full">
              <Label htmlFor="address" value="Address" />

              <TextInput
                type="text"
                placeholder="Address"
                required={true}
                className="custom-input"
                name="address"
                onChange={handleChange}
                value={cardInfo["address"]}
              />
            </div>

            <div className="mb-2 block mt-[30px] px-[80px] w-full">
              <Label htmlFor="address_2" value="Address Line 2" />

              <TextInput
                type="text"
                placeholder="Address Line 2"
                className="custom-input"
                name="address_2"
                onChange={handleChange}
                value={cardInfo["address_2"]}
              />
            </div>

            <div className="flex flex-row px-[80px] gap-2">
              {/* <div className="mb-2 block mt-[30px] w-full">
              <Label htmlFor="firstName" value="Country" />

              <TextInput
                type="text"
                placeholder="Country"
                required={true}
                className="custom-input"
                name="country"
                onChange={handleChange}
                value={current.db.country}
              />
            </div> */}

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="zipcode" value="Postal/Zip Code" />

                <TextInput
                  type="text"
                  placeholder="Postal/Zip Code"
                  required={true}
                  className="custom-input"
                  name="zipcode"
                  onChange={handleChange}
                  value={cardInfo["zipcode"]}
                />
              </div>
            </div>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="city" value="City" />

                <TextInput
                  type="text"
                  placeholder="City"
                  required={true}
                  className="custom-input"
                  name="city"
                  onChange={handleChange}
                  value={cardInfo["city"]}
                />
              </div>

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="state" value="State" />

                <Select
                  options={stateNames}
                  onChange={handleChangeSelectState}
                  name="state"
                  placeholder="State"
                  className="custom-select"
                  value={{ label: cardInfo["state"], value: cardInfo["state"] }}
                />
              </div>
            </div>

            <div className="mb-2 block mt-[30px] px-[80px] w-full">
              <Label htmlFor="firstName" value="Phone Number" />

              <TextInput
                type="text"
                placeholder="(*************"
                className="custom-input"
                name="phone"
                maxLength={14}
                value={formatPhone}
                onChange={handlePhoneChange}
              />
            </div>

            <div className="flex flex-row px-[80px]">
              {/* <div className="mb-2 block mt-[30px] w-full">
                <input
                  id="firstName"
                  type="checkbox"
                  required={true}
                  className="custom-input"
                />
                <Label
                  htmlFor="firstName"
                  value="Save this address for later use"
                  className="p-2"
                />
              </div> */}

              <div className="mb-2 block mt-[30px] w-full">
                <input type="checkbox" className="custom-input" />
                <Label
                  value="Set as account primary payment method"
                  className="p-2"
                />
              </div>
            </div>

            {/* <Button onClick={handleClickScroll}>scroll</Button> */}
            <div className="flex justify-between py-[30px]">
            {hasPermission('Billing', "delete") && (
              <Button
                  pill={true}
                  color="failure"
                  className="w-[168px] !px-4 !py-2"
                  onClick={() => setOpenDeleteModal(true)}
                >
                  {deleting ? "" : "Remove Card"}
                </Button>
              )}
              {hasPermission('Billing', "edit") && (
              <Button
                pill={true}
                disabled={processing}
                isProcessing={processing}
                className="theme-button w-[168px]"
                type="submit"
              >
                {processing ? "" : "Save Changes"}
              </Button>
              )}
            </div>
          </form>
        </div>
      </div>
      <Modal show={openDeleteModal} size="md" onClose={() => setOpenDeleteModal(false)} popup>
        <Modal.Header />
        <Modal.Body>
          <div className="text-center">
            <HiOutlineExclamationCircle className="mx-auto mb-4 h-14 w-14 text-gray-400 dark:text-gray-200" />
            <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              Remove card x{current.db.last_four} from the list of credit cards?
            </h3>
            <div className="flex justify-center gap-4">
              <Button pill={true} color="gray" className="w-[168px] !px-4 !py-2" onClick={() => setOpenDeleteModal(false)}>
                Cancel
              </Button>
              <Button pill={true} className="theme-button w-[168px]" onClick = {handleRemove} disabled={deleting} isProcessing={deleting}>
                Confirm
              </Button>
            </div>
          </div>
        </Modal.Body>
      </Modal>
    </div>
  );
}
