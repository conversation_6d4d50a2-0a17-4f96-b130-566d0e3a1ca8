import axios from "axios";
import { useState, useEffect, useRef } from "react";
import { Button, Label, TextInput, Alert, Modal } from "flowbite-react";
import { useRouter } from "next/router";
import { useSession, signOut } from "next-auth/react";
import Select from "react-select";
import { BiSolidBookAlt } from "react-icons/bi";
import BillingAddressBook from "./BillingAddressBook";
import { usePermissions } from "../../contexts/PermissionsContext";
import { createPermissionChecker } from "../utils";

export default function BillingView({
  newCard,
  setNewCard,
  stateNames,
  locations,
  onAdd,
  alert,
  setAlert,
}) {
  const [modal, setModal] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [saving, setSaving] = useState({});

  const router = useRouter();

  const { data: session, status } = useSession();
  const loading = status === "loading";
  const { permissions } = usePermissions();
  const hasPermission = createPermissionChecker(permissions, session?.user.group_id);

  const handleChange = (event) => {
    const { name, value } = event.target;
    setNewCard((prevState) => ({
      ...prevState,
      [name]: value,
    }));
  };

  const handleChangeSelectState = (selectedOption) => {
    setNewCard((prevState) => ({
      ...prevState,
      state: selectedOption.value,
    }));
  };

  const [formatPhone, setFormatPhone] = useState("");
  // Format the phone number with a mask as the user types
  const handlePhoneChange = (e) => {
    const input = e.target.value;

    // Remove non-numeric characters
    const cleanedInput = input.replace(/\D/g, "");
    setNewCard((prevState) => ({
      ...prevState,
      phone: cleanedInput,
    }));
    // Apply the mask
    let formattedPhone = "";
    for (let i = 0; i < cleanedInput.length; i++) {
      if (i === 0) {
        formattedPhone = `(${cleanedInput[i]}`;
      } else if (i === 3) {
        formattedPhone += `) ${cleanedInput[i]}`;
      } else if (i === 6) {
        formattedPhone += `-${cleanedInput[i]}`;
      } else {
        formattedPhone += cleanedInput[i];
      }
    }

    setFormatPhone(formattedPhone);
  };

  const handleClickScroll = () => {
    const element = document.getElementById("header-section");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  const successAddNotification = (name) => {
    const newNotification = {
      iconColor: {
        bg: "bg-green-100",
        text: "text-green-500",
      },
      bgColor: "bg-green-100",
      message: (
        <span>
          <span className="font-bold">{name}</span> added to Payment methods.
        </span>
      ),
      show: true,
    };
    setAlert(newNotification);
  };

  const failureBillingNotification = () => {
    const newNotification = {
      iconColor: {
        bg: "bg-red-100",
        text: "text-red-500",
      },
      bgColor: "bg-red-100",
      message: "Failed to add payment method. Please try again.",
      show: true,
    };

    setAlert(newNotification);
  };

  const save = (e) => {
    e.preventDefault();
    setSyncing(true);
    const user_id = session.user.id;
    const account_id = session.user.account_id;

    axios
      .post("/api/billing/add", {
        newCard,
        user: session.user.id,
        location: session.user.location_id,
        account_id: account_id,
      })
      .then((response) => {
        handleClickScroll();
        let cardtype = response.data.accttype;
        if (cardtype == "MC") {
          cardtype = "MASTERCARD";
        }
        if (cardtype == "????") {
          cardtype = newCard.nickname;
        }
        successAddNotification(cardtype);
        onAdd(user_id);
        setSyncing(false);
      })
      .catch((error) => {
        failureBillingNotification();
        setSyncing(false);
      });
  };

  return (
    <div className="basis-3/4">
      <div className="bg-white rounded-xl dark:bg-gray-800 mb-5">
        <div className="py-[14px] pl-[30px] pr-[30px] border-b-2 border-[#EFF1F0]">
          <form onSubmit={save}>
            <h1 className="text-[#353535] text-[20px] font-semibold">
              New Card Info
            </h1>

            <div className="mb-2 block mt-[30px] px-[80px]">
              <Label htmlFor="firstName" value="Nickname For Card" />

              <TextInput
                type="text"
                placeholder="Nickname"
                required={true}
                name="nickname"
                className="custom-input"
                onChange={handleChange}
                value={newCard.nickname}
              />
            </div>

            <div className="mb-2 block mt-[30px] px-[80px]">
              <Label htmlFor="firstName" value="Name On Card" />

              <TextInput
                type="text"
                placeholder="First and Last Name"
                name="card_owner"
                required={true}
                className="custom-input"
                onChange={handleChange}
                value={newCard.card_owner}
              />
            </div>

            <div className="mb-2 block mt-[30px] px-[80px]">
              <Label htmlFor="card_number" value="Card Number" />

              <TextInput
                type="text"
                placeholder="1234 5678 9012 3456"
                required={true}
                name="card_number"
                className="custom-input"
                onChange={handleChange}
                value={newCard.card_number}
              />
            </div>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="Expire Date" />

                <TextInput
                  type="text"
                  placeholder="MM/YY"
                  required={true}
                  className="custom-input"
                  name="expiration_date"
                  onChange={handleChange}
                  value={newCard.expiration_date}
                />
              </div>

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="CVC/CVV" />

                <TextInput
                  type="text"
                  placeholder="1234"
                  required={true}
                  className="custom-input"
                  name="cvc"
                  onChange={handleChange}
                  value={newCard.cvc}
                />
              </div>
            </div>

            <h1 className="text-[#353535] text-[20px] font-semibold mt-[80px]">
              Billing Info
            </h1>

            <div
              className="flex flex-grid  px-[80px] items-center pt-2 cursor-pointer"
              onClick={() => {
                setModal(true);
              }}
            >
              <BiSolidBookAlt className="mr-2 h-6 w-6" />
              Address Book
            </div>

            <Modal
              show={modal}
              onClose={() => {
                setModal(false);
              }}
              size="3xl"
            >
              <BillingAddressBook
                locations={locations}
                setModal={setModal}
                cardInfo={newCard}
                setCardInfo={setNewCard}
              />
            </Modal>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="Company Name" />

                <TextInput
                  type="text"
                  placeholder="Company"
                  className="custom-input"
                  name="company_name"
                  onChange={handleChange}
                  value={newCard.company_name}
                />
              </div>

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="Company Website" />

                <TextInput
                  type="text"
                  placeholder="Company Website"
                  className="custom-input"
                  name="company_website"
                  onChange={handleChange}
                  value={newCard.company_website}
                />
              </div>
            </div>

            <div className="mb-2 block mt-[30px] px-[80px] w-full">
              <Label htmlFor="firstName" value="Address" />

              <TextInput
                type="text"
                placeholder="Address"
                required={true}
                className="custom-input"
                name="address"
                onChange={handleChange}
                value={newCard.address}
              />
            </div>

            <div className="mb-2 block mt-[30px] px-[80px] w-full">
              <Label htmlFor="firstName" value="Address Line 2" />

              <TextInput
                type="text"
                placeholder="Address Line 2"
                className="custom-input"
                name="address_2"
                onChange={handleChange}
                value={newCard.address_2}
              />
            </div>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="Postal/Zip Code" />

                <TextInput
                  type="text"
                  placeholder="Postal/Zip Code"
                  required={true}
                  className="custom-input"
                  name="zipcode"
                  onChange={handleChange}
                  value={newCard.zipcode}
                />
              </div>
            </div>

            <div className="flex flex-row px-[80px] gap-2">
              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="City" />

                <TextInput
                  type="text"
                  placeholder="City"
                  required={true}
                  className="custom-input"
                  name="city"
                  onChange={handleChange}
                  value={newCard.city}
                />
              </div>

              <div className="mb-2 block mt-[30px] w-full">
                <Label htmlFor="firstName" value="State" />

                <Select
                  options={stateNames}
                  onChange={handleChangeSelectState}
                  required={true}
                  name="state"
                  placeholder="State"
                  className="custom-select "
                  value={
                    newCard.state !== ""
                      ? stateNames.find(
                          (option) => option.value === newCard.state
                        )
                      : ""
                  }
                />
              </div>
            </div>

            <div className="mb-2 block mt-[30px] px-[80px] w-full">
              <Label htmlFor="firstName" value="Phone Number" />

              <TextInput
                type="text"
                placeholder="(*************"
                className="custom-input"
                name="phone"
                maxLength={14}
                value={formatPhone}
                onChange={handlePhoneChange}
              />
            </div>

            <div className="flex flex-row px-[80px]">
              {/* <div className="mb-2 block mt-[30px] w-full">
                <input
                  id="firstName"
                  type="checkbox"
                  required={true}
                  className="custom-input"
                />
                <Label
                  htmlFor="firstName"
                  value="Save this address for later use"
                  className="p-2"
                />
              </div> */}

              <div className="mb-2 block mt-[30px] w-full">
                <input
                  id="firstName"
                  type="checkbox"
                  className="custom-input"
                />
                <Label
                  htmlFor="firstName"
                  value="Set as account primary payment method"
                  className="p-2"
                />
              </div>
            </div>

            <div className="flex justify-end py-[30px]">
              {syncing && (
                <div className="my-auto pr-4 animate-pulse text-lg">
                  Adding Card...
                </div>
              )}
              {hasPermission('Billing', "edit") && (
              <Button
                type="submit"
                isProcessing={syncing}
                disabled={syncing}
                pill={true}
                className="theme-button w-[168px] my-auto"
              >
                {syncing ? "" : "Add"}
              </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
