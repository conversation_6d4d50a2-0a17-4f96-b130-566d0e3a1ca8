import { Modal } from "flowbite-react";

const BillingAddressBook = ({ locations, setModal, cardInfo, setCardInfo }) => {
  return (
    <>
      <Modal.Header>Select Address</Modal.Header>
      <Modal.Body>
        <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
          <thead className="text-xs text-gray-700 uppercase bg-white dark:bg-gray-700 dark:text-gray-400">
            <tr>
              <th scope="col" className="px-6 py-3">
                Practice
              </th>
              <th scope="col" className="px-6 py-3">
                Address
              </th>
            </tr>
          </thead>
          <tbody>
            {(Array.isArray(locations) && locations.length > 0) ? (locations.map((item) => {
              return (
                <tr
                  className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:cursor-pointer hover:bg-gray-100"
                  onClick={() => {
                    setCardInfo({
                      ...cardInfo,
                      company_name: item.practice,
                      company_website: item.website,
                      address: item.address,
                      address_2: item.address_2,
                      city: item.city,
                      state: item.state,
                      zipcode: item.zip,
                      phone: item.phone,
                    });
                    setModal(false);
                  }}
                  key={item.id}
                >
                  <th
                    scope="row"
                    className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                  >
                    {item ? item.practice : "no data"}
                  </th>
                  <td className="px-6 py-4">
                    {item
                      ? item.address +
                        " " +
                        item.city +
                        ", " +
                        item.state +
                        " " +
                        item.zip
                      : "no data"}
                  </td>
                </tr>
              );
            })) : (
                <tr
                  className="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:cursor-pointer hover:bg-gray-100"
                  onClick={() => {
                    setCardInfo({
                      ...cardInfo,
                      company_name: locations.practice,
                      company_website: locations.website,
                      address: locations.address,
                      address_2: locations.address_2,
                      city: locations.city,
                      state: locations.state,
                      zipcode: locations.zip,
                      phone: locations.phone,
                    });
                    setModal(false);
                  }}
                  key={locations.id}
                >
                  <th
                    scope="row"
                    className="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white"
                  >
                    {locations ? locations.practice : "no data"}
                  </th>
                  <td className="px-6 py-4">
                    {locations
                      ? locations.address +
                        " " +
                        locations.city +
                        ", " +
                        locations.state +
                        " " +
                        locations.zip
                      : "no data"}
                  </td>
                </tr>
            )}
          </tbody>
        </table>
      </Modal.Body>
    </>
  );
};

export default BillingAddressBook;
