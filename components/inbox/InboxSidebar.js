// InboxSidebar.js
import {useEffect, useState } from "react";
import { Sidebar } from "flowbite-react";
import { FaEnvelope, FaEnvelopeOpen } from "react-icons/fa";

const InboxSidebar = ({ messages, onMessageClick, selectedMessage }) => {
  const [isMessageRead, setIsMessageRead] = useState({});

  useEffect(() => {
    // Update isMessageRead when messages change
    const updatedIsMessageRead = {};
    messages.forEach((message) => {
      updatedIsMessageRead[message.id] = message.open;
    });
    setIsMessageRead(updatedIsMessageRead);
  }, [messages]);

  const handleSelectMessage = (message) => {
    setIsMessageRead((prevReadState) => ({
      ...prevReadState,
      [message.id]: true,
    }));
    onMessageClick(message);
  };

  return (
    <div className="h-full w-full flex">
      <Sidebar
        aria-label="Sidebar"
        className="w-full product-sidebar hover: cursor-pointer"
      >
        <Sidebar.Items>
          <Sidebar.ItemGroup>
            {messages.map((message) => {
              const IconComponent = isMessageRead[message.id]
                ? FaEnvelopeOpen
                : FaEnvelope;

              return (
                <Sidebar.Item
                  key={message.id}
                  icon={IconComponent}
                  className={`text-[14px] font-medium whitespace-normal ${
                    message.id === selectedMessage?.id ? "bg-gray-200" : ""
                  }`}
                  onClick={() => handleSelectMessage(message)}
                >
                  {message.title}
                </Sidebar.Item>
              );
            })}
          </Sidebar.ItemGroup>
        </Sidebar.Items>
      </Sidebar>
    </div>
  );
};

export default InboxSidebar;
