// InboxPanel.js
import React from "react";

const InboxPanel = ({ selectedMessage }) => {
  return (
    <div className="flex flex-col h-full overflow-y-auto p-4">
      {selectedMessage ? (
        <div>
          <h3 className="text-xl font-bold mb-2">{selectedMessage.title}</h3>
          <hr/>
          <div
        className="html-notif" // Use the 'prose' class to style the HTML content similarly to the CKEditor
        dangerouslySetInnerHTML={{ __html: selectedMessage?.content }}
      />
        </div>
      ) : (
        <p className="text-gray-500">Select a message to view its content.</p>
      )}
    </div>
  );
};

export default InboxPanel;
