
// const Menu = () => {
//   const [categories, setCategories] = useState([
//     {
//       name: "Scaples & Knives",
//       sub: [
//         "Groove",
//         "Stab Incision",
//         "Slit Keratome",
//         "Clear Cornea",
//         "Implant",
//         "Crescent Tunnel",
//         "Round Tunnel",
//         "MVR",
//         "Scleral",
//         "Myringotomy",
//         "Mini Blades",
//       ],
//     },

//     {
//       name: "Cannulas & Needles",
//       sub: ["none"],
//     },
//     {
//       name: "Pupil Expansion Devices",
//       sub: ["none"],
//     },
//     {
//       name: "Collagen Shield",
//       sub: ["none"],
//     },
//     {
//       name: "Visco Shield",
//       sub: ["none"],
//     },
//     {
//       name: "PVA Foam Products",
//       sub: ["none"],
//     },
//     {
//       name: "Accessories",
//       sub: ["none"],
//     },
//     {
//       name: "Practice & Patient Materials",
//       sub: ["none"],
//     },
//   ]);

//   return (
//     <div className="w-fit text-sm">
//       <Sidebar>
//         <Sidebar.Items>
//           <Sidebar.ItemGroup>
//             {categories.map((item) => (
//               <Sidebar.Collapse label={item.name}>
//                 {item.sub.map((i) => (
//                   <Sidebar.Item href="#">{i}</Sidebar.Item>
//                 ))}
//               </Sidebar.Collapse>
//             ))}
//           </Sidebar.ItemGroup>
//         </Sidebar.Items>
//       </Sidebar>
//     </div>
//   );
// };