import { useRouter } from "next/router";
import { useEffect } from "react";
import { usePermissions } from "../contexts/PermissionsContext";
import { useUserAcctContext } from "../contexts/UserAcctContext";
import { createPermissionChecker } from "./utils"; // already in your project

export default function RouteGuard({ section, action = "view", children }) {
  const router = useRouter();
  const { userAcct } = useUserAcctContext();
  const { permissions, loading } = usePermissions();

  const hasPermission = createPermissionChecker(permissions, userAcct?.user.group_id);

  useEffect(() => {
    if (!loading && (!userAcct || !hasPermission(section, action))) {
      router.replace("/dashboard");
    }
  }, [loading, userAcct, permissions]);

  if (loading || !userAcct) return null;

  return hasPermission(section, action) ? children : null;
}
