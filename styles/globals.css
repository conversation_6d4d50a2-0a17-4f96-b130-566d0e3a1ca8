@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .custom-tab .active {
    @apply border-primary text-primary focus:ring-0;
  }

  .custom-tab > button {
    @apply w-2/4 focus:ring-0;
  }
  .custom-tab2 .active {
    @apply border-primary text-primary focus:ring-0;
  }

  .custom-tab2 > button {
    @apply w-1/4 focus:ring-0 text-xl text-[#929791];
  }

  .custom-input input {
    @apply border-none bg-[#F8F8F8] text-[#353535] font-light;
  }
  .custom-input svg {
    @apply text-[#353535] w-3 ml-2;
  }
  .custom-input-failure input {
    @apply border-[#C81E1D] bg-[#FDF1F2] text-[#353535] placeholder-[#C81E1D] font-light;
  }
  .custom-select .css-1jqq78o-placeholder {
    @apply text-sm text-[#353535] font-light;
  }
  .custom-select .css-tj5bde-Svg {
    @apply text-[#929791];
  }
  .custom-select .css-1u9des2-indicatorSeparator {
    @apply w-0;
  }
  .custom-select .css-13cymwt-control {
    @apply border-none bg-[#F8F8F8] text-[#353535] font-light;
  }

  .custom-select .css-6vykvi-control {
    @apply border-none bg-[#F8F8F8] text-[#353535] font-light;
  }
  .custom-select .css-1at1bs9-placeholderr {
    @apply text-sm text-[#353535] font-light;
  }

  .custom-select-failure .css-1jqq78o-placeholder {
    @apply text-sm font-light text-[#C81E1D];
  }

  .custom-select-failure .css-1u9des2-indicatorSeparator {
    @apply w-0;
  }

  .custom-select-failure .css-13cymwt-control {
    @apply border-[#C81E1D] bg-[#FDF1F2] font-light;
  }
  .acc-content:not([hidden]) {
    @apply grid;
  }
  .SearchBox {
    @apply bg-black;
  }
  .custom-radio {
    @apply !text-primary;
  }
  .sidebar-item-active {
    @apply bg-primary text-white hover:bg-primary-dark;
  }
  .sidebar-item-active svg {
    @apply text-white;
  }
  .sidebar-item-active span {
    @apply text-white;
  }
  .sidebar-item-active:hover {
    @apply hover:bg-primary-dark hover:text-white;

    > svg {
      stroke: white;
      fill: white;
      color: white;
    }
  }

  .sidebar-subitem-active span {
    @apply text-primary;
  }
  .theme-button {
    @apply !bg-primary !px-4 !py-2 hover:!bg-primary-dark focus:!ring-0 focus:!ring-transparent;
  }
  .secondary-button {
    @apply !bg-white !text-primary !border-primary !px-4 !py-2 hover:!bg-gray-100 focus:!ring-0 focus:!ring-transparent;
  }
  .product-sidebar div {
    @apply bg-white;
  }

  .custom-wrap span {
    @apply whitespace-normal;
  }

  .image-wrapper {
    position: relative;
    padding-top: 56.25%; /* This is the aspect ratio (9/16 * 100) for a 16:9 aspect ratio */
    overflow: hidden;
  }
  
  .image-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  
  @media print {
    body * {
      visibility: hidden;
    }
    .modal-content,
    .modal-content * {
      visibility: visible;
    }
    .modal-content {
      /* position: absolute;
          left: 0;
          top: 0; */
      /* width: 100% !important;
            max-width: 100% !important;
            max-height: 100%  !important;
            height: 100%  !important; */
    }
    .modal-print {
      /* visibility: visible; */
      display: block !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      max-width: 100% !important;
      max-height: 100% !important;
      height: 100% !important;
    }
  }

  .fade-out {
    transition: opacity 0.5s ease;
  }

  .product-details {
    ul {
      list-style-type: disc;
    }
  }
  /* ckeditor css */
  .html-notif {
    ul {
      list-style-type: disc;
      padding-inline-start: 40px;
    }
    ol {
        list-style-type: auto;
        padding-inline-start: 40px;
      }
      blockquote {
        border-left: 5px solid #ccc;
        font-style: italic;
        margin-left: 0;
        margin-right: 0;
        overflow: hidden;
        padding-left: 1.5em;
        padding-right: 1.5em;
        padding: 12px 24px;
        margin: 0 0 24px;
        font-size: 16.25px;
    }
    blockquote p:last-child, blockquote ul:last-child, blockquote ol:last-child {
        margin-bottom: 0;
    }
    hr {
        background: #dedede;
        border: 0;
        height: 4px;
        margin: 15px 0;
    }
    p {
        letter-spacing: 0.1px;
        margin: 0 0 12px;
    }
    .table {
        display: table;
        margin: 0.9em auto;
        width: 100%;
        max-width: 100%;
        margin-bottom: 24px;
    }
    .table table {
        border: 1px double #b3b3b3;
        border-collapse: collapse;
        border-spacing: 0;
        height: 100%;
        width: 100%;
    }
    .table table td, .table table th {
        border: 1px solid #bfbfbf;
        min-width: 2em;
        padding: 0.4em;
    }
    .table table td, .table table th {
        border: 1px solid #bfbfbf;
        min-width: 2em;
        padding: 0.4em;
    }
    .table td, .table th {
        overflow-wrap: break-word;
        position: relative;
    }
    .image {
        clear: both;
        display: table;
        margin: 0.9em auto;
        min-width: 50px;
        text-align: center;
    }
    .image.image_resized {
        box-sizing: border-box;
        display: block;
        max-width: 100%;
    }
    .image.image_resized img {
        width: 100%;
    }
    .image img {
        display: block;
        height: auto;
        margin: 0 auto;
        max-width: 100%;
        min-width: 100%;
    }
    .image-style-side {
        float: right;
        margin-left: 1.5em;
        max-width: 50%;
    }
    p+.image-style-align-left, p+.image-style-align-right, p+.image-style-side {
        margin-top: 0;
    }
    .image-inline {
        align-items: flex-start;
        display: inline-flex;
        max-width: 100%;
    }
    .image-inline img, .image-inline picture {
        flex-grow: 1;
        flex-shrink: 1;
        max-width: 100%;
    }
    .image.image_resized>figcaption {
        display: block;
    }
    .image>figcaption {
        background-color: #f7f7f7;
        caption-side: bottom;
        color: #333;
        display: table-caption;
        font-size: .75em;
        outline-offset: -1px;
        padding: 0.6em;
        word-break: break-word;
    }

    a:hover, a:focus {
        color: #0a6ebd;
        text-decoration: underline;
    }
  }
}
