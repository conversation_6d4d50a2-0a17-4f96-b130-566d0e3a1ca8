# OASIS v2 - Medical Supply E-Commerce Platform

OASIS v2 is a comprehensive B2B e-commerce platform designed specifically for healthcare professionals and medical practices. Built by OASIS Medical Inc., this platform enables medical practitioners to browse, order, and manage medical supplies with features tailored for the healthcare industry.

## 🏥 Overview

OASIS v2 serves as a digital marketplace connecting medical practices with OASIS Medical's product catalog. The platform supports multi-location practices, recurring orders, complex billing arrangements, and integrates with JDE (JD Edwards) ERP system for seamless business operations.

### Key Features

- **Product Catalog Management** - Browse medical supplies by categories with detailed product information
- **Multi-Location Support** - Manage multiple practice locations under one account
- **Recurring Orders** - Set up automatic delivery schedules for regular supplies
- **Advanced Billing** - Support for various payment methods, tax exemptions, and billing arrangements
- **Order Management** - Track orders from placement to delivery with real-time status updates
- **User Permissions** - Role-based access control for different user types
- **Training Resources** - Access to product training materials and resources
- **Return Management** - Handle product returns with RMA (Return Merchandise Authorization) system
- **JDE Integration** - Seamless synchronization with backend ERP system

## 🛠 Technology Stack

- **Frontend**: Next.js 13, React 18, Tailwind CSS, Flowbite React
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: MySQL
- **Authentication**: NextAuth.js with JWT
- **Payment Processing**: CardPointe API
- **Shipping**: FedEx API integration
- **State Management**: React Context API
- **UI Components**: Flowbite, React Icons
- **Date Handling**: date-fns, Luxon
- **HTTP Client**: Axios
- **Form Handling**: React Hook Form (via react-select, react-datepicker)

## 📁 Project Structure

```bash
oasisv2-eval/
├── components/           # Reusable React components
│   ├── billing/         # Billing-related components
│   ├── checkout/        # Checkout process components
│   ├── dashNav/         # Dashboard navigation
│   ├── formStepper/     # Multi-step form components
│   ├── inbox/           # Notification/message components
│   ├── location/        # Location management
│   ├── orders/          # Order-related components
│   ├── products/        # Product display components
│   ├── skeleton/        # Loading skeleton components
│   └── users/           # User management components
├── contexts/            # React Context providers
│   ├── CartContext.js   # Shopping cart state
│   ├── UserAcctContext.js # User account data
│   ├── PermissionsContext.js # User permissions
│   └── ...              # Other context providers
├── pages/               # Next.js pages and API routes
│   ├── api/             # Backend API endpoints
│   │   ├── auth/        # Authentication endpoints
│   │   ├── billing/     # Billing management
│   │   ├── cart/        # Shopping cart operations
│   │   ├── order/       # Order management
│   │   ├── products/    # Product catalog
│   │   ├── syncJDE/     # JDE integration
│   │   └── ...          # Other API endpoints
│   ├── dashboard.js     # Main dashboard page
│   ├── products.js      # Product catalog page
│   ├── cart.js          # Shopping cart page
│   ├── checkout.js      # Checkout process
│   └── ...              # Other pages
├── prisma/              # Database schema and migrations
│   └── schema.prisma    # Prisma database schema
├── public/              # Static assets
└── styles/              # CSS styles
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- MySQL 8.0+
- npm or yarn package manager

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd oasisv2-eval
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**

   Create a `.env.local` file in the root directory:

   ```bash
   # Database
   DATABASE_URL="mysql://username:password@localhost:3306/oasis_v2"

   # NextAuth
   NEXTAUTH_SECRET="your-nextauth-secret"
   NEXTAUTH_URL="http://localhost:3000"

   # CardPointe Payment Processing
   CARDPOINTE_USER="your-cardpointe-username"
   CARDPOINTE_PASS="your-cardpointe-password"
   CARDPOINTE_MERCH_ID="your-merchant-id"

   # Maintenance Mode
   NEXT_PUBLIC_MAINTENANCE_MODE="false"
   ```

4. **Set up the database**

   ```bash
   # Generate Prisma client
   npx prisma generate

   # Run database migrations
   npx prisma db push

   # (Optional) Seed the database
   npx prisma db seed
   ```

5. **Run the development server**

   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. **Open the application**

   Navigate to [http://localhost:3000](http://localhost:3000) in your browser.

### Build for Production

```bash
npm run build
npm start
```

## 🗄️ Database Schema Overview

The application uses a comprehensive MySQL database schema with the following key entities:

### Core Entities

- **Account** - Medical practice/organization accounts
- **User** - Individual users within accounts (doctors, staff, etc.)
- **Locations** - Physical practice locations
- **Product** - Medical supplies and equipment
- **Order** - Purchase orders with status tracking
- **Invoice** - Billing and payment tracking
- **Cart** - Shopping cart items
- **Payment** - Stored payment methods
- **Billing** - Billing address information

### Supporting Entities

- **Category/Filter** - Product categorization and filtering
- **Promotion** - Discount and promotional campaigns
- **ReturnForm** - Product return management
- **TrainingMaterials** - Educational resources
- **Notifications** - System notifications
- **Permission** - Role-based access control

### Key Relationships

- Accounts have multiple Users and Locations
- Users can have multiple Orders and Cart items
- Orders contain multiple Products through OrderedProduct
- Products belong to Categories and have Attributes
- Promotions can apply to specific Products or Order conditions

## 🔐 User Roles & Permissions

The platform supports role-based access control with the following user types:

### User Groups

1. **Primary User** - Account administrator with full access
2. **Standard User** - Regular practice staff with limited permissions
3. **View Only** - Read-only access to orders and invoices
4. **Admin** - System administrators (separate from practice users)

### Permission System

Permissions are organized by sections and actions:

- **Sections**: Dashboard, Products, Cart, Checkout, Orders, Invoices, Users, Locations, Settings
- **Actions**: view, add, edit, delete, approve, submit, pay

### Custom Permissions

Accounts can have custom permission overrides that modify the default role permissions for specific groups.

## 🔌 API Documentation

The application provides a comprehensive REST API built with Next.js API routes. All endpoints are located under `/api/`.

### Authentication

- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- Uses NextAuth.js with JWT tokens

### User Management

- `GET /api/user/get` - Get user information
- `POST /api/user/create` - Create new user
- `PUT /api/user/edit` - Update user details
- `DELETE /api/user/delete` - Delete user
- `POST /api/user/forgotPassword` - Password reset request
- `POST /api/user/resetPassword` - Reset password with token

### Product Catalog

- `GET /api/products/all?id={categoryId}` - Get products by category
- `GET /api/product?sku={productSku}` - Get single product details
- `GET /api/search/query?q={searchTerm}` - Search products
- `GET /api/categories/fetchCategories` - Get product categories
- `GET /api/filters/get` - Get product filters

### Shopping Cart

- `GET /api/getCart?account={accountId}&user_id={userId}` - Get cart items
- `POST /api/cart` - Add item to cart
- `POST /api/cart/updateQty` - Update item quantity
- `DELETE /api/cart/removeItemById` - Remove cart item

### Order Management

- `POST /api/order/create` - Create new order
- `GET /api/order/fetchAll` - Get user's orders
- `GET /api/order/fetchById?id={orderId}` - Get specific order
- `POST /api/order/cancelRecurring` - Cancel recurring order
- `POST /api/order/checkDuplicateRecurring` - Check for duplicate recurring orders

### Invoice Management

- `GET /api/invoice/all` - Get user's invoices
- `GET /api/invoice/fetchRange` - Get invoices by date range
- `GET /api/invoice/getForReturnList` - Get invoices for returns

### Location Management

- `GET /api/location/getList` - Get account locations
- `POST /api/location/add` - Add new location
- `PUT /api/location/edit` - Update location
- `DELETE /api/location/delete` - Delete location
- `POST /api/location/setDefault` - Set default location

### Billing & Payments

- `GET /api/billing/get` - Get billing information
- `POST /api/billing/add` - Add billing address
- `PUT /api/billing/update` - Update billing info
- `GET /api/billing/getTax` - Calculate tax for address

### JDE Integration

- `POST /api/syncJDE/syncAccount` - Sync account with JDE
- `POST /api/syncJDE/syncAll` - Full synchronization
- `POST /api/syncJDE/syncOrders` - Sync orders
- `POST /api/syncJDE/updateInvoices` - Update invoice status

## 👥 User Experience & Features

### For Medical Practice Staff

#### Dashboard

- **Order Overview** - View recent orders with status tracking
- **Invoice Summary** - Quick access to pending and paid invoices
- **Training Resources** - Access to product training materials
- **Notifications** - Important updates and announcements

#### Product Browsing

- **Categorized Catalog** - Browse products by medical specialty
- **Advanced Search** - Find products by name, SKU, or description
- **Product Details** - Comprehensive product information, images, and resources
- **Filtering** - Filter products by attributes and specifications

#### Ordering Process

- **Shopping Cart** - Add products and manage quantities
- **Recurring Orders** - Set up automatic deliveries (monthly, bi-monthly, quarterly)
- **Multiple Locations** - Ship to different practice locations
- **Bulk Ordering** - Order large quantities with volume considerations

#### Checkout & Payment

- **Secure Payment Processing** - Credit card processing via CardPointe
- **Saved Payment Methods** - Store multiple payment methods securely
- **Tax Calculations** - Automatic tax calculation based on location
- **Shipping Options** - Multiple shipping methods with real-time rates

#### Order Management

- **Order Tracking** - Real-time order status updates
- **Shipping Tracking** - Integration with FedEx and UPS tracking
- **Order History** - Complete order history with reorder functionality
- **Recurring Order Management** - Pause, modify, or cancel recurring orders

#### Account Management

- **Multi-Location Support** - Manage multiple practice locations
- **User Management** - Add/remove staff users with role-based permissions
- **Billing Management** - Multiple billing addresses and payment methods
- **Settings & Preferences** - Customize notification preferences and display options

### For Account Administrators

#### User & Permission Management

- **Role Assignment** - Assign roles and permissions to staff
- **Custom Permissions** - Override default permissions for specific needs
- **User Activity** - Monitor user activity and order history

#### Financial Management

- **Invoice Management** - View, pay, and track all invoices
- **Payment History** - Complete payment and transaction history
- **Tax Exemption** - Manage tax-exempt status and certificates
- **Credit Terms** - Manage payment terms and credit arrangements

#### Reporting & Analytics

- **Order Reports** - Detailed order history and analytics
- **Spending Analysis** - Track spending patterns and trends
- **Product Usage** - Monitor product consumption and reorder patterns

## 🔧 Development

### Code Structure

The application follows Next.js conventions with additional organization:

- **Pages** - Route-based pages using Next.js file-based routing
- **API Routes** - RESTful API endpoints in `/pages/api/`
- **Components** - Reusable React components organized by feature
- **Contexts** - React Context providers for global state management
- **Prisma** - Database schema and ORM configuration

### Key Technologies & Patterns

- **Server-Side Rendering** - Next.js SSR for improved performance
- **API-First Design** - Separate API layer for frontend/backend communication
- **Context Pattern** - React Context for state management
- **Component Composition** - Modular, reusable component architecture
- **Type Safety** - Prisma provides type-safe database operations

### Environment Configuration

The application supports different environments through environment variables:

- **Development** - Local development with hot reloading
- **Production** - Optimized build for production deployment
- **Maintenance Mode** - System-wide maintenance mode capability

### Database Migrations

Use Prisma for database schema management:

```bash
# Create a new migration
npx prisma migrate dev --name migration_name

# Apply migrations to production
npx prisma migrate deploy

# Reset database (development only)
npx prisma migrate reset
```

## 🚀 Deployment

### Production Deployment

1. **Build the application**

   ```bash
   npm run build
   ```

2. **Set production environment variables**
   - Configure production database URL
   - Set secure NEXTAUTH_SECRET
   - Configure payment processor credentials

3. **Deploy to hosting platform**
   - Vercel (recommended for Next.js)
   - AWS, Google Cloud, or other cloud providers
   - Traditional hosting with Node.js support

### Maintenance Mode

Enable maintenance mode by setting:

```bash
NEXT_PUBLIC_MAINTENANCE_MODE="true"
```

This will redirect all users to a maintenance page while preserving static assets.

## 📞 Support & Contact

For technical support or questions about OASIS v2:

- **OASIS Medical Inc.** - Primary support contact
- **System Administrators** - For technical issues
- **Account Representatives** - For business-related inquiries

## 📄 License

This project is proprietary software owned by OASIS Medical Inc. All rights reserved.

---

*Documentation by MEDL Mobile*
