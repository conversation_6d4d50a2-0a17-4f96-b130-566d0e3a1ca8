{"name": "oasis-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@prisma/client": "^4.6.1", "@types/node": "18.11.9", "@types/react": "18.0.25", "@types/react-dom": "18.0.9", "axios": "^1.3.4", "bcrypt": "^5.1.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "eslint": "8.27.0", "eslint-config-next": "13.0.3", "flowbite": "^1.7.0", "flowbite-react": "^0.4.7", "formidable": "^3.5.1", "luxon": "^3.4.4", "next": "^13.4.19", "next-auth": "^4.17.0", "react": "18.2.0", "react-datepicker": "^4.16.0", "react-dom": "18.2.0", "react-select": "^5.7.0", "swr": "^2.1.2"}, "devDependencies": {"autoprefixer": "^10.4.14", "postcss": "^8.4.24", "postcss-nesting": "^12.0.1", "prisma": "^4.6.1", "tailwindcss": "^3.3.2"}}