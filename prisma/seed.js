const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcrypt')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create Partner Types
  const partnerType = await prisma.partnerType.create({
    data: {
      code: 'MEDICAL',
      name: 'Medical Practice',
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Oasis Rep
  const oasisRep = await prisma.oasisRep.create({
    data: {
      name: '<PERSON>',
      phone: '************',
      email: '<EMAIL>',
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create User Group
  const userGroup = await prisma.group.create({
    data: {
      name: 'Primary Users',
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Account
  const account = await prisma.account.create({
    data: {
      jde_id: 'TEST001',
      account_num: 'ACC001',
      business_name: 'Demo Medical Practice',
      website: 'https://demo-medical.com',
      address: '123 Medical Drive',
      city: 'Healthcare City',
      state: 'CA',
      zip: '90210',
      oasis_rep_id: oasisRep.id,
      partner_type: partnerType.id,
      approved: 1,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Location
  const location = await prisma.locations.create({
    data: {
      account_id: account.id,
      practice: 'Main Office',
      address: '123 Medical Drive',
      city: 'Healthcare City',
      state: 'CA',
      zip: '90210',
      phone: '************',
      partner_type: partnerType.id,
      default: 1,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create User with hashed password
  const hashedPassword = await bcrypt.hash('password123', 10)
  const user = await prisma.user.create({
    data: {
      first_name: 'Demo',
      last_name: 'User',
      email: '<EMAIL>',
      password: hashedPassword,
      position: 'Practice Manager',
      phone: '************',
      group_id: userGroup.id,
      account_id: account.id,
      location_id: location.id,
      disabled: 0,
      primary_user: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Categories
  const category1 = await prisma.category.create({
    data: {
      name: 'Surgical Instruments',
      parent_id: null,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  const category2 = await prisma.category.create({
    data: {
      name: 'Medical Supplies',
      parent_id: null,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Products
  const product1 = await prisma.product.create({
    data: {
      sku: 'SURG001',
      name: 'Surgical Scalpel Set',
      description: 'Professional surgical scalpel set with multiple blade sizes',
      price: 89.99,
      stock: 50,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  const product2 = await prisma.product.create({
    data: {
      sku: 'SUPP001',
      name: 'Medical Gauze Pads',
      description: 'Sterile gauze pads for wound care and medical procedures',
      price: 24.99,
      stock: 100,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  const product3 = await prisma.product.create({
    data: {
      sku: 'SURG002',
      name: 'Surgical Forceps',
      description: 'High-quality stainless steel surgical forceps',
      price: 45.50,
      stock: 25,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Link Products to Categories
  await prisma.productCategory.create({
    data: {
      product_id: product1.id,
      category_id: category1.id,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  await prisma.productCategory.create({
    data: {
      product_id: product2.id,
      category_id: category2.id,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  await prisma.productCategory.create({
    data: {
      product_id: product3.id,
      category_id: category1.id,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Product Images
  await prisma.productImage.create({
    data: {
      product_id: product1.id,
      path: '/oasis-product-images/scalpel-set.jpg',
      order: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  await prisma.productImage.create({
    data: {
      product_id: product2.id,
      path: '/oasis-product-images/gauze-pads.jpg',
      order: 1,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Training Materials
  await prisma.trainingMaterials.create({
    data: {
      title: 'Surgical Instrument Care',
      video_path: '/training/surgical-care.mp4',
      image_path: '/training/surgical-care-thumb.jpg',
      order: 1,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  await prisma.trainingMaterials.create({
    data: {
      title: 'Wound Care Best Practices',
      video_path: '/training/wound-care.mp4',
      image_path: '/training/wound-care-thumb.jpg',
      order: 2,
      disabled: 0,
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  // Create Billing Information
  await prisma.billing.create({
    data: {
      user_id: user.id,
      account_id: account.id,
      company: 'Demo Medical Practice',
      address: '123 Medical Drive',
      city: 'Healthcare City',
      state: 'CA',
      postal_code: '90210',
      phone: '************',
      created_at: new Date(),
      updated_at: new Date(),
    },
  })

  console.log('✅ Database seeded successfully!')
  console.log('📧 Demo login: <EMAIL>')
  console.log('🔑 Demo password: password123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
