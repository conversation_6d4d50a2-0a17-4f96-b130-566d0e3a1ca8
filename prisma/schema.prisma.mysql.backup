generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["fullTextSearch", "fullTextIndex"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  id                    Int                           @id @default(autoincrement())
  jde_id                String                        @default("") @db.VarChar(100)
  account_num           String                        @default("") @db.VarChar(100)
  business_name         String                        @default("") @db.VarChar(100)
  website               String                        @default("") @db.VarChar(100)
  fax                   String                        @default("") @db.VarChar(100)
  address               String                        @default("") @db.VarChar(100)
  address_l2            String                        @default("") @db.VarChar(100)
  city                  String                        @default("") @db.VarChar(100)
  state                 String                        @default("") @db.VarChar(100)
  zip                   String                        @default("") @db.VarChar(100)
  oasis_rep_id          Int?
  territory_code        String?                       @db.VarChar(100)
  hear_of_oasis         String                        @default("") @db.Var<PERSON>har(100)
  email_marketing_on    Int                           @default(0) @db.TinyInt
  partner_type          Int?                          @default(20)
  invoice_preferred_by  Account_invoice_preferred_by  @default(EMPTY_ENUM_VALUE)
  tax_exempt_or_resale  Int                           @default(0) @db.TinyInt
  exempt_resale_cert    String                        @default("") @db.VarChar(100)
  exempt_resale_number  String                        @default("") @db.VarChar(100)
  pref_freight_delivery Account_pref_freight_delivery @default(EMPTY_ENUM_VALUE)
  freight_company       Account_freight_company       @default(EMPTY_ENUM_VALUE)
  shipping_acct_num     String                        @default("") @db.VarChar(100)
  private_practice      Int                           @default(0) @db.TinyInt
  num_locations         Int                           @default(1)
  primary_specialty     String                        @default("") @db.VarChar(100)
  secondary_specialty   String                        @default("") @db.VarChar(100)
  treat_dry_eye         Int                           @default(0) @db.TinyInt
  punctal_plugs         Int                           @default(0) @db.TinyInt
  terms                 Int                           @default(0) @db.TinyInt
  approved              Int                           @default(0) @db.TinyInt
  restored              Int                           @default(0) @db.TinyInt
  disabled              Int                           @default(0) @db.TinyInt
  created_at            DateTime?                     @db.DateTime(0)
  updated_at            DateTime?                     @db.DateTime(0)
  PartnerType           PartnerType?                  @relation(fields: [partner_type], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "account_ibfk_1")
  OasisRep              OasisRep?                     @relation(fields: [oasis_rep_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "account_ibfk_2")
  Territories           Territories?                  @relation(fields: [territory_code], references: [code], onDelete: Restrict, onUpdate: Restrict, map: "account_ibfk_3")
  Billing               Billing[]
  Cart                  Cart[]
  Contact               Contact[]
  Locations             Locations[]
  Order                 Order[]
  Payment               Payment[]
  User                  User[]
  AccountCustomPermissions AccountCustomPermission[]


  @@index([territory_code], map: "account_ibfk_3")
  @@index([oasis_rep_id], map: "oasis_rep_id")
  @@index([partner_type], map: "partner_type")
}

model AdminGroup {
  id         Int         @id @default(autoincrement())
  name       String?     @db.VarChar(50)
  created_at DateTime?   @db.DateTime(0)
  updated_at DateTime?   @db.DateTime(0)
  AdminUser  AdminUser[]
}

model AdminUser {
  id         Int         @id @default(autoincrement())
  first_name String?     @db.VarChar(50)
  last_name  String?     @db.VarChar(50)
  email      String?     @db.VarChar(100)
  username   String?     @db.VarChar(50)
  password   String?     @db.VarChar(100)
  group_id   Int?
  disabled   Int?        @default(0) @db.TinyInt
  created_at DateTime?   @db.DateTime(0)
  updated_at DateTime?   @db.DateTime(0)
  AdminGroup AdminGroup? @relation(fields: [group_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "adminuser_ibfk_1")
  Session    Session[]

  @@index([group_id], map: "group_id")
}

model Billing {
  id          Int       @id @default(autoincrement()) @db.UnsignedInt
  user_id     Int
  account_id  Int?
  company     String?   @db.VarChar(30)
  website     String?   @db.VarChar(100)
  address     String?   @db.VarChar(25)
  address_2   String?   @db.VarChar(25)
  city        String?   @db.VarChar(25)
  state       String?   @db.Char(2)
  country     String?   @db.Char(2)
  postal_code String?   @db.VarChar(15)
  phone       String?   @db.VarChar(25)
  created_at  DateTime? @db.DateTime(0)
  updated_at  DateTime? @db.DateTime(0)
  User        User      @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "billing_ibfk_1")
  Account     Account?  @relation(fields: [account_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "billing_ibfk_2")
  Order       Order[]
  Payment     Payment[]

  @@index([user_id], map: "user_id")
  @@index([account_id], map: "billing_ibfk_2_idx")
}

model Cart {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  account_id Int
  user_id    Int?
  product_id Int
  qty        Int
  price      Decimal? @db.Decimal(10, 2)
  Account    Account  @relation(fields: [account_id], references: [id], onUpdate: Restrict, map: "cart_ibfk_2")
  Product    Product  @relation(fields: [product_id], references: [id], onUpdate: Restrict, map: "cart_ibfk_3")
  User       User?    @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "cart_ibfk_4")

  @@index([account_id], map: "account_id")
  @@index([product_id], map: "product_id")
  @@index([user_id], map: "user_id")
}

model Category {
  id              Int               @id @default(autoincrement())
  name            String?           @db.VarChar(150)
  parent_id       Int?
  disabled        Int?              @db.TinyInt
  created_at      DateTime?         @db.DateTime(0)
  updated_at      DateTime?         @db.DateTime(0)
  CategoryFilter  CategoryFilter[]
  ProductCategory ProductCategory[]
}

model CategoryFilter {
  id          Int       @id @default(autoincrement())
  category_id Int?
  filter_id   Int?
  created_at  DateTime? @db.DateTime(0)
  updated_at  DateTime? @db.DateTime(0)
  Filter      Filter?   @relation(fields: [filter_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "cat_filter_id")
  Category    Category? @relation(fields: [category_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "categoryfilter_ibfk_1")

  @@unique([category_id, filter_id], map: "cat_filter_pair")
  @@index([filter_id], map: "cat_filter_id")
  @@index([category_id], map: "category_id")
}

model Contact {
  id         Int       @id @default(autoincrement())
  account_id Int?
  user_id    Int?
  contact_id Int?
  type       String?   @db.VarChar(50)
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  Account    Account?  @relation(fields: [account_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "Contact_ibfk_1")
  User       User?     @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "User_id")

  @@index([user_id], map: "User_id")
  @@index([account_id], map: "account_id")
}

model Filter {
  id              Int               @id @default(autoincrement())
  name            String?           @db.VarChar(150)
  disabled        Int?              @default(0) @db.TinyInt
  created_at      DateTime?         @db.DateTime(0)
  updated_at      DateTime?         @db.DateTime(0)
  CategoryFilter  CategoryFilter[]
  FilterAttribute FilterAttribute[]
}

model FilterAttribute {
  id               Int                @id @default(autoincrement())
  name             String?            @db.VarChar(150)
  filter_id        Int?
  disabled         Int?               @default(0) @db.TinyInt
  created_at       DateTime?          @db.DateTime(0)
  updated_at       DateTime?          @db.DateTime(0)
  Filter           Filter?            @relation(fields: [filter_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "filter_id")
  ProductAttribute ProductAttribute[]

  @@index([filter_id], map: "filter_id")
}

model Group {
  id         Int       @id @default(autoincrement())
  name       String?   @db.VarChar(100)
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  User       User[]
  RoleDefaultPermissions RoleDefaultPermission[]
  AccountCustomPermissions AccountCustomPermission[]
}

model Invoice {
  id                Int              @id @default(autoincrement()) @db.UnsignedInt
  order_id          Int              @db.UnsignedInt
  invoice_num       String?          @db.VarChar(25)
  tracking          String?          @db.VarChar(50)
  type              Invoice_type?
  status            Int?
  due_date          DateTime?        @db.DateTime(0)
  amount_due        Decimal?         @db.Decimal(10, 2)
  ordered_price     Decimal?         @db.Decimal(10, 2)
  shipped_price     Decimal?         @db.Decimal(10, 2)
  backordered_price Decimal?         @db.Decimal(10, 2)
  created_at        DateTime?        @db.DateTime(0)
  updated_at        DateTime?        @db.DateTime(0)
  Order             Order            @relation(fields: [order_id], references: [id], onUpdate: Restrict, map: "invoice_ibfk_1")
  OrderedProduct    OrderedProduct[]
  ReturnForm        ReturnForm[]

  @@index([order_id], map: "order_id")
}

model Locations {
  id           Int         @id @default(autoincrement())
  account_id   Int
  jde_id       String      @default("") @db.VarChar(100)
  practice     String      @default("") @db.VarChar(100)
  nickname     String      @default("") @db.VarChar(25)
  website      String      @default("") @db.VarChar(100)
  address      String      @default("") @db.VarChar(100)
  address_2    String      @default("") @db.VarChar(50)
  city         String      @default("") @db.VarChar(50)
  state        String      @default("") @db.VarChar(50)
  country      String      @default("") @db.VarChar(50)
  zip          String      @default("") @db.VarChar(20)
  phone        String      @default("") @db.VarChar(25)
  type         String      @default("") @db.VarChar(50)
  partner_type Int         @default(20)
  default      Int         @default(0)
  disabled     Int         @default(0)
  created_at   DateTime?   @db.DateTime(0)
  updated_at   DateTime?   @db.DateTime(0)
  PartnerType  PartnerType @relation(fields: [partner_type], references: [id], onUpdate: Restrict, map: "locations_ibfk_1")
  Account      Account     @relation(fields: [account_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "locations_ibfk_2")
  Order        Order[]
  Payment      Payment[]
  User         User[]

  @@index([account_id], map: "account_id")
  @@index([partner_type], map: "partner_type")
}

model Notifications {
  id                Int                  @id @default(autoincrement())
  headline          String?              @db.VarChar(100)
  body              String?              @db.LongText
  image_path        String?              @db.VarChar(100)
  group             Notifications_group? @default(all)
  group_filter      Int?
  sent              Int?                 @db.TinyInt
  date_sent         DateTime?            @db.DateTime(0)
  created_at        DateTime?            @db.DateTime(0)
  updated_at        DateTime?            @db.DateTime(0)
  UserNotifications UserNotifications[]
}

model OasisRep {
  id          Int           @id @default(autoincrement())
  name        String        @default("") @db.VarChar(100)
  phone       String        @default("") @db.VarChar(100)
  email       String        @db.VarChar(100)
  disabled    Int           @default(0) @db.TinyInt
  created_at  DateTime      @db.DateTime(0)
  updated_at  DateTime      @db.DateTime(0)
  Account     Account[]
  Territories Territories[]
}

model Order {
  id                     Int              @id @default(autoincrement()) @db.UnsignedInt
  order_by               Int
  account_id             Int
  date                   DateTime?        @db.DateTime(0)
  jde_id                 String           @default("") @db.VarChar(50)
  transaction_id         Int?
  authcode               String?          @db.VarChar(25)
  retref                 String?          @db.VarChar(25)
  authorized_amount      Decimal?         @db.Decimal(10, 2)
  billing_id             Int?             @db.UnsignedInt
  tax                    Decimal?         @db.Decimal(10, 2)
  shipping               Decimal?         @db.Decimal(10, 2)
  total                  Decimal?         @db.Decimal(10, 2)
  discount               Decimal?         @db.Decimal(10, 2)
  tracking               String?          @db.VarChar(50)
  status                 Int?
  shipping_method        String?          @db.VarChar(25)
  shipping_address_id    Int?
  payment_id             Int?
  auto_delivery          Int              @default(0) @db.TinyInt
  auto_freq              Int              @default(0) @db.TinyInt
  cancel_auto            Int              @default(0) @db.TinyInt
  pause_auto             Int              @default(0) @db.TinyInt
  auto_start_date        DateTime?        @db.Date
  auto_pause_date        DateTime?        @db.DateTime(0)
  auto_cancel_date       DateTime?        @db.DateTime(0)
  type                   Order_type?
  recurred               Int              @default(0) @db.TinyInt
  order_placed_by        String?          @db.VarChar(50)
  po_num                 String?          @db.VarChar(25)
  surgeon_names          String?          @db.VarChar(50)
  require_rep_consulting Int?
  parent_id              Int              @default(0)
  parent_jde_id          String?          @default("") @db.VarChar(50)
  restored               Int              @default(0) @db.TinyInt
  created_at             DateTime?        @db.DateTime(0)
  updated_at             DateTime?        @db.DateTime(0)
  Invoice                Invoice[]
  Billing                Billing?         @relation(fields: [billing_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "order_ibfk_1")
  User                   User             @relation(fields: [order_by], references: [id], onUpdate: Restrict, map: "order_ibfk_2")
  Account                Account          @relation(fields: [account_id], references: [id], onUpdate: Restrict, map: "order_ibfk_3")
  Payment                Payment?         @relation(fields: [payment_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "order_ibfk_4")
  Locations              Locations?       @relation(fields: [shipping_address_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "order_ibfk_5")
  OrderedProduct         OrderedProduct[]

  @@index([account_id], map: "account_id")
  @@index([billing_id], map: "billing")
  @@index([payment_id], map: "payment_id")
  @@index([shipping_address_id], map: "shipping_address_id")
  @@index([order_by], map: "user_id")
}

model OrderedProduct {
  id               Int      @id @default(autoincrement()) @db.UnsignedInt
  order_id         Int?     @db.UnsignedInt
  invoice_id       Int?     @db.UnsignedInt
  product_id       Int?
  qty              Int      @default(1)
  price            Decimal? @db.Decimal(10, 2)
  last_status_code String?  @db.VarChar(11)
  next_status_code String?  @db.VarChar(11)
  Order            Order?   @relation(fields: [order_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "orderedproduct_ibfk_1")
  Product          Product? @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "orderedproduct_ibfk_2")
  Invoice          Invoice? @relation(fields: [invoice_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "orderedproduct_ibfk_3")

  @@index([order_id], map: "order")
  @@index([product_id], map: "product_id")
  @@index([invoice_id], map: "invoice")
}

model PartnerType {
  id         Int         @id @default(autoincrement())
  code       String      @db.VarChar(100)
  name       String      @default("") @db.VarChar(100)
  disabled   Int         @default(0) @db.TinyInt
  deleted    Int         @default(0) @db.TinyInt
  created_at DateTime    @db.DateTime(0)
  updated_at DateTime    @db.DateTime(0)
  Account    Account[]
  Locations  Locations[]
}

model Payment {
  id          Int        @id @default(autoincrement())
  added_by    Int
  account_id  Int?
  nickname    String?    @db.VarChar(25)
  type        String?    @db.VarChar(10)
  last_four   String?    @db.VarChar(4)
  expiry      String?    @db.VarChar(4)
  token       String?    @db.VarChar(100)
  profileid   String?    @db.VarChar(100)
  default     Int?
  location_id Int?
  billing_id  Int?       @db.UnsignedInt
  deleted     Int        @default(0) @db.TinyInt
  created_at  DateTime?  @db.DateTime(0)
  updated_at  DateTime?  @db.DateTime(0)
  Order       Order[]
  Billing     Billing?   @relation(fields: [billing_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "payment_ibfk_2")
  Locations   Locations? @relation(fields: [location_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "payment_ibfk_3")
  User        User       @relation(fields: [added_by], references: [id], onUpdate: Restrict, map: "payment_ibfk_4")
  Account     Account?   @relation(fields: [account_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "payment_ibfk_5")

  @@index([billing_id], map: "billing")
  @@index([location_id], map: "location")
  @@index([added_by], map: "user_id")
  @@index([account_id], map: "payment_ibfk_5_idx")
}

model Product {
  id               Int                @id @default(autoincrement())
  jde_id           String?            @db.VarChar(50)
  sku              String?            @db.VarChar(50)
  name             String?            @db.VarChar(150)
  description      String?            @db.LongText
  details          String?            @db.LongText
  videos           String?            @db.LongText
  resources        String?            @db.LongText
  price            Decimal?           @db.Decimal(10, 2)
  weight_lbs       Decimal?           @db.Decimal(10, 2)
  stock            Int?
  disabled         Int?               @db.TinyInt
  created_at       DateTime?          @db.DateTime(0)
  updated_at       DateTime?          @db.DateTime(0)
  Cart             Cart[]
  OrderedProduct   OrderedProduct[]
  ProductAttribute ProductAttribute[]
  ProductCategory  ProductCategory[]
  ProductImage     ProductImage[]
  ProductResource  ProductResource[]
  ProductVideo     ProductVideo[]
  PromoProducts    PromotionProduct[]
  ReturnProducts   ReturnProducts[]

  @@fulltext([sku, name, description])
}

model ProductAttribute {
  id              Int              @id @default(autoincrement())
  product_id      Int?
  attribute_id    Int?
  created_at      DateTime?        @db.DateTime(0)
  updated_at      DateTime?        @db.DateTime(0)
  Product         Product?         @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "att_product_id")
  FilterAttribute FilterAttribute? @relation(fields: [attribute_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "prod_attribute_id")

  @@index([product_id], map: "att_product_id")
  @@index([attribute_id], map: "prod_attribute_id")
}

model ProductCategory {
  id          Int       @id @default(autoincrement())
  product_id  Int?
  category_id Int?
  created_at  DateTime? @db.DateTime(0)
  updated_at  DateTime? @db.DateTime(0)
  Product     Product?  @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "cat_product_id")
  Category    Category? @relation(fields: [category_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "productcategory_ibfk_1")

  @@index([product_id], map: "cat_product_id")
  @@index([category_id], map: "prod_category_id")
}

model ProductImage {
  id         Int       @id @default(autoincrement())
  product_id Int?
  path       String?   @db.VarChar(150)
  order      Int?
  extension  String?   @db.VarChar(10)
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  Product    Product?  @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "im_product_id")

  @@index([product_id], map: "im_product_id")
}

model ProductResource {
  id          Int       @id @default(autoincrement())
  product_id  Int?
  title       String?   @db.VarChar(150)
  description String?   @db.VarChar(250)
  path        String?   @db.VarChar(150)
  order       Int?
  created_at  DateTime? @db.DateTime(0)
  updated_at  DateTime? @db.DateTime(0)
  Product     Product?  @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "res_product_id")

  @@index([product_id], map: "res_product_id")
}

model ProductVideo {
  id         Int       @id @default(autoincrement())
  product_id Int?
  path       String?   @db.VarChar(150)
  order      Int?
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  Product    Product?  @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "vid_product_id")

  @@index([product_id], map: "vid_product_id")
}

model ReturnForm {
  id             Int               @id @default(autoincrement()) @db.UnsignedInt
  invoice_id     Int?              @db.UnsignedInt
  rma_num        Int?
  status         Int?
  admin_status   Int?
  reason         ReturnForm_reason @default(EMPTY_ENUM_VALUE)
  reason_notes   String?           @db.VarChar(255)
  created_at     DateTime?         @db.DateTime(0)
  updated_at     DateTime?         @db.DateTime(0)
  Invoice        Invoice?          @relation(fields: [invoice_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "returnform_ibfk_1")
  ReturnProducts ReturnProducts[]

  @@index([invoice_id], map: "invoice_id")
}

model ReturnProducts {
  id         Int         @id @default(autoincrement()) @db.UnsignedInt
  return_id  Int?        @db.UnsignedInt
  product_id Int?
  qty        Int?
  Product    Product?    @relation(fields: [product_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "returnproducts_ibfk_2")
  ReturnForm ReturnForm? @relation(fields: [return_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "returnproducts_ibfk_3")

  @@index([return_id], map: "return_id")
  @@index([product_id], map: "returnproducts_ibfk_2")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model Session {
  id           Int       @id @default(autoincrement())
  user_type_id Int       @db.TinyInt
  user_id      Int
  token        String    @unique(map: "unq_session") @db.VarChar(255)
  expiration   Int
  User         AdminUser @relation(fields: [user_id], references: [id], onUpdate: Restrict, map: "session_ibfk_1")

  @@index([user_id], map: "session_ibfk_1_idx")
}

model Setting {
  id                 Int                   @id @default(autoincrement())
  user_id            Int?                  @unique(map: "userID")
  invoice_view       Setting_invoice_view? @default(all)
  invoice_sort       Setting_invoice_sort? @default(latest)
  inv_new_email      Int?                  @default(1) @db.TinyInt
  inv_new_badge      Int?                  @default(0) @db.TinyInt
  inv_new_sms        Int?                  @default(0) @db.TinyInt
  inv_recieved_email Int?                  @default(1) @db.TinyInt
  inv_recieved_badge Int?                  @default(0) @db.TinyInt
  inv_recieved_sms   Int?                  @default(0) @db.TinyInt
  inv_pastdue_email  Int?                  @default(1) @db.TinyInt
  inv_pastdue_badge  Int?                  @default(0) @db.TinyInt
  inv_pastdue_sms    Int?                  @default(0) @db.TinyInt
  order_view         Setting_order_view?   @default(all)
  order_sort         Setting_order_sort?   @default(latest)
  ord_new_email      Int?                  @default(1) @db.TinyInt
  ord_new_badge      Int?                  @default(0) @db.TinyInt
  ord_new_sms        Int?                  @default(0) @db.TinyInt
  ord_recieved_email Int?                  @default(1) @db.TinyInt
  ord_recieved_badge Int?                  @default(0) @db.TinyInt
  ord_recieved_sms   Int?                  @default(0) @db.TinyInt
  ord_shipped_email  Int?                  @default(1) @db.TinyInt
  ord_shipped_badge  Int?                  @default(0) @db.TinyInt
  ord_shipped_sms    Int?                  @default(0) @db.TinyInt
  created_at         DateTime?             @db.DateTime(0)
  updated_at         DateTime?             @db.DateTime(0)
  User               User?                 @relation(fields: [user_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "Setting_ibfk_1")
}

model Territories {
  id         Int       @id @default(autoincrement())
  code       String?   @unique(map: "code_UNIQUE") @db.VarChar(150)
  rep_id     Int?
  disabled   Int?      @default(0) @db.TinyInt
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
  Account    Account[]
  OasisRep   OasisRep? @relation(fields: [rep_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "territories_ibfk_1")

  @@index([code], map: "code")
  @@index([rep_id], map: "filter_id")
}

model TrainingCategories {
  id                Int                 @id @default(autoincrement())
  title             String?             @db.VarChar(50)
  order             Int?
  disabled          Int?                @default(0) @db.TinyInt
  created_at        DateTime?           @db.DateTime(0)
  updated_at        DateTime?           @db.DateTime(0)
  TrainingMaterials TrainingMaterials[]
}

model TrainingMaterials {
  id                 Int                 @id @default(autoincrement())
  category_id        Int?
  title              String?             @db.VarChar(100)
  video_path         String?             @db.VarChar(100)
  image_path         String?             @db.VarChar(100)
  order              Int?
  disabled           Int?                @default(0) @db.TinyInt
  created_at         DateTime?           @db.DateTime(0)
  updated_at         DateTime?           @db.DateTime(0)
  TrainingCategories TrainingCategories? @relation(fields: [category_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "trainingmaterials_ibfk_1")

  @@index([category_id], map: "category_id")
}

model User {
  id                Int                 @id @default(autoincrement())
  first_name        String              @default("") @db.VarChar(100)
  last_name         String              @default("") @db.VarChar(100)
  jde_id            String?             @db.VarChar(100)
  email             String              @unique(map: "email") @default("") @db.VarChar(100)
  password          String?             @db.VarChar(100)
  position          String              @default("") @db.VarChar(100)
  phone             String              @default("") @db.VarChar(100)
  ext               String?             @default("") @db.VarChar(100)
  mobile_type       User_mobile_type?
  fax               String?             @db.VarChar(100)
  address           String?             @db.VarChar(150)
  address_l2        String?             @db.VarChar(100)
  city              String?             @db.VarChar(100)
  state             String?             @db.VarChar(100)
  zip               String?             @db.VarChar(100)
  group_id          Int?
  account_id        Int?
  location_id       Int?
  disabled          Int?                @default(0) @db.TinyInt
  primary_user      Int?                @default(0) @db.TinyInt
  last_login        DateTime?           @db.DateTime(0)
  created_at        DateTime?           @db.DateTime(0)
  updated_at        DateTime?           @db.DateTime(0)
  Billing           Billing[]
  Cart              Cart[]
  Contact           Contact[]
  Order             Order[]
  Payment           Payment[]
  Setting           Setting?
  Account           Account?            @relation(fields: [account_id], references: [id], onDelete: Cascade, onUpdate: Restrict, map: "User_ibfk_1")
  Group             Group?              @relation(fields: [group_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "User_ibfk_2")
  Locations         Locations?          @relation(fields: [location_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "location_id")
  UserNotifications UserNotifications[]

  @@index([account_id], map: "account_id")
  @@index([group_id], map: "group_id")
  @@index([location_id], map: "location_id")
}

model Webapp {
  webapp_id Int    @id @default(autoincrement())
  api_key   String @db.VarChar(255)
}

model account_recovery {
  account_recovery Int    @id @default(autoincrement())
  user_id          Int?
  user_type_id     Int    @default(1) @db.SmallInt
  token            String @unique(map: "unq_account_recovery") @db.VarChar(255)
  expiration       Int
}

model Files {
  id         Int       @id @default(autoincrement()) @db.UnsignedInt
  path       String?   @db.VarChar(150)
  for        String?   @db.VarChar(50)
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
}

model UserNotifications {
  id            Int            @id @default(autoincrement()) @db.UnsignedInt
  user_id       Int?
  notif_id      Int?
  open          Int?           @default(0) @db.TinyInt
  created_at    DateTime?      @db.DateTime(0)
  updated_at    DateTime?      @db.DateTime(0)
  User          User?          @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "user_notif_ibfk_1")
  Notifications Notifications? @relation(fields: [notif_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "user_notif_ibfk_2")

  @@index([notif_id], map: "notif_id")
  @@index([user_id], map: "user_id")
}

model GlobalMessages {
  id         Int       @id @default(autoincrement())
  title      String?   @db.VarChar(100)
  body       String?   @db.LongText
  on         Int?      @db.TinyInt
  created_at DateTime? @db.DateTime(0)
  updated_at DateTime? @db.DateTime(0)
}

model Promotion {
  id             Int                  @id @default(autoincrement())
  title          String
  description    String
  promotion_type PromotionType
  discount_type  DiscountType
  discount_value Decimal?             @db.Decimal(10, 2)
  active         Int
  created_at     DateTime?            @db.DateTime(0)
  updated_at     DateTime?            @db.DateTime(0)
  Conditions     PromotionCondition[]
}

model PromotionCondition {
  id                 Int                @id @default(autoincrement())
  promotion_id       Int
  type               ConditionType
  threshold          Int?
  value              Decimal?           @db.Decimal(10, 2)
  coupon_code        String?
  applicableShipping String?
  promotion          Promotion          @relation(fields: [promotion_id], references: [id], onDelete: Cascade, onUpdate: Restrict)
  applicableProducts PromotionProduct[]

  @@index([promotion_id], map: "PromotionCondition_promotion_id_fkey")
}

model PromotionProduct {
  id            Int                @id @default(autoincrement())
  product_id    Int
  promo_cond_id Int
  Condition     PromotionCondition @relation(fields: [promo_cond_id], references: [id], onDelete: Cascade, onUpdate: Restrict)
  Product       Product            @relation(fields: [product_id], references: [id], onUpdate: Restrict, map: "promoproducts_ibfk_1")

  @@index([promo_cond_id], map: "PromotionProduct_promo_cond_id_fkey")
  @@index([product_id], map: "promoproducts_ibfk_1")
}

model Permission {
  id         Int      @id @default(autoincrement())
  section    String   @db.VarChar(100) // e.g. "Dashboard", "Checkout"
  action     String   @db.VarChar(50)  // e.g. "view", "add", "edit", "approve", "submit", "pay"
  created_at DateTime? @default(now())
  updated_at DateTime? @db.DateTime(0)

  RoleDefaultPermission RoleDefaultPermission[]
  AccountCustomPermission AccountCustomPermission[]
}

model RoleDefaultPermission {
  id            Int        @id @default(autoincrement())
  group_id      Int
  permission_id Int

  Group      Group      @relation(fields: [group_id], references: [id], onDelete: Cascade)
  Permission Permission @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@unique([group_id, permission_id])
}

model AccountCustomPermission {
  id            Int        @id @default(autoincrement())
  account_id    Int
  group_id      Int
  permission_id Int
  allowed       Boolean    @default(false)

  Account    Account     @relation(fields: [account_id], references: [id])
  Group      Group       @relation(fields: [group_id], references: [id], onDelete: Cascade)
  Permission Permission  @relation(fields: [permission_id], references: [id], onDelete: Cascade)

  @@unique([account_id, group_id, permission_id])
}

enum ConditionType {
  product_quantity
  subtotal
  shipping
  coupon
  auto_delivery
}

enum DiscountType {
  percentage
  fixed_discount
  fixed_price
}

enum PromotionType {
  product_specific
  order_based
  shipping
  bundle
}

enum Invoice_type {
  web
  phone
  EMPTY_ENUM_VALUE @map("")
}

enum Setting_invoice_sort {
  EMPTY_ENUM_VALUE @map("")
  latest
  unpaid
}

enum User_mobile_type {
  Mobile
  Work
  EMPTY_ENUM_VALUE @map("")
}

enum Setting_order_sort {
  EMPTY_ENUM_VALUE @map("")
  latest
  unpaid
}

enum Account_invoice_preferred_by {
  EMPTY_ENUM_VALUE @map("")
  email
  mail
}

enum Account_freight_company {
  EMPTY_ENUM_VALUE @map("")
  fedex
  ups
}

enum Setting_invoice_view {
  EMPTY_ENUM_VALUE @map("")
  all
  paid
}

enum ReturnForm_reason {
  EMPTY_ENUM_VALUE @map("")
  damaged
  extra
  other
}

enum Setting_order_view {
  EMPTY_ENUM_VALUE @map("")
  all
  paid
}

enum Notifications_group {
  EMPTY_ENUM_VALUE @map("")
  all
  partner_type
  category
}

enum Order_type {
  web
  phone
  EMPTY_ENUM_VALUE @map("")
}

enum Account_pref_freight_delivery {
  EMPTY_ENUM_VALUE @map("")
  ground
  two_day
  three_day
}
