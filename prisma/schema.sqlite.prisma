generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Simplified schema for local development
model Account {
  id                       Int                       @id @default(autoincrement())
  jde_id                   String                    @default("")
  account_num              String                    @default("")
  business_name            String                    @default("")
  website                  String                    @default("")
  address                  String                    @default("")
  city                     String                    @default("")
  state                    String                    @default("")
  zip                      String                    @default("")
  oasis_rep_id             Int?
  partner_type             Int?                      @default(20)
  approved                 Int                       @default(1)
  disabled                 Int                       @default(0)
  created_at               DateTime?
  updated_at               DateTime?
  PartnerType              PartnerType?              @relation(fields: [partner_type], references: [id])
  OasisRep                 OasisRep?                 @relation(fields: [oasis_rep_id], references: [id])
  Billing                  Billing[]
  Cart                     Cart[]
  Locations                Locations[]
  Order                    Order[]
  Payment                  Payment[]
  User                     User[]
}

model User {
  id                Int                 @id @default(autoincrement())
  first_name        String              @default("")
  last_name         String              @default("")
  email             String              @unique @default("")
  password          String?
  position          String              @default("")
  phone             String              @default("")
  group_id          Int?
  account_id        Int?
  location_id       Int?
  disabled          Int?                @default(0)
  primary_user      Int?                @default(0)
  last_login        DateTime?
  created_at        DateTime?
  updated_at        DateTime?
  Billing           Billing[]
  Cart              Cart[]
  Order             Order[]
  Payment           Payment[]
  Account           Account?            @relation(fields: [account_id], references: [id])
  Group             Group?              @relation(fields: [group_id], references: [id])
  Locations         Locations?          @relation(fields: [location_id], references: [id])
}

model Group {
  id         Int       @id @default(autoincrement())
  name       String?
  created_at DateTime?
  updated_at DateTime?
  User       User[]
}

model PartnerType {
  id         Int         @id @default(autoincrement())
  code       String
  name       String      @default("")
  disabled   Int         @default(0)
  created_at DateTime?
  updated_at DateTime?
  Account    Account[]
  Locations  Locations[]
}

model OasisRep {
  id          Int           @id @default(autoincrement())
  name        String        @default("")
  phone       String        @default("")
  email       String
  disabled    Int           @default(0)
  created_at  DateTime?
  updated_at  DateTime?
  Account     Account[]
}

model Locations {
  id           Int         @id @default(autoincrement())
  account_id   Int
  practice     String      @default("")
  address      String      @default("")
  city         String      @default("")
  state        String      @default("")
  zip          String      @default("")
  phone        String      @default("")
  partner_type Int         @default(20)
  default      Int         @default(0)
  disabled     Int         @default(0)
  created_at   DateTime?
  updated_at   DateTime?
  PartnerType  PartnerType @relation(fields: [partner_type], references: [id])
  Account      Account     @relation(fields: [account_id], references: [id])
  Order        Order[]
  Payment      Payment[]
  User         User[]
}

model Product {
  id               Int                @id @default(autoincrement())
  sku              String?
  name             String?
  description      String?
  price            Float?
  stock            Int?
  disabled         Int?
  created_at       DateTime?
  updated_at       DateTime?
  Cart             Cart[]
  OrderedProduct   OrderedProduct[]
  ProductCategory  ProductCategory[]
  ProductImage     ProductImage[]
}

model Category {
  id              Int               @id @default(autoincrement())
  name            String?
  parent_id       Int?
  disabled        Int?
  created_at      DateTime?
  updated_at      DateTime?
  ProductCategory ProductCategory[]
}

model ProductCategory {
  id          Int       @id @default(autoincrement())
  product_id  Int?
  category_id Int?
  created_at  DateTime?
  updated_at  DateTime?
  Product     Product?  @relation(fields: [product_id], references: [id])
  Category    Category? @relation(fields: [category_id], references: [id])
}

model ProductImage {
  id         Int       @id @default(autoincrement())
  product_id Int?
  path       String?
  order      Int?
  created_at DateTime?
  updated_at DateTime?
  Product    Product?  @relation(fields: [product_id], references: [id])
}

model Cart {
  id         Int      @id @default(autoincrement())
  account_id Int
  user_id    Int?
  product_id Int
  qty        Int
  price      Float?
  Account    Account  @relation(fields: [account_id], references: [id])
  Product    Product  @relation(fields: [product_id], references: [id])
  User       User?    @relation(fields: [user_id], references: [id])
}

model Order {
  id                     Int              @id @default(autoincrement())
  order_by               Int
  account_id             Int
  date                   DateTime?
  jde_id                 String           @default("")
  authcode               String?
  retref                 String?
  authorized_amount      Float?
  billing_id             Int?
  tax                    Float?
  shipping               Float?
  total                  Float?
  discount               Float?
  tracking               String?
  status                 Int?
  shipping_method        String?
  shipping_address_id    Int?
  payment_id             Int?
  auto_delivery          Int              @default(0)
  created_at             DateTime?
  updated_at             DateTime?
  Invoice                Invoice[]
  Billing                Billing?         @relation(fields: [billing_id], references: [id])
  User                   User             @relation(fields: [order_by], references: [id])
  Account                Account          @relation(fields: [account_id], references: [id])
  Payment                Payment?         @relation(fields: [payment_id], references: [id])
  Locations              Locations?       @relation(fields: [shipping_address_id], references: [id])
  OrderedProduct         OrderedProduct[]
}

model OrderedProduct {
  id               Int      @id @default(autoincrement())
  order_id         Int?
  invoice_id       Int?
  product_id       Int?
  qty              Int      @default(1)
  price            Float?
  Order            Order?   @relation(fields: [order_id], references: [id])
  Product          Product? @relation(fields: [product_id], references: [id])
  Invoice          Invoice? @relation(fields: [invoice_id], references: [id])
}

model Invoice {
  id                Int              @id @default(autoincrement())
  order_id          Int
  invoice_num       String?
  tracking          String?
  status            Int?
  due_date          DateTime?
  amount_due        Float?
  created_at        DateTime?
  updated_at        DateTime?
  Order             Order            @relation(fields: [order_id], references: [id])
  OrderedProduct    OrderedProduct[]
}

model Billing {
  id          Int       @id @default(autoincrement())
  user_id     Int
  account_id  Int?
  company     String?
  address     String?
  city        String?
  state       String?
  postal_code String?
  phone       String?
  created_at  DateTime?
  updated_at  DateTime?
  User        User      @relation(fields: [user_id], references: [id])
  Account     Account?  @relation(fields: [account_id], references: [id])
  Order       Order[]
  Payment     Payment[]
}

model Payment {
  id          Int        @id @default(autoincrement())
  added_by    Int
  account_id  Int?
  nickname    String?
  type        String?
  last_four   String?
  expiry      String?
  token       String?
  default     Int?
  location_id Int?
  billing_id  Int?
  deleted     Int        @default(0)
  created_at  DateTime?
  updated_at  DateTime?
  Order       Order[]
  Billing     Billing?   @relation(fields: [billing_id], references: [id])
  Locations   Locations? @relation(fields: [location_id], references: [id])
  User        User       @relation(fields: [added_by], references: [id])
  Account     Account?   @relation(fields: [account_id], references: [id])
}

model TrainingMaterials {
  id                 Int                 @id @default(autoincrement())
  title              String?
  video_path         String?
  image_path         String?
  order              Int?
  disabled           Int?                @default(0)
  created_at         DateTime?
  updated_at         DateTime?
}
