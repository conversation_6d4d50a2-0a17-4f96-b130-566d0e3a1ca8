import { NextResponse,NextRequest } from "next/server";

export function middleware(req) {
    const maintenanceMode = process.env.NEXT_PUBLIC_MAINTENANCE_MODE === "true";
    const excludedPaths = ['/_next', '/public', '/favicon.ico', '/maintenance','/oasis_logo.png','/<EMAIL>']; // Add other static paths as needed

    if (maintenanceMode && !excludedPaths.some(path => req.nextUrl.pathname.startsWith(path))) {
      return NextResponse.rewrite(new URL('/maintenance', req.url));
    }
    return NextResponse.next();
  }
  
  export const config = {
    matcher: ['/((?!api|_next/static|_next/image|_next|public|favicon.ico).*)'],
  };